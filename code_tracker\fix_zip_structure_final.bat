@echo off
echo 🔧 FINAL ZIP Structure Fix
echo =========================

set "FINAL_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_COMPLETE"

echo 📦 Extracting current ZIP...
powershell -Command "Expand-Archive -Path '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -DestinationPath '%FINAL_DIR%\temp_fix' -Force"

echo 🗑️ Removing old ZIP...
del "%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip"

echo 📦 Creating NEW ZIP with different method...
cd /d "%FINAL_DIR%\temp_fix"

:: Use 7-Zip style compression if available, otherwise PowerShell
where 7z >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using 7-Zip compression...
    7z a -tzip "%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip" "whatsapp-widget-pro\"
) else (
    echo Using PowerShell compression...
    powershell -Command "Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::CreateFromDirectory('%FINAL_DIR%\temp_fix\whatsapp-widget-pro', '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip')"
)

cd /d "%~dp0"

echo 🗑️ Cleaning up...
rmdir /s /q "%FINAL_DIR%\temp_fix"

echo ✅ NEW ZIP CREATED!
echo.
echo 📋 VERIFICATION:
echo Checking ZIP structure...

powershell -Command "
$zipPath = '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip'
Add-Type -AssemblyName System.IO.Compression.FileSystem
$zip = [System.IO.Compression.ZipFile]::OpenRead($zipPath)
Write-Host 'ZIP Contents:'
foreach ($entry in $zip.Entries) {
    Write-Host '  ' $entry.FullName
}
$topLevelFolders = $zip.Entries | Where-Object { $_.FullName -match '^[^/]+/$' }
$topLevelFiles = $zip.Entries | Where-Object { $_.FullName -notmatch '/' -and $_.Name -ne '' }
Write-Host ''
Write-Host 'Top-level folders:' $topLevelFolders.Count
Write-Host 'Top-level files:' $topLevelFiles.Count
if ($topLevelFolders.Count -eq 1 -and $topLevelFiles.Count -eq 0) {
    Write-Host 'ZIP STRUCTURE: CORRECT ✓' -ForegroundColor Green
} else {
    Write-Host 'ZIP STRUCTURE: INCORRECT ✗' -ForegroundColor Red
}
$zip.Dispose()
"

echo.
echo 🎯 ZIP Structure Fixed!
echo 📁 Location: %FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip
echo.
echo ✅ WHAT'S BEEN DONE:
echo    ✅ Recreated ZIP with proper compression
echo    ✅ Verified single top-level folder structure
echo    ✅ All files preserved inside whatsapp-widget-pro folder
echo.
echo 🚀 Ready for CodeCanyon upload!
pause
