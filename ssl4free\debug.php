<?php
echo "<h1>SSL4Free Debug Information</h1>";
echo "<pre>";

echo "=== File Check ===\n";
$configFile = __DIR__ . '/api/config.php';
echo "Config file exists: " . (file_exists($configFile) ? "YES" : "NO") . "\n";
echo "Config file path: $configFile\n";

if (file_exists($configFile)) {
    echo "\n=== Config File Content ===\n";
    $content = file_get_contents($configFile);
    
    // Show first 20 lines
    $lines = explode("\n", $content);
    for ($i = 0; $i < min(20, count($lines)); $i++) {
        echo sprintf("%2d: %s\n", $i + 1, $lines[$i]);
    }
    
    echo "\n=== API Key Check ===\n";
    if (preg_match("/define\('ZEROSSL_API_KEY',\s*'([^']+)'\);/", $content, $matches)) {
        $apiKey = $matches[1];
        echo "API Key found: " . substr($apiKey, 0, 10) . "...\n";
        echo "API Key length: " . strlen($apiKey) . "\n";
        echo "Is default key: " . ($apiKey === 'YOUR_ZEROSSL_API_KEY_HERE' ? "YES" : "NO") . "\n";
    } else {
        echo "API Key NOT FOUND in config!\n";
    }
    
    echo "\n=== EAB Check ===\n";
    if (preg_match("/define\('ZEROSSL_EAB_KID',\s*'([^']+)'\);/", $content, $matches)) {
        echo "EAB KID found: " . substr($matches[1], 0, 10) . "...\n";
    } else {
        echo "EAB KID not found\n";
    }
    
    echo "\n=== Include Test ===\n";
    try {
        require_once $configFile;
        echo "Config file included successfully\n";
        echo "ZEROSSL_API_KEY constant: " . (defined('ZEROSSL_API_KEY') ? "DEFINED" : "NOT DEFINED") . "\n";
        if (defined('ZEROSSL_API_KEY')) {
            echo "ZEROSSL_API_KEY value: " . substr(ZEROSSL_API_KEY, 0, 10) . "...\n";
        }
        echo "ZEROSSL_EAB_KID constant: " . (defined('ZEROSSL_EAB_KID') ? "DEFINED" : "NOT DEFINED") . "\n";
        
        echo "\n=== Function Test ===\n";
        if (function_exists('checkZeroSSLApiKey')) {
            echo "checkZeroSSLApiKey function exists\n";
            $result = checkZeroSSLApiKey();
            echo "API Key validation result: " . ($result ? "VALID" : "INVALID") . "\n";
        } else {
            echo "checkZeroSSLApiKey function NOT FOUND\n";
        }
        
    } catch (Exception $e) {
        echo "Error including config: " . $e->getMessage() . "\n";
    }
}

echo "\n=== Directory Check ===\n";
echo "Current directory: " . __DIR__ . "\n";
echo "API directory exists: " . (is_dir(__DIR__ . '/api') ? "YES" : "NO") . "\n";

if (is_dir(__DIR__ . '/api')) {
    echo "API directory contents:\n";
    $files = scandir(__DIR__ . '/api');
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            echo "  - $file\n";
        }
    }
}

echo "\n=== PHP Info ===\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Current time: " . date('Y-m-d H:i:s') . "\n";

echo "</pre>";
?>
