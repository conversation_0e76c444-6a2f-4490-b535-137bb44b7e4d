/**
 * Advanced Data Parser and Formatter for SIM Database Scraping
 * Extracts and formats SIM owner details, CNIC info, and related data
 * Educational purpose only
 */

class SIMDataParser {
    constructor() {
        this.patterns = {
            // Name patterns
            name: [
                /(?:Owner|Name|نام)[:\s]*([A-Za-z\s]{3,50})/i,
                /<(?:span|div|td)[^>]*class="[^"]*(?:owner|name)[^"]*"[^>]*>([^<]+)</i,
                /Owner Name[:\s]*<[^>]*>([^<]+)</i,
                /Name[:\s]*([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/
            ],
            
            // CNIC patterns
            cnic: [
                /CNIC[:\s]*(\d{5}-\d{7}-\d{1})/i,
                /CNIC[:\s]*(\d{13})/i,
                /شناختی[:\s]*(\d{5}-\d{7}-\d{1})/i,
                /<(?:span|div|td)[^>]*class="[^"]*cnic[^"]*"[^>]*>([^<]+)</i
            ],
            
            // Address patterns
            address: [
                /(?:Address|پتہ)[:\s]*([^<\n]{10,100})/i,
                /<(?:span|div|td)[^>]*class="[^"]*address[^"]*"[^>]*>([^<]+)</i,
                /Address[:\s]*<[^>]*>([^<]+)</i
            ],
            
            // Network patterns
            network: [
                /(?:Network|Operator|نیٹ ورک)[:\s]*(Jazz|Telenor|Zong|Ufone|Warid)/i,
                /<(?:span|div|td)[^>]*class="[^"]*(?:network|operator)[^"]*"[^>]*>([^<]+)</i
            ],
            
            // Mobile number patterns
            mobile: [
                /(?:Mobile|Number|نمبر)[:\s]*(\+?92\d{10})/i,
                /(?:Mobile|Number)[:\s]*(03\d{9})/i,
                /<(?:span|div|td)[^>]*class="[^"]*(?:mobile|number)[^"]*"[^>]*>([^<]+)</i
            ],
            
            // Status patterns
            status: [
                /(?:Status|حالت)[:\s]*(Active|Inactive|Blocked)/i,
                /<(?:span|div|td)[^>]*class="[^"]*status[^"]*"[^>]*>([^<]+)</i
            ],
            
            // Type patterns (Prepaid/Postpaid)
            type: [
                /(?:Type|قسم)[:\s]*(Prepaid|Postpaid)/i,
                /<(?:span|div|td)[^>]*class="[^"]*type[^"]*"[^>]*>([^<]+)</i
            ],
            
            // Registration date patterns
            date: [
                /(?:Registration|Date|تاریخ)[:\s]*(\d{4}-\d{2}-\d{2})/i,
                /(?:Registration|Date)[:\s]*(\d{2}\/\d{2}\/\d{4})/i,
                /<(?:span|div|td)[^>]*class="[^"]*date[^"]*"[^>]*>([^<]+)</i
            ]
        };

        this.cleaningRules = {
            name: (value) => this.cleanName(value),
            cnic: (value) => this.cleanCNIC(value),
            address: (value) => this.cleanAddress(value),
            network: (value) => this.cleanNetwork(value),
            mobile: (value) => this.cleanMobile(value),
            status: (value) => this.cleanStatus(value),
            type: (value) => this.cleanType(value),
            date: (value) => this.cleanDate(value)
        };
    }

    // Main parsing function
    parseHTML(html, searchNumber = null) {
        console.log('🔍 Starting HTML parsing for SIM data');
        
        if (!html || typeof html !== 'string') {
            return {
                success: false,
                error: 'Invalid HTML content',
                data: null
            };
        }

        const extractedData = {};
        
        // Extract data using patterns
        for (const [field, patterns] of Object.entries(this.patterns)) {
            const value = this.extractField(html, patterns, field);
            if (value) {
                extractedData[field] = value;
            }
        }

        // Try table-based extraction
        const tableData = this.extractFromTables(html);
        Object.assign(extractedData, tableData);

        // Try structured data extraction
        const structuredData = this.extractStructuredData(html);
        Object.assign(extractedData, structuredData);

        // Clean and validate extracted data
        const cleanedData = this.cleanExtractedData(extractedData);

        // Add search number if provided
        if (searchNumber) {
            cleanedData.searchNumber = searchNumber;
        }

        // Validate if we have meaningful data
        if (this.hasValidData(cleanedData)) {
            return {
                success: true,
                data: this.formatFinalData(cleanedData),
                extractionMethod: 'HTML Pattern Matching'
            };
        }

        return {
            success: false,
            error: 'No valid SIM data found in HTML',
            data: cleanedData
        };
    }

    // Extract field using multiple patterns
    extractField(html, patterns, fieldName) {
        for (const pattern of patterns) {
            try {
                const match = html.match(pattern);
                if (match && match[1] && match[1].trim()) {
                    const value = match[1].trim();
                    console.log(`✅ Found ${fieldName}: ${value}`);
                    return value;
                }
            } catch (error) {
                console.log(`❌ Pattern error for ${fieldName}:`, error.message);
                continue;
            }
        }
        return null;
    }

    // Extract data from HTML tables
    extractFromTables(html) {
        const tableData = {};
        
        try {
            // Create a temporary DOM element
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const tables = doc.querySelectorAll('table');

            for (const table of tables) {
                const rows = table.querySelectorAll('tr');
                
                for (const row of rows) {
                    const cells = row.querySelectorAll('td, th');
                    
                    if (cells.length >= 2) {
                        const label = cells[0].textContent.trim().toLowerCase();
                        const value = cells[1].textContent.trim();

                        if (value && value.length > 0) {
                            if (label.includes('owner') || label.includes('name')) {
                                tableData.name = value;
                            } else if (label.includes('cnic')) {
                                tableData.cnic = value;
                            } else if (label.includes('address')) {
                                tableData.address = value;
                            } else if (label.includes('network') || label.includes('operator')) {
                                tableData.network = value;
                            } else if (label.includes('mobile') || label.includes('number')) {
                                tableData.mobile = value;
                            } else if (label.includes('status')) {
                                tableData.status = value;
                            } else if (label.includes('type')) {
                                tableData.type = value;
                            } else if (label.includes('date') || label.includes('registration')) {
                                tableData.date = value;
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.log('Table extraction error:', error.message);
        }

        return tableData;
    }

    // Extract structured data (JSON-LD, microdata, etc.)
    extractStructuredData(html) {
        const structuredData = {};
        
        try {
            // Look for JSON-LD data
            const jsonLdMatch = html.match(/<script[^>]*type="application\/ld\+json"[^>]*>(.*?)<\/script>/is);
            if (jsonLdMatch) {
                try {
                    const jsonData = JSON.parse(jsonLdMatch[1]);
                    if (jsonData.owner) structuredData.name = jsonData.owner;
                    if (jsonData.cnic) structuredData.cnic = jsonData.cnic;
                    if (jsonData.address) structuredData.address = jsonData.address;
                } catch (jsonError) {
                    console.log('JSON-LD parsing error:', jsonError.message);
                }
            }

            // Look for data attributes
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            
            const dataElements = doc.querySelectorAll('[data-owner], [data-cnic], [data-address], [data-network]');
            for (const element of dataElements) {
                if (element.dataset.owner) structuredData.name = element.dataset.owner;
                if (element.dataset.cnic) structuredData.cnic = element.dataset.cnic;
                if (element.dataset.address) structuredData.address = element.dataset.address;
                if (element.dataset.network) structuredData.network = element.dataset.network;
            }

        } catch (error) {
            console.log('Structured data extraction error:', error.message);
        }

        return structuredData;
    }

    // Clean extracted data
    cleanExtractedData(data) {
        const cleaned = {};
        
        for (const [field, value] of Object.entries(data)) {
            if (value && this.cleaningRules[field]) {
                cleaned[field] = this.cleaningRules[field](value);
            } else if (value) {
                cleaned[field] = value.toString().trim();
            }
        }

        return cleaned;
    }

    // Data cleaning functions
    cleanName(name) {
        if (!name) return null;
        
        // Remove HTML tags and extra spaces
        const cleaned = name.replace(/<[^>]*>/g, '').trim();
        
        // Validate name format (only letters and spaces)
        if (/^[A-Za-z\s]{2,50}$/.test(cleaned)) {
            return cleaned.replace(/\s+/g, ' ');
        }
        
        return null;
    }

    cleanCNIC(cnic) {
        if (!cnic) return null;
        
        // Remove non-digits and format
        const digits = cnic.replace(/\D/g, '');
        
        if (digits.length === 13) {
            return `${digits.substring(0, 5)}-${digits.substring(5, 12)}-${digits.substring(12)}`;
        }
        
        return null;
    }

    cleanAddress(address) {
        if (!address) return null;
        
        const cleaned = address.replace(/<[^>]*>/g, '').trim();
        
        if (cleaned.length >= 10 && cleaned.length <= 200) {
            return cleaned;
        }
        
        return null;
    }

    cleanNetwork(network) {
        if (!network) return null;
        
        const cleaned = network.toLowerCase().trim();
        
        if (cleaned.includes('jazz') || cleaned.includes('mobilink')) return 'Jazz';
        if (cleaned.includes('telenor')) return 'Telenor';
        if (cleaned.includes('zong')) return 'Zong';
        if (cleaned.includes('ufone')) return 'Ufone';
        if (cleaned.includes('warid')) return 'Warid';
        
        return network.trim();
    }

    cleanMobile(mobile) {
        if (!mobile) return null;
        
        const digits = mobile.replace(/\D/g, '');
        
        if (digits.startsWith('92') && digits.length === 12) {
            return '0' + digits.substring(2);
        }
        
        if (digits.startsWith('03') && digits.length === 11) {
            return digits;
        }
        
        return null;
    }

    cleanStatus(status) {
        if (!status) return null;
        
        const cleaned = status.toLowerCase().trim();
        
        if (cleaned.includes('active')) return 'Active';
        if (cleaned.includes('inactive')) return 'Inactive';
        if (cleaned.includes('blocked')) return 'Blocked';
        
        return status.trim();
    }

    cleanType(type) {
        if (!type) return null;
        
        const cleaned = type.toLowerCase().trim();
        
        if (cleaned.includes('prepaid')) return 'Prepaid';
        if (cleaned.includes('postpaid')) return 'Postpaid';
        
        return type.trim();
    }

    cleanDate(date) {
        if (!date) return null;
        
        // Try to parse different date formats
        const datePatterns = [
            /(\d{4}-\d{2}-\d{2})/,
            /(\d{2}\/\d{2}\/\d{4})/,
            /(\d{2}-\d{2}-\d{4})/
        ];

        for (const pattern of datePatterns) {
            const match = date.match(pattern);
            if (match) {
                try {
                    const parsedDate = new Date(match[1]);
                    if (!isNaN(parsedDate.getTime())) {
                        return parsedDate.toISOString().split('T')[0];
                    }
                } catch (error) {
                    continue;
                }
            }
        }

        return null;
    }

    // Check if extracted data is valid
    hasValidData(data) {
        return data.name || data.cnic || (data.mobile && data.network);
    }

    // Format final data structure
    formatFinalData(data) {
        return {
            mobile: data.mobile || data.searchNumber || 'N/A',
            owner: data.name || 'N/A',
            cnic: data.cnic || 'N/A',
            address: data.address || 'N/A',
            network: data.network || 'Unknown',
            status: data.status || 'Unknown',
            type: data.type || 'Unknown',
            registrationDate: data.date || 'N/A',
            source: 'Parsed from HTML',
            timestamp: new Date().toISOString(),
            confidence: this.calculateConfidence(data)
        };
    }

    // Calculate confidence score based on available data
    calculateConfidence(data) {
        let score = 0;
        const weights = {
            name: 30,
            cnic: 25,
            address: 20,
            network: 10,
            mobile: 10,
            status: 3,
            type: 2
        };

        for (const [field, weight] of Object.entries(weights)) {
            if (data[field] && data[field] !== 'N/A' && data[field] !== 'Unknown') {
                score += weight;
            }
        }

        if (score >= 80) return 'High';
        if (score >= 50) return 'Medium';
        if (score >= 20) return 'Low';
        return 'Very Low';
    }
}

// Export the parser
window.SIMDataParser = new SIMDataParser();

console.log('🔍 SIM Data Parser loaded - Educational use only');
