<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Details - OLX Marketplace</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .product-detail-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 40px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .product-images {
            margin-bottom: 30px;
        }
        
        .main-image {
            margin-bottom: 15px;
        }
        
        .main-image img {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
        }
        
        .image-gallery img {
            width: 100%;
            height: 80px;
            object-fit: cover;
            border-radius: 4px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.3s;
        }
        
        .image-gallery img:hover,
        .image-gallery img.active {
            border-color: #23e5db;
        }
        
        .product-description {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .product-info-card {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .product-price {
            font-size: 28px;
            font-weight: bold;
            color: #002f34;
            margin-bottom: 20px;
        }
        
        .product-meta {
            margin-bottom: 20px;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: #666;
        }
        
        .meta-item i {
            margin-right: 8px;
            color: #002f34;
        }
        
        .action-buttons {
            margin-bottom: 20px;
        }
        
        .btn-primary, .btn-secondary {
            width: 100%;
            margin-bottom: 10px;
            padding: 12px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            transition: background 0.3s;
        }
        
        .btn-primary {
            background: #002f34;
            color: white;
        }
        
        .btn-primary:hover {
            background: #001a1d;
        }
        
        .btn-secondary {
            background: #fff;
            color: #002f34;
            border: 2px solid #002f34;
        }
        
        .btn-secondary:hover {
            background: #f8f9fa;
        }
        
        .contact-info-card {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
        }
        
        .contact-item {
            margin-bottom: 15px;
        }
        
        .safety-tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .safety-tips h4 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .safety-tips ul {
            color: #856404;
            font-size: 14px;
            margin: 0;
            padding-left: 20px;
        }
        
        .related-products {
            margin-top: 40px;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .related-product {
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s;
        }
        
        .related-product:hover {
            transform: translateY(-5px);
        }
        
        .related-image {
            height: 150px;
            overflow: hidden;
        }
        
        .related-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .related-info {
            padding: 15px;
        }
        
        .related-price {
            font-weight: bold;
            color: #002f34;
            margin-bottom: 5px;
        }
        
        .related-title {
            font-size: 14px;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .product-detail-container {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px 10px;
            }
            
            .product-sidebar {
                order: -1;
            }
            
            .main-image img {
                height: 250px;
            }
            
            .related-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-wrapper">
                <div class="logo">
                    <h1><a href="index.html" style="text-decoration: none; color: inherit;"><i class="fas fa-store"></i> OLX Marketplace</a></h1>
                </div>
                
                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-box">
                        <input type="text" id="searchInput" placeholder="What are you looking for?">
                        <select id="locationSelect">
                            <option value="">All Pakistan</option>
                            <option value="karachi">Karachi</option>
                            <option value="lahore">Lahore</option>
                            <option value="islamabad">Islamabad</option>
                            <option value="rawalpindi">Rawalpindi</option>
                            <option value="faisalabad">Faisalabad</option>
                        </select>
                        <button class="search-btn"><i class="fas fa-search"></i></button>
                    </div>
                </div>
                
                <!-- Navigation -->
                <nav class="nav-menu">
                    <a href="#" class="nav-link" id="favoritesLink"><i class="fas fa-heart"></i> Favorites</a>
                    <a href="#" class="nav-link" id="loginLink"><i class="fas fa-user"></i> Login</a>
                    <a href="#" class="sell-btn" id="sellBtn"><i class="fas fa-plus"></i> SELL</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Breadcrumb -->
    <div class="breadcrumb-container" style="background: #f8f9fa; padding: 15px 0; border-bottom: 1px solid #eee;">
        <div class="container">
            <nav class="breadcrumb" style="font-size: 14px; color: #666;">
                <a href="index.html" style="color: #002f34; text-decoration: none;">Home</a>
                <span style="margin: 0 8px;">/</span>
                <span id="breadcrumbCategory">Products</span>
                <span style="margin: 0 8px;">/</span>
                <span id="breadcrumbTitle">Product Details</span>
            </nav>
        </div>
    </div>

    <!-- Product Details Container -->
    <div class="container">
        <div class="product-detail-container" id="productDetailContainer">
            <!-- Content will be loaded here -->
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>OLX Marketplace</h3>
                    <p>Pakistan's largest marketplace for buying and selling everything.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Popular Categories</h4>
                    <ul>
                        <li><a href="#">Cars</a></li>
                        <li><a href="#">Mobiles</a></li>
                        <li><a href="#">Houses for Sale</a></li>
                        <li><a href="#">Jobs</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Popular Cities</h4>
                    <ul>
                        <li><a href="#">Karachi</a></li>
                        <li><a href="#">Lahore</a></li>
                        <li><a href="#">Islamabad</a></li>
                        <li><a href="#">Rawalpindi</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Help & Support</h4>
                    <ul>
                        <li><a href="#">Help</a></li>
                        <li><a href="#">Sitemap</a></li>
                        <li><a href="#">Legal & Privacy</a></li>
                        <li><a href="#">Contact Us</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 OLX Marketplace. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/product-details.js"></script>
</body>
</html>
