/* WhatsApp Widget Pro - Admin Styles */

.whatsapp-admin-container {
    max-width: 1200px;
    margin: 20px 0;
}

.whatsapp-admin-header {
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
    padding: 30px;
    border-radius: 12px 12px 0 0;
    margin-bottom: 0;
}

.whatsapp-admin-header h1 {
    margin: 0;
    font-size: 28px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.whatsapp-admin-header p {
    margin: 8px 0 0 0;
    opacity: 0.9;
    font-size: 16px;
}

.whatsapp-admin-tabs {
    background: white;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    margin: 0;
    padding: 0;
}

.whatsapp-admin-tab {
    background: none;
    border: none;
    padding: 16px 24px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.whatsapp-admin-tab:hover {
    color: #25D366;
    background: #f8f9fa;
}

.whatsapp-admin-tab.active {
    color: #25D366;
    border-bottom-color: #25D366;
    background: #f8f9fa;
}

.whatsapp-admin-content {
    background: white;
    padding: 30px;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.whatsapp-tab-content {
    display: none;
}

.whatsapp-tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Form Styles */
.whatsapp-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.whatsapp-form-section {
    background: #f8f9fa;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.whatsapp-form-section h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.whatsapp-form-group {
    margin-bottom: 20px;
}

.whatsapp-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.whatsapp-form-group input,
.whatsapp-form-group select,
.whatsapp-form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.whatsapp-form-group input:focus,
.whatsapp-form-group select:focus,
.whatsapp-form-group textarea:focus {
    outline: none;
    border-color: #25D366;
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
}

.whatsapp-form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Toggle Switch */
.whatsapp-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.whatsapp-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.whatsapp-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.whatsapp-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}
.whatsapp-toggle input:checked + .whatsapp-toggle-slider {
    background-color: #25D366;
}

.whatsapp-toggle input:checked + .whatsapp-toggle-slider:before {
    transform: translateX(26px);
}

/* Preview Section */
.whatsapp-preview-section {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    text-align: center;
}

.whatsapp-preview-container {
    position: relative;
    height: 300px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    overflow: hidden;
    margin: 20px 0;
}

.whatsapp-widget {
    position: absolute !important;
    opacity: 1 !important;
    transform: scale(1) !important;
    animation: none !important;
}

.whatsapp-widget a {
    pointer-events: none;
}

/* Agents Management */
.whatsapp-agents-list {
    margin-top: 20px;
}

.whatsapp-agent-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    position: relative;
}

.whatsapp-agent-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.whatsapp-agent-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
}

.whatsapp-agent-remove {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.whatsapp-agent-remove:hover {
    background: #c82333;
}

.whatsapp-add-agent {
    background: #25D366;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    margin-top: 16px;
}

.whatsapp-add-agent:hover {
    background: #128C7E;
}

/* Working Hours */
.whatsapp-working-hours {
    display: grid;
    gap: 16px;
}

.whatsapp-day-schedule {
    display: grid;
    grid-template-columns: 100px 1fr 100px 100px 60px;
    gap: 12px;
    align-items: center;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.whatsapp-day-name {
    font-weight: 500;
    color: #333;
}

/* Save Button */
.whatsapp-save-button {
    background: #25D366;
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 30px;
}

.whatsapp-save-button:hover {
    background: #128C7E;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

/* Success Message */
.whatsapp-success-message {
    background: #d4edda;
    color: #155724;
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid #c3e6cb;
    margin-bottom: 20px;
    display: none;
}

/* Responsive */
@media (max-width: 768px) {
    .whatsapp-form-grid {
        grid-template-columns: 1fr;
    }

    .whatsapp-admin-tabs {
        flex-wrap: wrap;
    }

    .whatsapp-admin-tab {
        flex: 1;
        min-width: 120px;
    }

    .whatsapp-day-schedule {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .whatsapp-admin-header {
        padding: 20px;
    }

    .whatsapp-admin-content {
        padding: 20px;
    }
}