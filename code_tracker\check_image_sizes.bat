@echo off
echo 🖼️ WhatsApp Widget Screenshots Size Checker
echo ==========================================

set "SCREENSHOT_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete\WhatsApp_Widget_Screenshots"

echo 📁 Checking folder: %SCREENSHOT_DIR%
echo.

if exist "%SCREENSHOT_DIR%" (
    echo 📸 Screenshot files and sizes:
    echo.
    
    cd /d "%SCREENSHOT_DIR%"
    
    for %%f in (*.png) do (
        echo 📸 %%f
        for %%s in ("%%f") do (
            set /a sizeKB=%%~zs/1024
            echo    Size: !sizeKB! KB
            if !sizeKB! GTR 1024 (
                echo    Status: ❌ TOO LARGE ^(^>1MB^)
            ) else if !sizeKB! GTR 800 (
                echo    Status: ⚠️ LARGE ^(^>800KB^)
            ) else (
                echo    Status: ✅ OK
            )
        )
        echo.
    )
    
    echo 🎯 CodeCanyon Requirements:
    echo    • Maximum: 1 MB per image
    echo    • Recommended: 500-800 KB
    echo    • Minimum: 6 screenshots
    echo    • Format: PNG or JPG
    echo.
    
    echo 💡 If any images are too large, consider:
    echo    • Using online compression tools
    echo    • Reducing image quality slightly
    echo    • Converting to JPG format
    
) else (
    echo ❌ Screenshots folder not found!
    echo Please check the path: %SCREENSHOT_DIR%
)

echo.
echo Press any key to continue...
pause >nul
