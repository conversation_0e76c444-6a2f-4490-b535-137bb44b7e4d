<!DOCTYPE html>
<html>
<head>
    <title>SSL4Free - Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>SSL4Free - Simple API Test</h1>
    
    <?php
    require_once 'api/config.php';
    
    echo "<h2>Configuration Check</h2>";
    echo "<p><strong>API Key:</strong> " . ZEROSSL_API_KEY . "</p>";
    echo "<p><strong>API URL:</strong> " . ZEROSSL_API_URL . "</p>";
    
    // Test 1: API Connection
    echo "<h2>Test 1: API Connection</h2>";
    $url = ZEROSSL_API_URL . '/certificates?access_key=' . ZEROSSL_API_KEY . '&limit=1';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p class='error'>❌ cURL Error: $error</p>";
    } else {
        echo "<p class='success'>✅ cURL Success (HTTP $httpCode)</p>";
    }
    
    echo "<h3>API Response:</h3>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    $apiData = json_decode($response, true);
    
    if (isset($apiData['error'])) {
        echo "<p class='error'>❌ API Error: " . json_encode($apiData['error']) . "</p>";
        
        // Check specific error types
        if (isset($apiData['error']['code'])) {
            $errorCode = $apiData['error']['code'];
            $errorType = $apiData['error']['type'] ?? 'unknown';
            
            echo "<h3>Error Analysis:</h3>";
            echo "<p><strong>Error Code:</strong> $errorCode</p>";
            echo "<p><strong>Error Type:</strong> $errorType</p>";
            
            if ($errorCode == 2817) {
                echo "<p class='error'>🚫 Certificate limit reached for this API key</p>";
                echo "<p class='info'>💡 Solution: Create a new ZeroSSL account or wait 24 hours</p>";
            } elseif ($errorCode == 401) {
                echo "<p class='error'>🔑 Invalid API key</p>";
                echo "<p class='info'>💡 Solution: Check your API key in ZeroSSL dashboard</p>";
            } else {
                echo "<p class='info'>💡 Unknown error - check ZeroSSL documentation</p>";
            }
        }
    } else {
        echo "<p class='success'>✅ API is working correctly!</p>";
        
        if (isset($apiData['total_count'])) {
            echo "<p class='info'>📊 Total certificates: " . $apiData['total_count'] . "</p>";
        }
    }
    
    // Test 2: CSR Generation
    echo "<h2>Test 2: CSR Generation</h2>";
    
    try {
        $config = [
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ];
        
        $privateKey = @openssl_pkey_new($config);
        
        if ($privateKey) {
            echo "<p class='success'>✅ Private key generated</p>";
            
            $dn = [
                'C' => 'US',
                'ST' => 'CA',
                'L' => 'SF',
                'O' => 'SSL4Free',
                'CN' => 'test.example.com'
            ];
            
            $csr = @openssl_csr_new($dn, $privateKey, $config);
            
            if ($csr && @openssl_csr_export($csr, $csrString)) {
                echo "<p class='success'>✅ CSR generated successfully</p>";
                echo "<p><strong>CSR Length:</strong> " . strlen($csrString) . " characters</p>";
                
                // Test 3: Certificate Creation (if API is working)
                if (!isset($apiData['error'])) {
                    echo "<h2>Test 3: Certificate Creation</h2>";
                    
                    $requestData = [
                        'certificate_domains' => 'test.example.com',
                        'certificate_validity_days' => 90,
                        'certificate_csr' => $csrString
                    ];
                    
                    $postData = http_build_query($requestData);
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, ZEROSSL_API_URL . '/certificates?access_key=' . ZEROSSL_API_KEY);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Content-Type: application/x-www-form-urlencoded'
                    ]);
                    
                    $response = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
                    echo "<h3>Certificate Creation Response:</h3>";
                    echo "<pre>" . htmlspecialchars($response) . "</pre>";
                    
                    $certData = json_decode($response, true);
                    if (isset($certData['id'])) {
                        echo "<p class='success'>🎉 Certificate created successfully!</p>";
                        echo "<p><strong>Certificate ID:</strong> " . $certData['id'] . "</p>";
                    } else {
                        echo "<p class='error'>❌ Certificate creation failed</p>";
                        if (isset($certData['error'])) {
                            echo "<p><strong>Error:</strong> " . json_encode($certData['error']) . "</p>";
                        }
                    }
                }
                
            } else {
                echo "<p class='error'>❌ CSR generation failed</p>";
                echo "<p><strong>OpenSSL Error:</strong> " . openssl_error_string() . "</p>";
            }
        } else {
            echo "<p class='error'>❌ Private key generation failed</p>";
            echo "<p><strong>OpenSSL Error:</strong> " . openssl_error_string() . "</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Exception: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>Summary</h2>";
    echo "<p>If you see errors above, here are the solutions:</p>";
    echo "<ul>";
    echo "<li><strong>Certificate Limit:</strong> Create new ZeroSSL account</li>";
    echo "<li><strong>Invalid API Key:</strong> Check ZeroSSL dashboard</li>";
    echo "<li><strong>CSR Generation:</strong> Install OpenSSL for Windows</li>";
    echo "<li><strong>cURL Errors:</strong> Check internet connection</li>";
    echo "</ul>";
    
    echo "<p><a href='index.html'>← Back to SSL4Free</a></p>";
    ?>
</body>
</html>
