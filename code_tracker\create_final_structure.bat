@echo off
echo 🎯 Creating FINAL CodeCanyon Structure
echo =====================================

set "FINAL_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_FINAL"
set "SOURCE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete"

echo 🗑️ Removing old directory if exists...
if exist "%FINAL_DIR%" rmdir /s /q "%FINAL_DIR%"

echo 📁 Creating fresh directory structure...
mkdir "%FINAL_DIR%"
mkdir "%FINAL_DIR%\Thumbnail"
mkdir "%FINAL_DIR%\Theme_Preview" 
mkdir "%FINAL_DIR%\WordPress_Theme"

echo 📦 Creating proper WordPress plugin structure...
mkdir "%FINAL_DIR%\temp_plugin"
mkdir "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro"

echo 📄 Copying plugin files to proper structure...
copy "%SOURCE_DIR%\whatsapp-chat-widget.php" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1
copy "%SOURCE_DIR%\style.css" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1
copy "%SOURCE_DIR%\script.js" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1
copy "%SOURCE_DIR%\admin-style.css" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1
copy "%SOURCE_DIR%\admin-script.js" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1
copy "%SOURCE_DIR%\readme.txt" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1
copy "%SOURCE_DIR%\LICENSE.txt" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1
copy "%SOURCE_DIR%\INSTALLATION-GUIDE.md" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1
copy "%SOURCE_DIR%\FAQ.md" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1
copy "%SOURCE_DIR%\DEVELOPER-GUIDE.md" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul 2>&1

echo 🌐 Copying demo folder...
if exist "%SOURCE_DIR%\demo" (
    xcopy "%SOURCE_DIR%\demo" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\" /E /I /Q >nul 2>&1
)

echo 📦 Creating WordPress ZIP with proper structure...
powershell -Command "Compress-Archive -Path '%FINAL_DIR%\temp_plugin\whatsapp-widget-pro' -DestinationPath '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -Force"

echo 🗑️ Cleaning up temp files...
rmdir /s /q "%FINAL_DIR%\temp_plugin"

echo 📸 Copying screenshots for manual conversion...
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\01_General_Settings_Tab.png" "%FINAL_DIR%\Theme_Preview\01_general_settings_CONVERT_TO_JPG.png" >nul 2>&1
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\02_Agents_Management_Tab.png" "%FINAL_DIR%\Theme_Preview\02_agents_management_CONVERT_TO_JPG.png" >nul 2>&1
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\03_Appearance_Themes_Tab.png" "%FINAL_DIR%\Theme_Preview\03_appearance_themes_CONVERT_TO_JPG.png" >nul 2>&1
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\04_Working_Hours_Tab.png" "%FINAL_DIR%\Theme_Preview\04_working_hours_CONVERT_TO_JPG.png" >nul 2>&1
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\05_Advanced_Settings_Tab.png" "%FINAL_DIR%\Theme_Preview\05_advanced_settings_CONVERT_TO_JPG.png" >nul 2>&1
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\06_Preview_Tab.png" "%FINAL_DIR%\Theme_Preview\06_preview_tab_CONVERT_TO_JPG.png" >nul 2>&1

echo 🖼️ Copying thumbnail for manual resize...
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\01_General_Settings_Tab.png" "%FINAL_DIR%\Thumbnail\RESIZE_TO_80x80_thumbnail.png" >nul 2>&1

echo ✅ Basic structure created!
echo.
echo 📁 Location: %FINAL_DIR%
echo.
echo 🎯 MANUAL STEPS REQUIRED:
echo.
echo 1. THUMBNAIL (3 minutes):
echo    📁 Go to: %FINAL_DIR%\Thumbnail\
echo    🌐 Use: https://www.canva.com (80x80px, green background, "WA Pro" text)
echo    💾 Save as: thumbnail.png
echo    🗑️ Delete: RESIZE_TO_80x80_thumbnail.png
echo.
echo 2. SCREENSHOTS (5 minutes):
echo    📁 Go to: %FINAL_DIR%\Theme_Preview\
echo    🌐 Use: https://convertio.co/png-jpg/
echo    🔄 Convert all PNG files to JPG format
echo    📝 Remove "_CONVERT_TO_JPG" from names
echo    🗑️ Delete original PNG files
echo.
echo 3. FORM SETTINGS (1 minute):
echo    ✅ Compatible Browsers: Select ALL
echo    ✅ ThemeForest Files: CSS, JS, PHP
echo.
echo 🚀 Total time: 9 minutes to complete!
pause
