# WhatsApp Widget Pro - Developer Guide

## Table of Contents
1. [Plugin Architecture](#plugin-architecture)
2. [Hooks & Filters](#hooks--filters)
3. [JavaScript API](#javascript-api)
4. [CSS Classes](#css-classes)
5. [Database Schema](#database-schema)
6. [Customization Examples](#customization-examples)

## Plugin Architecture

### File Structure
```
whatsapp-widget-pro/
├── whatsapp-chat-widget.php    # Main plugin file
├── style.css                   # Frontend styles
├── script.js                   # Frontend JavaScript
├── admin-style.css            # Admin panel styles
├── admin-script.js            # Admin panel JavaScript
├── readme.txt                 # WordPress plugin readme
├── INSTALLATION-GUIDE.md      # Installation documentation
├── FAQ.md                     # Frequently asked questions
└── DEVELOPER-GUIDE.md         # This file
```

### Main Class Structure
```php
class WhatsAppChatWidget {
    private $default_settings;     # Default plugin settings
    
    // Core methods
    public function __construct()           # Initialize plugin
    public function activate()              # Plugin activation
    public function deactivate()            # Plugin deactivation
    
    // Frontend methods
    public function enqueue_scripts()       # Load CSS/JS
    public function display_widget()        # Render widget
    
    // Admin methods
    public function admin_menu()            # Add admin pages
    public function admin_page()            # Main settings page
    public function analytics_page()        # Analytics dashboard
    
    // AJAX handlers
    public function track_click_ajax()      # Track widget clicks
    public function save_settings_ajax()    # Save settings via AJAX
}
```

## Hooks & Filters

### Action Hooks

**Plugin Initialization**
```php
// After plugin is fully loaded
do_action('whatsapp_widget_loaded');

// Before widget is displayed
do_action('whatsapp_widget_before_display');

// After widget is displayed
do_action('whatsapp_widget_after_display');
```

**Settings Management**
```php
// Before settings are saved
do_action('whatsapp_widget_before_save_settings', $settings);

// After settings are saved
do_action('whatsapp_widget_after_save_settings', $settings);
```

**Analytics Events**
```php
// When a click is tracked
do_action('whatsapp_widget_click_tracked', $click_data);

// When analytics data is processed
do_action('whatsapp_widget_analytics_processed', $analytics_data);
```

### Filter Hooks

**Widget Configuration**
```php
// Modify widget settings before display
$settings = apply_filters('whatsapp_widget_settings', $settings);

// Modify widget HTML output
$html = apply_filters('whatsapp_widget_html', $html, $settings);

// Modify widget position
$position = apply_filters('whatsapp_widget_position', $position);
```

**Agent Management**
```php
// Modify available agents
$agents = apply_filters('whatsapp_widget_agents', $agents);

// Modify agent selection logic
$selected_agent = apply_filters('whatsapp_widget_selected_agent', $agent, $agents);
```

**Working Hours**
```php
// Modify working hours check
$is_online = apply_filters('whatsapp_widget_is_online', $is_online, $working_hours);

// Modify offline message
$offline_message = apply_filters('whatsapp_widget_offline_message', $message);
```

### Usage Examples

**Customize Widget for Specific Pages**
```php
function custom_whatsapp_settings($settings) {
    if (is_page('contact')) {
        $settings['message'] = 'Contact page inquiry';
        $settings['theme'] = 'minimal';
    }
    return $settings;
}
add_filter('whatsapp_widget_settings', 'custom_whatsapp_settings');
```

**Add Custom Agent Selection Logic**
```php
function custom_agent_selection($agent, $agents) {
    // Route to specific agent based on page
    if (is_shop()) {
        // Find sales agent
        foreach ($agents as $a) {
            if (strpos(strtolower($a['title']), 'sales') !== false) {
                return $a;
            }
        }
    }
    return $agent;
}
add_filter('whatsapp_widget_selected_agent', 'custom_agent_selection', 10, 2);
```

## JavaScript API

### Global Object
The plugin exposes a global `WhatsAppWidget` object with the following methods:

```javascript
// Track custom events
WhatsAppWidget.trackEvent(category, action, label);

// Send analytics data
WhatsAppWidget.sendAnalytics(agentPhone, agentName);

// Toggle bubble widget
WhatsAppWidget.toggleBubble();

// Close bubble widget
WhatsAppWidget.closeBubble();
```

### Custom Event Tracking
```javascript
// Set up custom tracking function
window.whatsappWidgetCustomTrack = function(category, action, label) {
    // Google Analytics 4
    if (typeof gtag !== 'undefined') {
        gtag('event', action, {
            event_category: category,
            event_label: label,
            custom_parameter_1: 'whatsapp_widget_pro'
        });
    }
    
    // Facebook Pixel
    if (typeof fbq !== 'undefined') {
        fbq('track', 'Contact', {
            content_name: 'WhatsApp Widget',
            content_category: category
        });
    }
    
    // Custom analytics service
    yourAnalytics.track(action, {
        category: category,
        label: label,
        source: 'whatsapp_widget'
    });
};
```

### Widget Events
```javascript
// Listen to widget events
document.addEventListener('whatsapp-widget-click', function(e) {
    console.log('Widget clicked:', e.detail);
});

document.addEventListener('whatsapp-bubble-opened', function(e) {
    console.log('Bubble opened:', e.detail);
});

document.addEventListener('whatsapp-bubble-closed', function(e) {
    console.log('Bubble closed:', e.detail);
});
```

## CSS Classes

### Main Widget Classes
```css
/* Main widget container */
.whatsapp-widget { }

/* Widget loaded state */
.whatsapp-widget.widget-loaded { }

/* Position classes */
.whatsapp-widget.bottom-right { }
.whatsapp-widget.bottom-left { }
.whatsapp-widget.top-right { }
.whatsapp-widget.top-left { }

/* Theme classes */
.whatsapp-widget.whatsapp-theme-default { }
.whatsapp-widget.whatsapp-theme-minimal { }
.whatsapp-widget.whatsapp-theme-dark { }
.whatsapp-widget.whatsapp-theme-gradient { }
.whatsapp-widget.whatsapp-theme-square { }
.whatsapp-widget.whatsapp-theme-pulse { }
```

### Bubble Widget Classes
```css
/* Bubble widget container */
.whatsapp-bubble-widget { }

/* Bubble trigger button */
.whatsapp-bubble-widget .bubble-trigger { }

/* Bubble content panel */
.whatsapp-bubble-widget .bubble-content { }

/* Active state */
.whatsapp-bubble-widget.active .bubble-content { }

/* Agent items */
.whatsapp-bubble-widget .agent-item { }
.whatsapp-bubble-widget .agent-avatar { }
.whatsapp-bubble-widget .agent-info { }
.whatsapp-bubble-widget .agent-chat-btn { }
```

### Status Indicators
```css
/* Online indicator */
.online-indicator { }

/* Offline indicator */
.offline-indicator { }

/* Offline widget */
.whatsapp-widget-offline { }
```

## Database Schema

### Analytics Table
```sql
CREATE TABLE wp_whatsapp_widget_analytics (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    click_time datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
    agent_phone varchar(20) NOT NULL,
    agent_name varchar(100) DEFAULT '',
    user_ip varchar(45) NOT NULL,
    user_agent text NOT NULL,
    page_url varchar(255) NOT NULL,
    referrer varchar(255) DEFAULT '',
    PRIMARY KEY (id),
    KEY click_time (click_time),
    KEY agent_phone (agent_phone)
);
```

### Options Stored
```php
// Plugin settings stored in wp_options
$options = [
    'whatsapp_enabled',
    'whatsapp_phone',
    'whatsapp_message',
    'whatsapp_position',
    'whatsapp_size',
    'whatsapp_theme',
    'whatsapp_show_on_mobile',
    'whatsapp_show_on_desktop',
    'whatsapp_working_hours_enabled',
    'whatsapp_working_hours',
    'whatsapp_timezone',
    'whatsapp_offline_message',
    'whatsapp_agents',
    'whatsapp_bubble_enabled',
    'whatsapp_bubble_title',
    'whatsapp_bubble_subtitle',
    'whatsapp_custom_css',
    'whatsapp_analytics_enabled'
];
```

## Customization Examples

### Custom Widget Theme
```css
/* Create a custom theme */
.whatsapp-widget.whatsapp-theme-custom a {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.4);
}

.whatsapp-widget.whatsapp-theme-custom a:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 40px rgba(255, 107, 107, 0.6);
}
```

### Conditional Widget Display
```php
function conditional_whatsapp_display($html, $settings) {
    // Hide on checkout pages
    if (function_exists('is_checkout') && is_checkout()) {
        return '';
    }
    
    // Show different message for logged-in users
    if (is_user_logged_in()) {
        $settings['message'] = 'Hello ' . wp_get_current_user()->display_name . '! How can we help?';
    }
    
    return $html;
}
add_filter('whatsapp_widget_html', 'conditional_whatsapp_display', 10, 2);
```

### Custom Analytics Integration
```php
function custom_whatsapp_analytics($click_data) {
    // Send to external analytics service
    wp_remote_post('https://your-analytics-api.com/track', [
        'body' => json_encode([
            'event' => 'whatsapp_click',
            'agent' => $click_data['agent_phone'],
            'page' => $click_data['page_url'],
            'timestamp' => $click_data['click_time']
        ]),
        'headers' => [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer YOUR_API_KEY'
        ]
    ]);
}
add_action('whatsapp_widget_click_tracked', 'custom_whatsapp_analytics');
```

### Dynamic Agent Assignment
```php
function dynamic_agent_assignment($agents) {
    $current_time = current_time('H');
    
    // Morning shift (9-17)
    if ($current_time >= 9 && $current_time < 17) {
        return array_filter($agents, function($agent) {
            return strpos($agent['title'], 'Day') !== false;
        });
    }
    
    // Evening shift (17-24)
    if ($current_time >= 17) {
        return array_filter($agents, function($agent) {
            return strpos($agent['title'], 'Evening') !== false;
        });
    }
    
    return $agents;
}
add_filter('whatsapp_widget_agents', 'dynamic_agent_assignment');
```

### Custom Shortcode Attributes
```php
function custom_whatsapp_shortcode($atts) {
    $atts = shortcode_atts([
        'phone' => get_option('whatsapp_phone'),
        'message' => get_option('whatsapp_message'),
        'size' => '60',
        'theme' => 'default',
        'department' => ''
    ], $atts);
    
    // Route to department-specific agent
    if ($atts['department']) {
        $agents = get_option('whatsapp_agents', []);
        foreach ($agents as $agent) {
            if (stripos($agent['title'], $atts['department']) !== false) {
                $atts['phone'] = $agent['phone'];
                break;
            }
        }
    }
    
    // Generate widget HTML
    ob_start();
    ?>
    <div class="whatsapp-widget whatsapp-theme-<?php echo esc_attr($atts['theme']); ?> whatsapp-shortcode" 
         style="position: relative; width: <?php echo esc_attr($atts['size']); ?>px; height: <?php echo esc_attr($atts['size']); ?>px; display: inline-block;">
        <a href="https://wa.me/<?php echo esc_attr($atts['phone']); ?>?text=<?php echo urlencode($atts['message']); ?>" target="_blank">
            <i class="fab fa-whatsapp"></i>
        </a>
    </div>
    <?php
    return ob_get_clean();
}
add_shortcode('whatsapp_widget_custom', 'custom_whatsapp_shortcode');
```

### Performance Optimization
```php
// Cache agent data
function cached_whatsapp_agents($agents) {
    $cache_key = 'whatsapp_agents_processed';
    $cached = wp_cache_get($cache_key);
    
    if ($cached !== false) {
        return $cached;
    }
    
    // Process agents (expensive operation)
    $processed_agents = array_map(function($agent) {
        // Add additional data, validate, etc.
        return $agent;
    }, $agents);
    
    wp_cache_set($cache_key, $processed_agents, '', 300); // 5 minutes
    return $processed_agents;
}
add_filter('whatsapp_widget_agents', 'cached_whatsapp_agents');
```

## Best Practices

### Performance
1. Use caching for expensive operations
2. Minimize database queries
3. Optimize CSS and JavaScript
4. Use CDN for static assets

### Security
1. Sanitize all user inputs
2. Use nonces for AJAX requests
3. Validate phone numbers
4. Escape output data

### Accessibility
1. Add proper ARIA labels
2. Ensure keyboard navigation
3. Provide alternative text
4. Test with screen readers

### Mobile Optimization
1. Test on real devices
2. Consider touch targets
3. Optimize for different screen sizes
4. Ensure fast loading

---

**Need help with custom development?** Contact our development team for professional customization services.
