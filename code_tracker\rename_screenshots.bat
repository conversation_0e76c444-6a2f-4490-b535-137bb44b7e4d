@echo off
setlocal enabledelayedexpansion
echo WhatsApp Widget Screenshots Renamer
echo ====================================

cd /d "C:\Users\<USER>\Desktop\WhatsApp_Widget_Screenshots"

echo Current directory: %CD%
echo Files before renaming:
dir *.png /b

echo.
echo Renaming screenshots...

set count=1
for %%f in (*.png) do (
    if !count!==1 (
        ren "%%f" "01_General_Settings_Tab.png"
        echo Renamed %%f to 01_General_Settings_Tab.png
    )
    if !count!==2 (
        ren "%%f" "02_Agents_Management_Tab.png"
        echo Renamed %%f to 02_Agents_Management_Tab.png
    )
    if !count!==3 (
        ren "%%f" "03_Appearance_Themes_Tab.png"
        echo Renamed %%f to 03_Appearance_Themes_Tab.png
    )
    if !count!==4 (
        ren "%%f" "04_Working_Hours_Tab.png"
        echo Renamed %%f to 04_Working_Hours_Tab.png
    )
    if !count!==5 (
        ren "%%f" "05_Advanced_Settings_Tab.png"
        echo Renamed %%f to 05_Advanced_Settings_Tab.png
    )
    if !count!==6 (
        ren "%%f" "06_Preview_Tab.png"
        echo Renamed %%f to 06_Preview_Tab.png
    )
    set /a count+=1
)

echo.
echo Renaming completed!
echo Files after renaming:
dir *.png /b

echo.
echo All screenshots renamed successfully!
