<?php
require_once 'config.php';

setApiHeaders();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo apiResponse(false, 'Method not allowed');
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo apiResponse(false, 'Invalid JSON input');
        exit();
    }
    
    // Validate required fields
    if (!isset($input['certificate_id'])) {
        echo apiResponse(false, 'Missing required field: certificate_id');
        exit();
    }
    
    $certificateId = trim($input['certificate_id']);
    
    logMessage("Checking certificate status for ID: $certificateId", 'INFO');
    
    // Get certificate details from ZeroSSL
    $response = zeroSSLRequest("/certificates/$certificateId");
    
    if (!$response) {
        echo apiResponse(false, 'Failed to retrieve certificate information from ZeroSSL API');
        exit();
    }
    
    // Check if certificate exists
    if (isset($response['success']) && $response['success'] === false) {
        echo apiResponse(false, 'Certificate not found or access denied');
        exit();
    }
    
    // Extract certificate information
    $certificateInfo = [
        'id' => $response['id'],
        'status' => $response['status'],
        'common_name' => $response['common_name'],
        'additional_domains' => $response['additional_domains'] ?? '',
        'created' => $response['created'],
        'expires' => $response['expires'],
        'validation_type' => $response['validation_type'],
        'validation_emails' => $response['validation_emails']
    ];
    
    // Get verification status if pending validation
    $verificationStatus = null;
    if ($response['status'] === 'pending_validation') {
        $verificationStatus = getVerificationStatus($certificateId, $response);
    }
    
    // Check if certificate is ready for download
    $downloadReady = ($response['status'] === 'issued');
    
    // Prepare response data
    $responseData = [
        'certificate' => $certificateInfo,
        'verification_status' => $verificationStatus,
        'download_ready' => $downloadReady,
        'next_action' => getNextAction($response['status'], $response['validation_type'])
    ];
    
    logMessage("Certificate status retrieved successfully for ID: $certificateId", 'INFO');
    
    echo apiResponse(true, 'Certificate status retrieved successfully', $responseData);
    
} catch (Exception $e) {
    logMessage("Error checking certificate status: " . $e->getMessage(), 'ERROR');
    echo apiResponse(false, 'Internal server error: ' . $e->getMessage());
}

function getVerificationStatus($certificateId, $certificateData) {
    try {
        // According to ZeroSSL documentation, verification status endpoint is only for EMAIL verification
        if ($certificateData['validation_type'] === 'EMAIL') {
            logMessage("Checking email verification status for certificate: $certificateId", 'INFO');

            $statusResponse = zeroSSLRequest("/certificates/$certificateId/status");

            if ($statusResponse) {
                logMessage("Email verification status response: " . json_encode($statusResponse), 'INFO');

                return [
                    'validation_completed' => $statusResponse['validation_completed'] ?? 0,
                    'details' => $statusResponse['details'] ?? [],
                    'method' => 'EMAIL',
                    'status' => $statusResponse['validation_completed'] ? 'Completed' : 'Pending email verification'
                ];
            }
        } else {
            // For CNAME and HTTP/HTTPS methods, verification results are instant
            // Check the certificate status directly
            logMessage("Checking instant verification status for method: " . $certificateData['validation_type'], 'INFO');

            // For non-email methods, we need to check if the certificate status changed to 'issued'
            $currentStatus = $certificateData['status'];

            if ($currentStatus === 'issued') {
                return [
                    'validation_completed' => 1,
                    'method' => $certificateData['validation_type'],
                    'status' => 'Verification completed - Certificate issued'
                ];
            } else if ($currentStatus === 'pending_validation') {
                return [
                    'validation_completed' => 0,
                    'method' => $certificateData['validation_type'],
                    'status' => 'Pending verification - Complete the required steps',
                    'instructions' => getVerificationInstructions($certificateData['validation_type'])
                ];
            } else {
                return [
                    'validation_completed' => 0,
                    'method' => $certificateData['validation_type'],
                    'status' => 'Certificate status: ' . $currentStatus
                ];
            }
        }

        // Fallback
        return [
            'validation_completed' => 0,
            'method' => $certificateData['validation_type'] ?? 'unknown',
            'status' => 'Unable to determine verification status'
        ];

    } catch (Exception $e) {
        logMessage("Error getting verification status: " . $e->getMessage(), 'ERROR');
        return [
            'validation_completed' => 0,
            'method' => $certificateData['validation_type'] ?? 'unknown',
            'status' => 'Error checking verification status',
            'error' => $e->getMessage()
        ];
    }
}

function getVerificationInstructions($validationType) {
    switch ($validationType) {
        case 'HTTP_CSR_HASH':
            return 'Upload the verification file to your website at the specified HTTP URL path.';
        case 'HTTPS_CSR_HASH':
            return 'Upload the verification file to your website at the specified HTTPS URL path.';
        case 'CNAME_CSR_HASH':
            return 'Add the CNAME record to your DNS settings as specified.';
        case 'EMAIL':
            return 'Check your email and click the verification link.';
        default:
            return 'Complete the verification process as instructed.';
    }
}

function getNextAction($status, $validationType) {
    switch ($status) {
        case 'draft':
            return 'Complete domain verification to proceed with certificate issuance.';
        case 'pending_validation':
            switch ($validationType) {
                case 'EMAIL':
                    return 'Check your email and click the verification link to complete domain verification.';
                case 'HTTP_CSR_HASH':
                case 'HTTPS_CSR_HASH':
                    return 'Ensure the verification file is uploaded and accessible on your website.';
                case 'CNAME_CSR_HASH':
                    return 'Verify that the CNAME record has been added to your DNS settings.';
                default:
                    return 'Complete the domain verification process.';
            }
        case 'issued':
            return 'Certificate is ready! You can download the certificate files.';
        case 'cancelled':
            return 'Certificate has been cancelled. Create a new certificate if needed.';
        case 'revoked':
            return 'Certificate has been revoked. Create a new certificate if needed.';
        case 'expired':
            return 'Certificate has expired. Create a new certificate to replace it.';
        default:
            return 'Check certificate status for next steps.';
    }
}
?>
