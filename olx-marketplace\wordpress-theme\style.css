/*
Theme Name: OLX Marketplace
Description: A complete marketplace theme similar to OLX for buying and selling products
Author: Your Name
Version: 1.0
Text Domain: olx-marketplace
*/

/* Import the main stylesheet */
@import url('../css/style.css');

/* WordPress specific styles */
.wp-admin-bar {
    display: none !important;
}

body.admin-bar {
    margin-top: 0 !important;
}

/* WordPress content styles */
.wp-content {
    margin: 20px 0;
}

.wp-post {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.wp-post-title {
    color: #002f34;
    margin-bottom: 15px;
    font-size: 24px;
}

.wp-post-meta {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.wp-post-content {
    line-height: 1.6;
}

/* WordPress form styles */
.wp-form {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    max-width: 600px;
    margin: 0 auto;
}

.wp-form-group {
    margin-bottom: 20px;
}

.wp-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #002f34;
}

.wp-form-input,
.wp-form-textarea,
.wp-form-select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.wp-form-input:focus,
.wp-form-textarea:focus,
.wp-form-select:focus {
    outline: none;
    border-color: #23e5db;
}

.wp-form-textarea {
    min-height: 120px;
    resize: vertical;
}

.wp-form-submit {
    background: #002f34;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s;
}

.wp-form-submit:hover {
    background: #001a1d;
}

/* WordPress pagination */
.wp-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin: 40px 0;
}

.wp-pagination a,
.wp-pagination span {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-decoration: none;
    color: #002f34;
    transition: all 0.3s;
}

.wp-pagination a:hover {
    background: #002f34;
    color: white;
    border-color: #002f34;
}

.wp-pagination .current {
    background: #002f34;
    color: white;
    border-color: #002f34;
}

/* WordPress widget styles */
.wp-widget {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.wp-widget-title {
    color: #002f34;
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 2px solid #23e5db;
    padding-bottom: 10px;
}

.wp-widget ul {
    list-style: none;
}

.wp-widget ul li {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.wp-widget ul li:last-child {
    border-bottom: none;
}

.wp-widget ul li a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s;
}

.wp-widget ul li a:hover {
    color: #002f34;
}

/* WordPress comment styles */
.wp-comments {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.wp-comment {
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.wp-comment:last-child {
    border-bottom: none;
}

.wp-comment-author {
    font-weight: bold;
    color: #002f34;
    margin-bottom: 5px;
}

.wp-comment-date {
    color: #666;
    font-size: 12px;
    margin-bottom: 10px;
}

.wp-comment-content {
    line-height: 1.6;
}

/* Responsive adjustments for WordPress */
@media (max-width: 768px) {
    .wp-form {
        padding: 20px;
        margin: 0 10px;
    }
    
    .wp-pagination {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .wp-pagination a,
    .wp-pagination span {
        padding: 6px 10px;
        font-size: 14px;
    }
}
