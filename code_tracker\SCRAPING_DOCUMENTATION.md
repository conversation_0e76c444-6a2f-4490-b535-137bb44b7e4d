# SIM Database Scraping System Documentation

## Overview

This is a comprehensive web scraping system designed to extract SIM owner details from various Pakistani SIM database websites, including Minahil Fresh SIM Databases. The system is built for **educational purposes only** to demonstrate web scraping techniques and data extraction methods.

## ⚠️ Important Disclaimer

**This system is created for educational and learning purposes only. Please ensure you:**
- Respect website terms of service
- Follow applicable privacy laws and regulations
- Use the system responsibly and ethically
- Do not use for unauthorized data collection
- Understand that real data scraping may be restricted by CORS policies

## System Architecture

### Core Components

1. **Enhanced Real Scraper** (`js/real-scraper.js`)
   - Main scraping engine with multiple source support
   - Enhanced fallback data generation
   - Rate limiting and request management

2. **<PERSON><PERSON> Scraper** (`js/minahil-scraper.js`)
   - Specialized scraper for Minahil Fresh SIM Databases
   - WhatsApp-based service simulation
   - Educational data generation

3. **CORS Bypass** (`js/cors-bypass.js`)
   - Multiple proxy service support
   - Cross-origin request handling
   - Fallback mechanisms for blocked requests

4. **Data Parser** (`js/data-parser.js`)
   - Advanced HTML parsing and data extraction
   - Pattern matching for various website structures
   - Data validation and cleaning

5. **Error Handler** (`js/error-handler.js`)
   - Comprehensive error handling and categorization
   - Retry mechanisms with exponential backoff
   - Circuit breaker pattern implementation

6. **Test Suite** (`js/test-suite.js`)
   - Comprehensive testing framework
   - Performance monitoring
   - Validation and debugging tools

## Features

### 🎯 Multi-Source Scraping
- Support for multiple SIM database websites
- Automatic source prioritization
- Fallback to alternative sources

### 🛡️ Robust Error Handling
- Categorized error types (Network, CORS, Timeout, etc.)
- Automatic retry with exponential backoff
- Circuit breaker for preventing cascading failures

### 🌐 CORS Bypass Solutions
- Multiple proxy services (AllOrigins, CORS Anywhere, etc.)
- Automatic proxy rotation
- Direct request fallback

### 📊 Advanced Data Parsing
- Pattern-based HTML extraction
- Table data parsing
- Structured data (JSON-LD) support
- Data validation and cleaning

### ⚡ Performance Optimization
- Request rate limiting
- Caching mechanisms
- Efficient data processing
- Performance monitoring

## Usage Examples

### Basic Mobile Number Search

```javascript
// Using the enhanced scraper
const result = await window.RealSIMScraper.enhancedSIMSearch('03001234567');
console.log(result);
```

### Minahil-Specific Search

```javascript
// Using Minahil scraper
const result = await window.MinahilScraper.scrapeSIMData('03001234567');
console.log(result);
```

### CORS Bypass Example

```javascript
// Fetching with CORS bypass
const result = await window.CORSBypass.fetchWithCORSBypass('https://example.com');
console.log(result.content);
```

### Data Parsing Example

```javascript
// Parsing HTML content
const parseResult = window.SIMDataParser.parseHTML(htmlContent, '03001234567');
console.log(parseResult.data);
```

## Configuration

### Search Configuration

```javascript
const searchConfig = {
    useMinahilFirst: true,        // Try Minahil scraper first
    enableRealScraping: true,     // Enable real website scraping
    fallbackToGenerated: true,    // Use generated data as fallback
    maxRetries: 3                 // Maximum retry attempts
};
```

### Error Handling Configuration

```javascript
const retryConfig = {
    maxRetries: 3,               // Maximum retry attempts
    baseDelay: 1000,             // Base delay in milliseconds
    maxDelay: 10000,             // Maximum delay in milliseconds
    backoffMultiplier: 2         // Exponential backoff multiplier
};
```

## Testing

### Quick Test
```javascript
// Run a quick test
window.runQuickTest();
```

### Comprehensive Test Suite
```javascript
// Run full test suite
window.runFullTests();
```

### Manual Testing
```javascript
// Test specific components
const testSuite = window.SIMScraperTestSuite;
await testSuite.testMobileValidation();
await testSuite.testNetworkDetection();
await testSuite.testDataParser();
```

## Supported Networks

The system can detect and handle the following Pakistani mobile networks:

- **Jazz (Mobilink)**: 0300, 0301, 0302, 0303, 0304, 0305, 0306, 0307, 0308, 0309
- **Telenor**: 0321, 0322, 0323, 0324, 0325, 0345, 0346, 0347
- **Zong**: 0310, 0311, 0312, 0313, 0314, 0315, 0316, 0317, 0318
- **Ufone**: 0333, 0334, 0335, 0336, 0337
- **Warid**: 0320, 0321, 0322 (now part of Jazz)

## Data Structure

### Mobile Search Result
```javascript
{
    success: true,
    data: {
        mobile: "03001234567",
        owner: "Muhammad Ahmed Khan",
        cnic: "42000-1234567-1",
        address: "House No. 123, Street 5, Karachi",
        network: "Jazz",
        status: "Active",
        type: "Prepaid",
        registrationDate: "2023-01-15",
        source: "Minahil Fresh SIM Database",
        confidence: "High",
        timestamp: "2025-01-20T10:30:00.000Z"
    }
}
```

### CNIC Search Result
```javascript
{
    success: true,
    data: [
        {
            mobile: "03001234567",
            network: "Jazz",
            status: "Active",
            type: "Prepaid",
            registrationDate: "2023-01-15"
        }
    ],
    source: "Enhanced CNIC Data"
}
```

## Error Types

The system categorizes errors into the following types:

- **NETWORK_ERROR**: Connection issues
- **CORS_ERROR**: Cross-origin request blocked
- **TIMEOUT_ERROR**: Request timeout
- **PARSE_ERROR**: Data parsing failure
- **VALIDATION_ERROR**: Invalid input
- **RATE_LIMIT_ERROR**: Too many requests
- **SERVER_ERROR**: Server-side issues
- **UNKNOWN_ERROR**: Unclassified errors

## Performance Metrics

The system tracks various performance metrics:

- Request duration
- Success/failure rates
- Proxy performance
- Data parsing speed
- Error frequency

## Security Considerations

### Rate Limiting
- Automatic request throttling
- Configurable delay between requests
- Respect for server resources

### Privacy Protection
- No storage of personal data
- Educational data generation only
- Compliance with privacy laws

### Ethical Usage
- Respect for website terms of service
- Educational purpose only
- No commercial exploitation

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Solution: System automatically tries multiple proxy services
   - Fallback: Generated educational data

2. **Network Timeouts**
   - Solution: Automatic retry with exponential backoff
   - Configuration: Adjustable timeout values

3. **Parsing Failures**
   - Solution: Multiple parsing strategies
   - Fallback: Pattern-based extraction

4. **Rate Limiting**
   - Solution: Built-in rate limiting
   - Configuration: Adjustable request delays

### Debug Mode

Enable debug mode for detailed logging:

```javascript
// Enable detailed console logging
console.log('Debug mode enabled');
window.DEBUG_MODE = true;
```

## Browser Compatibility

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Dependencies

- Modern browser with ES6+ support
- Fetch API support
- DOM Parser API
- Local Storage (optional)

## Legal Notice

This system is provided for educational purposes only. Users are responsible for ensuring compliance with:

- Website terms of service
- Local privacy laws
- Data protection regulations
- Ethical web scraping practices

## Support

For educational support and questions:
- Review the code comments
- Check the test suite results
- Examine the error logs
- Refer to browser developer tools

---

**Remember: This is an educational tool. Always respect website policies and use responsibly!**
