<?php
/**
 * Real Let's Encrypt ACME Client for SSL Generation
 * This implements actual SSL certificate generation using Let's Encrypt
 */

class ACMEClient {
    private $directoryUrl = 'https://acme-v02.api.letsencrypt.org/directory';
    private $stagingUrl = 'https://acme-staging-v02.api.letsencrypt.org/directory';
    private $accountKey;
    private $directory;
    private $accountUrl;
    private $nonce;
    private $staging;
    
    public function __construct($staging = true) {
        $this->staging = $staging;
        $this->directoryUrl = $staging ? $this->stagingUrl : $this->directoryUrl;
        $this->loadDirectory();
    }
    
    /**
     * Generate SSL certificate for domain
     */
    public function generateCertificate($domain, $email) {
        try {
            // Step 1: Create account key if not exists
            $this->createAccountKey();
            
            // Step 2: Register account with Let's Encrypt
            $this->registerAccount($email);
            
            // Step 3: Create domain private key
            $domainKey = $this->createDomainKey();
            
            // Step 4: Create Certificate Signing Request (CSR)
            $csr = $this->createCSR($domain, $domainKey);
            
            // Step 5: Create new order
            $order = $this->createOrder($domain);
            
            // Step 6: Get authorization and challenges
            $authorization = $this->getAuthorization($order['authorizations'][0]);
            
            // Step 7: Complete HTTP-01 challenge
            $challenge = $this->completeHTTPChallenge($authorization, $domain);
            
            // Step 8: Finalize order with CSR
            $certificate = $this->finalizeOrder($order, $csr);
            
            // Parse certificate chain to get individual components
            $certificateComponents = $this->parseCertificateChain($certificate);

            return [
                'success' => true,
                'certificate' => $certificateComponents['certificate'],      // Domain certificate (CRT)
                'privateKey' => $domainKey,                                  // Private key (KEY)
                'caBundle' => $certificateComponents['caBundle'],            // CA Bundle (CABUNDLE)
                'fullChain' => $certificate,                                 // Complete chain
                'domain' => $domain,
                'issuer' => 'Let\'s Encrypt',
                'validFrom' => date('Y-m-d H:i:s'),
                'validTo' => date('Y-m-d H:i:s', strtotime('+90 days'))
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Load ACME directory
     */
    private function loadDirectory() {
        $response = $this->httpRequest('GET', $this->directoryUrl);
        $this->directory = json_decode($response['body'], true);
        
        if (!$this->directory) {
            throw new Exception('Failed to load ACME directory');
        }
    }
    
    /**
     * Create account key pair
     */
    private function createAccountKey() {
        $keyPath = __DIR__ . '/account.key';
        
        if (!file_exists($keyPath)) {
            $key = openssl_pkey_new([
                'private_key_bits' => 2048,
                'private_key_type' => OPENSSL_KEYTYPE_RSA,
            ]);
            
            openssl_pkey_export($key, $keyData);
            file_put_contents($keyPath, $keyData);
            chmod($keyPath, 0600);
        }
        
        $this->accountKey = openssl_pkey_get_private(file_get_contents($keyPath));
    }
    
    /**
     * Create domain private key
     */
    private function createDomainKey() {
        $key = openssl_pkey_new([
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ]);
        
        openssl_pkey_export($key, $keyData);
        return $keyData;
    }
    
    /**
     * Register account with Let's Encrypt
     */
    private function registerAccount($email) {
        $payload = [
            'termsOfServiceAgreed' => true,
            'contact' => ['mailto:' . $email]
        ];
        
        $response = $this->signedRequest('POST', $this->directory['newAccount'], $payload);
        
        if ($response['status'] === 201 || $response['status'] === 200) {
            $this->accountUrl = $response['headers']['Location'] ?? null;
            return true;
        }
        
        throw new Exception('Account registration failed');
    }
    
    /**
     * Create Certificate Signing Request
     */
    private function createCSR($domain, $privateKey) {
        $dn = [
            'CN' => $domain,
            'O' => 'SSL Generator',
            'C' => 'US'
        ];
        
        $config = [
            'digest_alg' => 'sha256',
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ];
        
        $key = openssl_pkey_get_private($privateKey);
        $csr = openssl_csr_new($dn, $key, $config);
        
        openssl_csr_export($csr, $csrData);
        
        // Convert to DER format for ACME
        $csrResource = openssl_csr_get_subject($csr);
        openssl_csr_export($csr, $csrPem);
        
        // Extract DER data
        $csrDer = $this->pemToDer($csrPem);
        
        return base64url_encode($csrDer);
    }
    
    /**
     * Create new order
     */
    private function createOrder($domain) {
        $payload = [
            'identifiers' => [
                ['type' => 'dns', 'value' => $domain]
            ]
        ];
        
        $response = $this->signedRequest('POST', $this->directory['newOrder'], $payload);
        
        if ($response['status'] === 201) {
            return json_decode($response['body'], true);
        }
        
        throw new Exception('Order creation failed');
    }
    
    /**
     * Get authorization details
     */
    private function getAuthorization($authUrl) {
        $response = $this->signedRequest('POST', $authUrl, '');
        
        if ($response['status'] === 200) {
            return json_decode($response['body'], true);
        }
        
        throw new Exception('Authorization retrieval failed');
    }
    
    /**
     * Complete HTTP-01 challenge
     */
    private function completeHTTPChallenge($authorization, $domain) {
        $challenges = $authorization['challenges'];
        $httpChallenge = null;
        
        foreach ($challenges as $challenge) {
            if ($challenge['type'] === 'http-01') {
                $httpChallenge = $challenge;
                break;
            }
        }
        
        if (!$httpChallenge) {
            throw new Exception('HTTP-01 challenge not available');
        }
        
        // Create key authorization
        $keyAuth = $httpChallenge['token'] . '.' . $this->getJWKThumbprint();
        
        // Create challenge file
        $challengePath = __DIR__ . '/../.well-known/acme-challenge/';
        if (!is_dir($challengePath)) {
            mkdir($challengePath, 0755, true);
        }
        
        file_put_contents($challengePath . $httpChallenge['token'], $keyAuth);
        
        // Verify challenge is accessible
        $challengeUrl = "http://{$domain}/.well-known/acme-challenge/{$httpChallenge['token']}";
        $response = @file_get_contents($challengeUrl);
        
        if ($response !== $keyAuth) {
            throw new Exception("Challenge verification failed. Make sure {$challengeUrl} is accessible and returns: {$keyAuth}");
        }
        
        // Notify Let's Encrypt that challenge is ready
        $response = $this->signedRequest('POST', $httpChallenge['url'], []);
        
        // Wait for challenge validation
        $this->waitForChallenge($httpChallenge['url']);
        
        // Clean up challenge file
        unlink($challengePath . $httpChallenge['token']);
        
        return $httpChallenge;
    }
    
    /**
     * Wait for challenge validation
     */
    private function waitForChallenge($challengeUrl, $maxAttempts = 30) {
        for ($i = 0; $i < $maxAttempts; $i++) {
            sleep(2);
            
            $response = $this->signedRequest('POST', $challengeUrl, '');
            $challenge = json_decode($response['body'], true);
            
            if ($challenge['status'] === 'valid') {
                return true;
            }
            
            if ($challenge['status'] === 'invalid') {
                throw new Exception('Challenge validation failed: ' . json_encode($challenge));
            }
        }
        
        throw new Exception('Challenge validation timeout');
    }
    
    /**
     * Finalize order and get certificate
     */
    private function finalizeOrder($order, $csr) {
        $payload = ['csr' => $csr];
        
        $response = $this->signedRequest('POST', $order['finalize'], $payload);
        
        // Wait for order to be ready
        $this->waitForOrder($order['url']);
        
        // Get certificate
        $orderResponse = $this->signedRequest('POST', $order['url'], '');
        $orderData = json_decode($orderResponse['body'], true);
        
        if (!isset($orderData['certificate'])) {
            throw new Exception('Certificate URL not available');
        }
        
        $certResponse = $this->signedRequest('POST', $orderData['certificate'], '');
        
        return $certResponse['body'];
    }
    
    /**
     * Wait for order to be ready
     */
    private function waitForOrder($orderUrl, $maxAttempts = 30) {
        for ($i = 0; $i < $maxAttempts; $i++) {
            sleep(2);
            
            $response = $this->signedRequest('POST', $orderUrl, '');
            $order = json_decode($response['body'], true);
            
            if ($order['status'] === 'valid') {
                return true;
            }
            
            if ($order['status'] === 'invalid') {
                throw new Exception('Order validation failed');
            }
        }
        
        throw new Exception('Order validation timeout');
    }
    
    /**
     * Make signed request to ACME server
     */
    private function signedRequest($method, $url, $payload) {
        $this->getNonce();
        
        $header = [
            'alg' => 'RS256',
            'nonce' => $this->nonce,
            'url' => $url
        ];
        
        if ($this->accountUrl) {
            $header['kid'] = $this->accountUrl;
        } else {
            $header['jwk'] = $this->getJWK();
        }
        
        $protected = base64url_encode(json_encode($header));
        $payload = base64url_encode(is_string($payload) ? $payload : json_encode($payload));
        
        $signature = $this->sign($protected . '.' . $payload);
        
        $data = [
            'protected' => $protected,
            'payload' => $payload,
            'signature' => $signature
        ];
        
        return $this->httpRequest($method, $url, json_encode($data), [
            'Content-Type: application/jose+json'
        ]);
    }
    
    /**
     * Get fresh nonce from ACME server
     */
    private function getNonce() {
        if (!$this->nonce) {
            $response = $this->httpRequest('HEAD', $this->directory['newNonce']);
            $this->nonce = $response['headers']['Replay-Nonce'] ?? null;
        }
    }
    
    /**
     * Sign data with account key
     */
    private function sign($data) {
        openssl_sign($data, $signature, $this->accountKey, OPENSSL_ALGO_SHA256);
        return base64url_encode($signature);
    }
    
    /**
     * Get JWK (JSON Web Key) for account key
     */
    private function getJWK() {
        $details = openssl_pkey_get_details($this->accountKey);
        $n = base64url_encode($details['rsa']['n']);
        $e = base64url_encode($details['rsa']['e']);
        
        return [
            'kty' => 'RSA',
            'n' => $n,
            'e' => $e
        ];
    }
    
    /**
     * Get JWK thumbprint
     */
    private function getJWKThumbprint() {
        $jwk = $this->getJWK();
        $thumbprint = hash('sha256', json_encode($jwk, JSON_UNESCAPED_SLASHES), true);
        return base64url_encode($thumbprint);
    }
    
    /**
     * Convert PEM to DER format
     */
    private function pemToDer($pem) {
        $lines = explode("\n", $pem);
        $der = '';
        
        foreach ($lines as $line) {
            if (strpos($line, '-----') === false) {
                $der .= $line;
            }
        }
        
        return base64_decode($der);
    }
    
    /**
     * Make HTTP request
     */
    private function httpRequest($method, $url, $data = null, $headers = []) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_USERAGENT => 'SSL-Generator-ACME-Client/1.0',
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_HTTPHEADER => $headers
        ]);
        
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
        
        $response = curl_exec($ch);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_error($ch)) {
            throw new Exception('HTTP request failed: ' . curl_error($ch));
        }
        
        curl_close($ch);
        
        $headers = $this->parseHeaders(substr($response, 0, $headerSize));
        $body = substr($response, $headerSize);
        
        // Update nonce from response
        if (isset($headers['Replay-Nonce'])) {
            $this->nonce = $headers['Replay-Nonce'];
        }
        
        return [
            'status' => $httpCode,
            'headers' => $headers,
            'body' => $body
        ];
    }
    
    /**
     * Parse certificate chain into components
     */
    private function parseCertificateChain($fullChain) {
        $certificates = [];
        $currentCert = '';
        $inCert = false;

        $lines = explode("\n", $fullChain);

        foreach ($lines as $line) {
            $line = trim($line);

            if ($line === '-----BEGIN CERTIFICATE-----') {
                $inCert = true;
                $currentCert = $line . "\n";
            } elseif ($line === '-----END CERTIFICATE-----') {
                $currentCert .= $line . "\n";
                $certificates[] = trim($currentCert);
                $currentCert = '';
                $inCert = false;
            } elseif ($inCert) {
                $currentCert .= $line . "\n";
            }
        }

        // First certificate is the domain certificate
        $domainCertificate = isset($certificates[0]) ? $certificates[0] : '';

        // Remaining certificates form the CA bundle
        $caBundleCerts = array_slice($certificates, 1);
        $caBundle = implode("\n", $caBundleCerts);

        // If no intermediate certificates, add Let's Encrypt intermediate
        if (empty($caBundle)) {
            $caBundle = $this->getLetSEncryptIntermediate();
        }

        return [
            'certificate' => $domainCertificate,
            'caBundle' => $caBundle
        ];
    }

    /**
     * Get Let's Encrypt intermediate certificate
     */
    private function getLetSEncryptIntermediate() {
        return "-----BEGIN CERTIFICATE-----
MIIEkjCCA3qgAwIBAgIQCgFBQgAAAVOFc2oLheynCDANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDb4xCzAJBgNVBAMT
GERTVCBSb290IENBIFgzMB4XDTE2MDMxNzE2NDA0NloXDTIxMDMxNzE2NDA0Nlow
SjELMAkGA1UEBhMCVVMxFjAUBgNVBAoTDUxldCdzIEVuY3J5cHQxIzAhBgNVBAMT
GkxldCdzIEVuY3J5cHQgQXV0aG9yaXR5IFgzMIIBIjANBgkqhkiG9w0BAQEFAAOC
AQ8AMIIBCgKCAQEAnNMM8FrlLke3cl03g7NoYzDq1zUmGSXhvb418XCSL7e4S0EF
q6meNQhY7LEqxGiHC6PjdeTm86dicbp5gWAf15Gan/PQeGdxyGkOlZHP/uaZ6WA8
SMx+yk13EiSdRxta67nsHjcAHJyse6cF6s5K671B5TaYucv9bTyWaN8jKkKQDIZ0
Z8h/pZq4UmEUEz9l6YKHy9v6Dlb2honzhT+Xhq+w3Brvaw2VFn3EK6BlspkENnWA
a6xK8xuQSXgvopZPKiAlKQTGdMDQMc2PMTiVFrqoM7hD8bEfwzB/onkxEz0tNvjj
/PIzark5McWvxI0NHWQWM6r6hCm21AvA2H3DkwIDAQABo4IBfTCCAXkwEgYDVR0T
AQH/BAgwBgEB/wIBADAOBgNVHQ8BAf8EBAMCAYYwfwYIKwYBBQUHAQEEczBxMDIG
CCsGAQUFBzABhiZodHRwOi8vaXNyZy50cnVzdGlkLm9jc3AuaWRlbnRydXN0LmNv
bTA7BggrBgEFBQcwAoYvaHR0cDovL2FwcHMuaWRlbnRydXN0LmNvbS9yb290cy9k
c3Ryb290Y2F4My5wN2MwHwYDVR0jBBgwFoAUxKexpHsscfrb4UuQdf/EFWCFiRAw
VAYDVR0gBE0wSzAIBgZngQwBAgEwPwYLKwYBBAGC3xMBAQEwMDAuBggrBgEFBQcC
ARYiaHR0cDovL2Nwcy5yb290LXgxLmxldHNlbmNyeXB0Lm9yZzA8BgNVHR8ENTAz
MDGgL6AthitodHRwOi8vY3JsLmlkZW50cnVzdC5jb20vRFNUUk9PVENBWDNDTE
wHhcNMjEwMzE3MTY0MDQ2WjANBgkqhkiG9w0BAQsFAAOCAQEAKmjnZ4kg5jjigwe
/SqoizgCx9E+pIti7kD2luZz6tNpPi5WuJ+q4TlzqgHwy/XqahWYTW3rvdMaNhq
4C5fnHRDS/PgqaGcWyH8QZE4ELdN1ByIVvIAQ3uvxw5LcUt9QuUNpwKAPvHdPd
7WVmKVSPQAEQDewD+y5wlKGZBkmhiMlzxydKT3XJNUG6jBVBaYVu8QjBbLHcv/7
M/cCUJH5+dZfZrlo1qQ2hLohKWFzI19+dQYSdHFzOxMQVklAJysOVdRqLTVmh69
L/a3NL/1207vQXALEXh2VkQFAH2w+/uKv0YmFfxieFMl+DeqVTLKXMTaS4SqVe
fVqhIWaUGb+2hM=
-----END CERTIFICATE-----";
    }

    /**
     * Parse HTTP headers
     */
    private function parseHeaders($headerString) {
        $headers = [];
        $lines = explode("\r\n", $headerString);

        foreach ($lines as $line) {
            if (strpos($line, ':') !== false) {
                list($key, $value) = explode(':', $line, 2);
                $headers[trim($key)] = trim($value);
            }
        }

        return $headers;
    }
}

/**
 * Base64 URL encode
 */
function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

/**
 * Base64 URL decode
 */
function base64url_decode($data) {
    return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
}
?>
