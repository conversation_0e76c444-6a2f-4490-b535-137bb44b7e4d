<!DOCTYPE html>
<html>
<head>
    <title>Domain Validation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        input { padding: 10px; margin: 5px; width: 300px; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Domain Validation Test</h1>
    
    <form method="POST">
        <input type="text" name="test_domain" placeholder="Enter domain to test" 
               value="<?php echo isset($_POST['test_domain']) ? htmlspecialchars($_POST['test_domain']) : 'test.jobzhit.com'; ?>">
        <input type="email" name="test_email" placeholder="Enter email" 
               value="<?php echo isset($_POST['test_email']) ? htmlspecialchars($_POST['test_email']) : '<EMAIL>'; ?>">
        <button type="submit">Test Domain Validation</button>
    </form>
    
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        require_once 'api/config.php';
        
        $testDomain = trim($_POST['test_domain']);
        $testEmail = trim($_POST['test_email']);
        
        echo "<h2>Testing Domain: $testDomain</h2>";
        echo "<h3>Testing Email: $testEmail</h3>";
        
        // Test 1: JavaScript-style validation
        echo "<h3>Test 1: JavaScript Domain Format Validation</h3>";
        $jsPattern = '/^[a-zA-Z0-9][a-zA-Z0-9.-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/';
        if (preg_match($jsPattern, $testDomain)) {
            echo "<p class='success'>✅ JavaScript validation: PASSED</p>";
        } else {
            echo "<p class='error'>❌ JavaScript validation: FAILED</p>";
        }
        
        // Test 2: PHP domain validation
        echo "<h3>Test 2: PHP Domain Validation</h3>";
        if (validateDomain($testDomain)) {
            echo "<p class='success'>✅ PHP validateDomain(): PASSED</p>";
        } else {
            echo "<p class='error'>❌ PHP validateDomain(): FAILED</p>";
        }
        
        // Test 3: Email validation
        echo "<h3>Test 3: Email Validation</h3>";
        if (validateEmail($testEmail)) {
            echo "<p class='success'>✅ Email validation: PASSED</p>";
        } else {
            echo "<p class='error'>❌ Email validation: FAILED</p>";
        }
        
        // Test 4: Domain accessibility
        echo "<h3>Test 4: Domain Accessibility Check</h3>";
        try {
            $url = "http://$testDomain/";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'SSL4Free Domain Validator');
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if (!$error && $httpCode > 0) {
                echo "<p class='success'>✅ Domain accessible: HTTP $httpCode</p>";
            } else {
                echo "<p class='warning'>⚠️ Domain not accessible: $error (but allowing anyway)</p>";
            }
        } catch (Exception $e) {
            echo "<p class='warning'>⚠️ Accessibility check error: " . $e->getMessage() . " (but allowing anyway)</p>";
        }
        
        // Test 5: DNS Resolution
        echo "<h3>Test 5: DNS Resolution</h3>";
        try {
            $ip = @gethostbyname($testDomain);
            if ($ip !== $testDomain) {
                echo "<p class='success'>✅ DNS resolves to: $ip</p>";
            } else {
                echo "<p class='warning'>⚠️ DNS resolution failed (but allowing anyway)</p>";
            }
        } catch (Exception $e) {
            echo "<p class='warning'>⚠️ DNS check error: " . $e->getMessage() . " (but allowing anyway)</p>";
        }
        
        // Test 6: Full API validation
        echo "<h3>Test 6: Full API Validation Test</h3>";
        
        $postData = json_encode([
            'domain' => $testDomain,
            'email' => $testEmail
        ]);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost/ssl4free/api/validate-domain.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
        
        if ($error) {
            echo "<p class='error'>❌ cURL Error: $error</p>";
        } else {
            echo "<p class='success'>✅ API call successful</p>";
            echo "<h4>API Response:</h4>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            
            $apiResult = json_decode($response, true);
            if (isset($apiResult['success']) && $apiResult['success']) {
                echo "<p class='success'>🎉 Full validation: SUCCESS</p>";
            } else {
                echo "<p class='error'>❌ Full validation: FAILED</p>";
                if (isset($apiResult['message'])) {
                    echo "<p class='error'>Error: " . htmlspecialchars($apiResult['message']) . "</p>";
                }
            }
        }
        
        echo "<h3>Summary</h3>";
        echo "<p>If all tests pass, the domain should work in SSL4Free.</p>";
        echo "<p>If some tests fail but show 'allowing anyway', it should still work.</p>";
    }
    ?>
    
    <h3>Test Domains to Try:</h3>
    <ul>
        <li><strong>test.jobzhit.com</strong> - Subdomain of existing domain</li>
        <li><strong>staging.jobzhit.com</strong> - Another subdomain</li>
        <li><strong>demo.example.com</strong> - Generic test domain</li>
        <li><strong>mytest.com</strong> - Simple domain</li>
    </ul>
    
    <p><a href="index.html">← Back to SSL4Free</a></p>
</body>
</html>
