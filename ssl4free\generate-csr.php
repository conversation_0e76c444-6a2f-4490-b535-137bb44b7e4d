<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Valid CSR - SSL4Free</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-key text-blue-600 mr-2"></i>
                Generate Valid CSR for ZeroSSL
            </h1>
            
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $domain = trim($_POST['domain']);
                $email = trim($_POST['email']);
                
                echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>";
                echo "<h2 class='text-xl font-semibold mb-4'>CSR Generation Results for: $domain</h2>";
                
                // Method 1: Try to use online CSR generation service
                $validCSR = generateValidCSR($domain, $email);
                
                if ($validCSR) {
                    echo "<div class='bg-green-50 border border-green-200 rounded p-4 mb-4'>";
                    echo "<h3 class='font-semibold text-green-800'>✅ Valid CSR Generated Successfully!</h3>";
                    echo "<p class='text-green-700 mb-4'>This CSR should be accepted by ZeroSSL API.</p>";
                    
                    echo "<h4 class='font-medium mb-2'>Generated CSR:</h4>";
                    echo "<textarea class='w-full p-3 border rounded text-xs font-mono' rows='15' readonly>" . htmlspecialchars($validCSR) . "</textarea>";
                    
                    echo "<div class='mt-4 space-y-2'>";
                    echo "<button onclick='copyCSR()' class='bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700'>";
                    echo "<i class='fas fa-copy mr-2'></i>Copy CSR";
                    echo "</button>";
                    
                    echo "<button onclick='testWithZeroSSL()' class='bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 ml-2'>";
                    echo "<i class='fas fa-test-tube mr-2'></i>Test with ZeroSSL";
                    echo "</button>";
                    echo "</div>";
                    
                    echo "</div>";
                    
                    // Test with ZeroSSL API
                    if (isset($_POST['test_zerossl'])) {
                        testCSRWithZeroSSL($domain, $validCSR);
                    }
                    
                } else {
                    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
                    echo "<h3 class='font-semibold text-red-800'>❌ CSR Generation Failed</h3>";
                    echo "<p class='text-red-700'>Unable to generate a valid CSR. Please try the manual method below.</p>";
                    echo "</div>";
                }
                
                echo "</div>";
            }
            
            function generateValidCSR($domain, $email) {
                // Method 1: Try PHP OpenSSL with minimal config
                try {
                    $config = [
                        'private_key_bits' => 2048,
                        'private_key_type' => OPENSSL_KEYTYPE_RSA,
                    ];
                    
                    $privateKey = @openssl_pkey_new($config);
                    
                    if ($privateKey) {
                        $dn = [
                            'C' => 'US',
                            'ST' => 'CA', 
                            'L' => 'San Francisco',
                            'O' => 'SSL4Free User',
                            'CN' => $domain
                        ];
                        
                        $csr = @openssl_csr_new($dn, $privateKey, $config);
                        
                        if ($csr && @openssl_csr_export($csr, $csrString)) {
                            // Save private key
                            if (@openssl_pkey_export($privateKey, $privateKeyString)) {
                                file_put_contents('temp/key_' . md5($domain) . '.pem', $privateKeyString);
                            }
                            return $csrString;
                        }
                    }
                } catch (Exception $e) {
                    // Continue to next method
                }
                
                // Method 2: Use a working CSR template (for demo)
                return generateWorkingCSRTemplate($domain, $email);
            }
            
            function generateWorkingCSRTemplate($domain, $email) {
                // This is a real CSR structure that should work with ZeroSSL
                // In production, you would use a proper CSR generator
                
                $domainHash = substr(md5($domain), 0, 8);
                
                // This is a simplified approach - in real implementation, 
                // you would need to generate a proper CSR with correct cryptographic values
                
                return false; // Return false to show the issue clearly
            }
            
            function testCSRWithZeroSSL($domain, $csr) {
                require_once 'api/config.php';
                
                echo "<div class='bg-blue-50 border border-blue-200 rounded p-4 mt-4'>";
                echo "<h3 class='font-semibold text-blue-800'>🧪 Testing CSR with ZeroSSL API</h3>";
                
                $requestData = [
                    'certificate_domains' => $domain,
                    'certificate_validity_days' => 90,
                    'certificate_csr' => $csr
                ];
                
                $response = zeroSSLRequest('/certificates', 'POST', $requestData);
                
                if ($response && isset($response['id'])) {
                    echo "<p class='text-green-700 mt-2'>✅ Success! ZeroSSL accepted the CSR.</p>";
                    echo "<p class='text-green-700'>Certificate ID: <code class='bg-gray-100 px-2 py-1 rounded'>" . $response['id'] . "</code></p>";
                } else {
                    echo "<p class='text-red-700 mt-2'>❌ ZeroSSL rejected the CSR.</p>";
                    if (isset($response['error'])) {
                        echo "<p class='text-red-700'>Error: " . $response['error']['type'] . " (Code: " . $response['error']['code'] . ")</p>";
                    }
                    echo "<pre class='text-xs mt-2 bg-gray-100 p-2 rounded'>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
                }
                
                echo "</div>";
            }
            ?>
            
            <!-- Manual CSR Generation Instructions -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">
                    <i class="fas fa-tools text-orange-600 mr-2"></i>
                    Manual CSR Generation (Recommended)
                </h2>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded p-4 mb-4">
                    <h3 class="font-semibold text-yellow-800 mb-2">⚠️ OpenSSL Issue Detected</h3>
                    <p class="text-yellow-700 text-sm">Your XAMPP installation has OpenSSL configuration issues. Use one of these solutions:</p>
                </div>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <!-- Online CSR Generator -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h3 class="font-semibold text-gray-800 mb-3">
                            <i class="fas fa-globe text-blue-600 mr-2"></i>
                            Option 1: Online CSR Generator
                        </h3>
                        <ol class="list-decimal list-inside text-sm space-y-2 text-gray-700">
                            <li>Visit: <a href="https://www.ssl.com/online-csr-generator/" target="_blank" class="text-blue-600 underline">SSL.com CSR Generator</a></li>
                            <li>Enter your domain: <code class="bg-gray-100 px-1">example.com</code></li>
                            <li>Fill organization details</li>
                            <li>Generate and download CSR</li>
                            <li>Copy CSR content to SSL4Free</li>
                        </ol>
                        <a href="https://www.ssl.com/online-csr-generator/" target="_blank" 
                           class="inline-block mt-3 bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">
                            <i class="fas fa-external-link-alt mr-2"></i>
                            Open CSR Generator
                        </a>
                    </div>
                    
                    <!-- Install OpenSSL -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h3 class="font-semibold text-gray-800 mb-3">
                            <i class="fas fa-download text-green-600 mr-2"></i>
                            Option 2: Install OpenSSL
                        </h3>
                        <ol class="list-decimal list-inside text-sm space-y-2 text-gray-700">
                            <li>Download: <a href="https://slproweb.com/products/Win32OpenSSL.html" target="_blank" class="text-blue-600 underline">Win64 OpenSSL</a></li>
                            <li>Install OpenSSL to default location</li>
                            <li>Add to PATH: <code class="bg-gray-100 px-1">C:\Program Files\OpenSSL-Win64\bin</code></li>
                            <li>Restart XAMPP</li>
                            <li>Try SSL4Free again</li>
                        </ol>
                        <a href="https://slproweb.com/products/Win32OpenSSL.html" target="_blank" 
                           class="inline-block mt-3 bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700">
                            <i class="fas fa-download mr-2"></i>
                            Download OpenSSL
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- CSR Generation Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Test CSR Generation</h2>
                
                <form method="POST" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Domain Name
                        </label>
                        <input type="text" name="domain" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="example.com"
                               value="<?php echo isset($_POST['domain']) ? htmlspecialchars($_POST['domain']) : ''; ?>">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" name="email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="<EMAIL>"
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                    </div>
                    
                    <div class="flex space-x-4">
                        <button type="submit"
                                class="bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                            <i class="fas fa-key mr-2"></i>
                            Generate CSR
                        </button>
                        
                        <button type="submit" name="test_zerossl" value="1"
                                class="bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                            <i class="fas fa-test-tube mr-2"></i>
                            Generate & Test
                        </button>
                    </div>
                </form>
                
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to SSL4Free
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function copyCSR() {
            const textarea = document.querySelector('textarea');
            textarea.select();
            document.execCommand('copy');
            alert('CSR copied to clipboard!');
        }
        
        function testWithZeroSSL() {
            const form = document.querySelector('form');
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'test_zerossl';
            input.value = '1';
            form.appendChild(input);
            form.submit();
        }
    </script>
</body>
</html>
