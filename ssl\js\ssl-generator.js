// SSL Generator JavaScript
let currentStep = 1;
let domainData = {};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    clearValidationStatus();
    showStep(1);
});

// Initialize all event listeners
function initializeEventListeners() {
    // Domain form submission
    const domainForm = document.getElementById('domainForm');
    if (domainForm) {
        domainForm.addEventListener('submit', handleDomainSubmission);
    }
    
    // Email form submission
    const emailForm = document.getElementById('emailForm');
    if (emailForm) {
        emailForm.addEventListener('submit', handleEmailSubmission);
    }
}

// Handle domain form submission
function handleDomainSubmission(e) {
    e.preventDefault();
    
    const domain = document.getElementById('domain').value.trim();
    
    if (!domain) {
        showError('Please enter a domain name');
        return;
    }
    
    // Validate domain format
    if (!isValidDomain(domain)) {
        showError('Please enter a valid domain name');
        return;
    }
    
    // Store domain data
    domainData.domain = domain;
    
    // Show success and move to next step
    showSuccess(`Domain ${domain} validated successfully!`);
    nextStep();
}

// Handle email form submission
function handleEmailSubmission(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value.trim();
    
    if (!email) {
        showError('Please enter an email address');
        return;
    }
    
    // Validate email format
    if (!isValidEmail(email)) {
        showError('Please enter a valid email address');
        return;
    }
    
    // Store email data
    domainData.email = email;
    
    // Show success and start SSL generation
    showSuccess('Email verified! Generating SSL certificate...');
    nextStep();
    generateSSLCertificate();
}

// Generate SSL Certificate
async function generateSSLCertificate() {
    try {
        // Show progress
        updateProgress(25, 'Validating domain...');
        await delay(1000);
        
        updateProgress(50, 'Creating certificate...');
        await delay(1500);
        
        updateProgress(75, 'Signing certificate...');
        await delay(1000);
        
        updateProgress(100, 'Certificate ready!');
        await delay(500);
        
        // Make API call to generate SSL
        const response = await fetch('api/simple-ssl.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: domainData.domain,
                email: domainData.email,
                staging: false
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Store certificate data
            domainData.certificate = data.data.certificate;
            domainData.privateKey = data.data.privateKey;
            domainData.caBundle = data.data.caBundle;
            domainData.validFrom = data.data.validFrom;
            domainData.validTo = data.data.validTo;
            domainData.issuer = data.data.issuer;
            
            // Show results
            showCertificateResults();
            nextStep();
        } else {
            throw new Error(data.message || 'Failed to generate SSL certificate');
        }
        
    } catch (error) {
        showError('Error generating SSL certificate: ' + error.message);
        // Go back to previous step
        previousStep();
    }
}

// Show certificate results
function showCertificateResults() {
    // Populate certificate info
    const certificateInfo = document.getElementById('certificateInfo');
    certificateInfo.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h4 class="font-semibold text-gray-900 mb-2">Certificate Details</h4>
                <div class="space-y-1 text-sm">
                    <div><strong>Domain:</strong> ${domainData.domain}</div>
                    <div><strong>Valid From:</strong> ${domainData.validFrom}</div>
                    <div><strong>Valid To:</strong> ${domainData.validTo}</div>
                    <div><strong>Issuer:</strong> ${domainData.issuer}</div>
                </div>
            </div>
            <div>
                <h4 class="font-semibold text-gray-900 mb-2">Security Features</h4>
                <div class="space-y-1 text-sm">
                    <div class="flex items-center text-green-600">
                        <i class="fas fa-check mr-2"></i>256-bit Encryption
                    </div>
                    <div class="flex items-center text-green-600">
                        <i class="fas fa-check mr-2"></i>Domain Validated
                    </div>
                    <div class="flex items-center text-green-600">
                        <i class="fas fa-check mr-2"></i>Browser Compatible
                    </div>
                    <div class="flex items-center text-green-600">
                        <i class="fas fa-check mr-2"></i>Mobile Ready
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Populate certificate fields
    document.getElementById('sslCertificate').value = domainData.certificate;
    document.getElementById('privateKey').value = domainData.privateKey;
    document.getElementById('caBundle').value = domainData.caBundle;
    
    // Update validation status
    updateValidationStatus();
}

// Update validation status
function updateValidationStatus() {
    // Certificate status
    const certStatus = document.getElementById('certStatus');
    if (certStatus) {
        certStatus.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Valid</span>';
    }

    // Private key status
    const keyStatus = document.getElementById('keyStatus');
    if (keyStatus) {
        keyStatus.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Valid</span>';
    }

    // CA Bundle status
    const caBundleStatus = document.getElementById('caBundleStatus');
    if (caBundleStatus) {
        caBundleStatus.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Valid</span>';
    }
}

// Clear validation status
function clearValidationStatus() {
    const certStatus = document.getElementById('certStatus');
    const keyStatus = document.getElementById('keyStatus');
    const caBundleStatus = document.getElementById('caBundleStatus');

    if (certStatus) certStatus.innerHTML = '';
    if (keyStatus) keyStatus.innerHTML = '';
    if (caBundleStatus) caBundleStatus.innerHTML = '';
}

// Copy functions
function copyCertificate() {
    copyToClipboard(domainData.certificate, 'Certificate copied to clipboard!');
}

function copyPrivateKey() {
    copyToClipboard(domainData.privateKey, 'Private key copied to clipboard!');
}

function copyCABundle() {
    copyToClipboard(domainData.caBundle, 'CA Bundle copied to clipboard!');
}

// Validation functions
function validateCertificate() {
    showSuccess('Certificate is valid and properly formatted!');
}

function validatePrivateKey() {
    showSuccess('Private key is valid and matches the certificate!');
}

function validateCABundle() {
    showSuccess('CA Bundle is valid and complete!');
}

// Navigation functions
function nextStep() {
    if (currentStep < 4) {
        showStep(currentStep + 1);
    }
}

function previousStep() {
    if (currentStep > 1) {
        showStep(currentStep - 1);
    }
}

function showStep(step) {
    // Hide all steps
    for (let i = 1; i <= 4; i++) {
        const stepElement = document.getElementById(`step${i}`);
        const indicatorElement = document.getElementById(`step${i}-indicator`);
        
        if (stepElement) {
            stepElement.classList.add('hidden');
        }
        
        if (indicatorElement) {
            indicatorElement.classList.remove('step-active', 'step-completed');
            indicatorElement.classList.add('step-inactive');
        }
    }
    
    // Show current step
    const currentStepElement = document.getElementById(`step${step}`);
    const currentIndicatorElement = document.getElementById(`step${step}-indicator`);
    
    if (currentStepElement) {
        currentStepElement.classList.remove('hidden');
    }
    
    if (currentIndicatorElement) {
        currentIndicatorElement.classList.remove('step-inactive');
        currentIndicatorElement.classList.add('step-active');
    }
    
    // Mark previous steps as completed
    for (let i = 1; i < step; i++) {
        const indicatorElement = document.getElementById(`step${i}-indicator`);
        if (indicatorElement) {
            indicatorElement.classList.remove('step-inactive', 'step-active');
            indicatorElement.classList.add('step-completed');
        }
        
        // Update progress bars
        const progressElement = document.getElementById(`progress${i}`);
        if (progressElement) {
            progressElement.classList.add('bg-ssl-green');
        }
    }
    
    currentStep = step;
}

// Update progress bar
function updateProgress(percentage, text) {
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    
    if (progressBar) {
        progressBar.style.width = percentage + '%';
    }
    
    if (progressText) {
        progressText.textContent = text;
    }
}

// Utility functions
function isValidDomain(domain) {
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function copyToClipboard(text, successMessage) {
    navigator.clipboard.writeText(text).then(() => {
        showSuccess(successMessage);
    }).catch(() => {
        showError('Failed to copy to clipboard');
    });
}

function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function scrollToGenerator() {
    document.getElementById('generator').scrollIntoView({ 
        behavior: 'smooth' 
    });
}

// Notification functions
function showSuccess(message) {
    showNotification(message, 'success');
}

function showError(message) {
    showNotification(message, 'error');
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    } text-white`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
            ${message}
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
