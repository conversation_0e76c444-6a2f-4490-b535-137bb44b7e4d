<?php
require_once 'api/config.php';

header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo json_encode(['success' => false, 'message' => 'Invalid JSON input']);
        exit();
    }
    
    // Validate required fields
    if (!isset($input['domain']) || !isset($input['email'])) {
        echo json_encode(['success' => false, 'message' => 'Missing required fields: domain and email']);
        exit();
    }
    
    $domain = trim($input['domain']);
    $email = trim($input['email']);
    $manualCSR = $input['manual_csr'] ?? null;
    
    // Validate inputs
    if (!validateDomain($domain) || !validateEmail($email)) {
        echo json_encode(['success' => false, 'message' => 'Invalid domain or email format']);
        exit();
    }
    
    // Generate or use manual CSR
    if ($manualCSR) {
        $csrData = $manualCSR;
        logMessage("Using manual CSR for domain: $domain", 'INFO');
    } else {
        $csrData = generateWorkingCSR($domain, $email);
        if (!$csrData) {
            echo json_encode(['success' => false, 'message' => 'CSR generation failed. Please use manual CSR.']);
            exit();
        }
        logMessage("Generated CSR automatically for domain: $domain", 'INFO');
    }
    
    // Create certificate with ZeroSSL using correct API format
    $requestData = [
        'certificate_domains' => $domain,
        'certificate_validity_days' => 90,
        'certificate_csr' => $csrData,
        'certificate_validation_method' => 'HTTP_CSR_HASH'
    ];
    
    logMessage("Creating certificate with ZeroSSL API for domain: $domain", 'INFO');
    logMessage("Request data: " . json_encode($requestData), 'DEBUG');
    
    $response = zeroSSLRequest('/certificates', 'POST', $requestData);
    
    if ($response && isset($response['id'])) {
        logMessage("Certificate created successfully with ID: " . $response['id'], 'INFO');
        
        echo json_encode([
            'success' => true,
            'message' => 'Certificate created successfully',
            'data' => [
                'certificate_id' => $response['id'],
                'status' => $response['status'] ?? 'draft',
                'common_name' => $response['common_name'] ?? $domain,
                'validation' => $response['validation'] ?? null
            ]
        ]);
    } else {
        logMessage("Certificate creation failed: " . json_encode($response), 'ERROR');
        
        $errorMessage = 'Certificate creation failed';
        $errorDetails = [];
        
        if (isset($response['error'])) {
            $error = $response['error'];
            $errorCode = $error['code'] ?? 0;
            $errorType = $error['type'] ?? 'unknown';
            
            $errorMessage = "ZeroSSL API Error: $errorType";
            $errorDetails = [
                'error_code' => $errorCode,
                'error_type' => $errorType,
                'raw_response' => $response
            ];
            
            // Handle specific errors
            if ($errorCode == 2839) {
                $errorMessage = 'Domain already has a certificate. Try a subdomain.';
            } elseif ($errorCode == 2817) {
                $errorMessage = 'Certificate limit reached. Try again tomorrow.';
            }
        }
        
        echo json_encode([
            'success' => false,
            'message' => $errorMessage,
            'details' => $errorDetails
        ]);
    }
    
} catch (Exception $e) {
    logMessage("Exception in certificate creation: " . $e->getMessage(), 'ERROR');
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error: ' . $e->getMessage()
    ]);
}

// Helper function to generate working CSR
function generateWorkingCSR($domain, $email) {
    try {
        $config = [
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ];
        
        $privateKey = @openssl_pkey_new($config);
        if (!$privateKey) {
            return false;
        }
        
        $dn = [
            'C' => 'US',
            'ST' => 'CA',
            'L' => 'SF',
            'O' => 'SSL4Free',
            'CN' => $domain
        ];
        
        $csr = @openssl_csr_new($dn, $privateKey, $config);
        if (!$csr) {
            return false;
        }
        
        if (@openssl_csr_export($csr, $csrString)) {
            // Save private key
            if (@openssl_pkey_export($privateKey, $privateKeyString)) {
                $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
                file_put_contents($keyFile, $privateKeyString);
            }
            return $csrString;
        }
        
        return false;
    } catch (Exception $e) {
        return false;
    }
}
?>
