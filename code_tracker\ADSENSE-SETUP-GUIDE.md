# AdSense Setup Guide for PakSim Website

## 📍 Ad Placement Locations

Your website now has **strategic ad placements** for maximum revenue:

### **1. Homepage (index.html)**
- **Top Banner** - After hero section (728x90 or Responsive)
- **Left Sidebar** - Next to "How to Use" section (300x250)
- **Right Sidebar** - Next to "How to Use" section (300x250)
- **Bottom Banner** - Before footer (728x90 or Responsive)

### **2. About Page (about.html)**
- **Top Banner** - After page header (728x90 or Responsive)

### **3. Contact Page (contact.html)**
- **Top Banner** - After header (728x90 or Responsive)

### **4. Other Pages**
- Similar ad placements can be added to FAQ, Blog, and SIM data pages

## 🔧 How to Add Your AdSense Code

### **Step 1: Get AdSense Approval**
1. Apply for Google AdSense at: https://www.google.com/adsense/
2. Wait for approval (usually 1-14 days)
3. Create ad units in your AdSense dashboard

### **Step 2: Replace Placeholder Code**
Look for these placeholder divs in your HTML files:

```html
<!-- Replace this div with your AdSense code -->
<div style="background: #e9ecef; border: 2px dashed #6c757d; padding: 60px 20px; border-radius: 10px; color: #6c757d;">
    <i class="fas fa-ad" style="font-size: 2rem; margin-bottom: 10px;"></i>
    <p style="margin: 0; font-size: 1.1rem; font-weight: 500;">AdSense Banner Ad (728x90 or Responsive)</p>
    <small style="opacity: 0.7;">Paste your AdSense code here</small>
</div>
```

### **Step 3: Replace with AdSense Code**
Replace the entire placeholder div with your AdSense code like this:

```html
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXX"
     crossorigin="anonymous"></script>
<!-- Banner Ad -->
<ins class="adsbygoogle"
     style="display:block"
     data-ad-client="ca-pub-XXXXXXXXXX"
     data-ad-slot="XXXXXXXXXX"
     data-ad-format="auto"
     data-full-width-responsive="true"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script>
```

## 📊 Recommended Ad Sizes

### **Banner Ads (Top/Bottom)**
- **728x90** (Leaderboard) - Desktop
- **320x50** (Mobile Banner) - Mobile
- **Responsive** - Adapts to screen size

### **Sidebar Ads**
- **300x250** (Medium Rectangle) - Most popular
- **300x600** (Half Page) - Higher revenue
- **160x600** (Wide Skyscraper) - Vertical

### **In-Content Ads**
- **336x280** (Large Rectangle)
- **300x250** (Medium Rectangle)
- **Responsive** - Best for mobile

## 💡 Best Practices

### **1. Ad Placement Strategy**
- Place ads where users naturally look
- Don't overwhelm with too many ads
- Keep content-to-ad ratio balanced

### **2. Mobile Optimization**
- Use responsive ad units
- Ensure ads don't block content
- Test on different screen sizes

### **3. Performance Tips**
- Use lazy loading for ads
- Monitor Core Web Vitals
- A/B test different placements

## 🚀 Revenue Optimization

### **1. High-Performing Locations**
- **Above the fold** - First screen view
- **End of articles** - Natural stopping point
- **Between content sections** - Non-intrusive

### **2. Ad Types to Try**
- **Display ads** - Standard banner/rectangle
- **In-feed ads** - Native-looking ads
- **In-article ads** - Within content
- **Matched content** - Related articles

### **3. Testing Strategy**
- Start with 2-3 ad units
- Monitor performance for 1 week
- Add more ads gradually
- Track revenue vs user experience

## 📈 Monitoring & Analytics

### **1. AdSense Reports**
- Check daily earnings
- Monitor CTR (Click-Through Rate)
- Track RPM (Revenue Per Mille)

### **2. Google Analytics**
- Monitor bounce rate
- Check page load speed
- Track user engagement

### **3. Key Metrics**
- **CTR**: 1-3% is good
- **RPM**: Varies by niche ($1-10+)
- **Viewability**: 50%+ is good

## 🔒 AdSense Policies

### **1. Content Guidelines**
- Original, high-quality content
- No prohibited content
- Regular content updates

### **2. Traffic Requirements**
- Organic traffic preferred
- No artificial traffic
- Minimum 1000 pageviews/month

### **3. Technical Requirements**
- Fast loading website
- Mobile-friendly design
- Good user experience

## 📞 Support

If you need help with AdSense setup:
- **Google AdSense Help**: https://support.google.com/adsense/
- **Community Forum**: https://support.google.com/adsense/community
- **YouTube Tutorials**: Search "AdSense setup guide"

---

**Note**: Replace all placeholder codes with your actual AdSense codes after approval. Monitor performance and adjust placements based on revenue and user experience.
