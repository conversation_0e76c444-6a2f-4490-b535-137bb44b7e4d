<?php
/**
 * Single Product Template
 *
 * @package OLX_Marketplace
 */

get_header(); ?>

<div class="container" style="padding: 40px 20px;">
    <?php while (have_posts()) : the_post(); ?>
        <div class="product-detail-container" style="display: grid; grid-template-columns: 1fr 400px; gap: 40px; max-width: 1200px; margin: 0 auto;">
            
            <!-- Product Images and Details -->
            <div class="product-main">
                <!-- Product Images -->
                <div class="product-images" style="margin-bottom: 30px;">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="main-image" style="margin-bottom: 15px;">
                            <?php the_post_thumbnail('large', array(
                                'style' => 'width: 100%; height: 400px; object-fit: cover; border-radius: 8px;'
                            )); ?>
                        </div>
                    <?php else : ?>
                        <div class="main-image" style="margin-bottom: 15px;">
                            <img src="https://via.placeholder.com/600x400/f0f0f0/666?text=No+Image" 
                                 alt="<?php echo esc_attr(get_the_title()); ?>"
                                 style="width: 100%; height: 400px; object-fit: cover; border-radius: 8px;">
                        </div>
                    <?php endif; ?>
                    
                    <!-- Additional images can be added here with a gallery -->
                    <?php
                    $gallery = get_post_meta(get_the_ID(), '_product_gallery', true);
                    if ($gallery) :
                        $gallery_ids = explode(',', $gallery);
                        if (count($gallery_ids) > 0) :
                    ?>
                        <div class="image-gallery" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px;">
                            <?php foreach ($gallery_ids as $image_id) : ?>
                                <img src="<?php echo esc_url(wp_get_attachment_image_url($image_id, 'thumbnail')); ?>" 
                                     alt="Product Image"
                                     style="width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;">
                            <?php endforeach; ?>
                        </div>
                    <?php 
                        endif;
                    endif; 
                    ?>
                </div>
                
                <!-- Product Description -->
                <div class="product-description" style="background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <h2 style="color: #002f34; margin-bottom: 20px; font-size: 24px;">Description</h2>
                    <div class="content" style="line-height: 1.6; color: #333;">
                        <?php the_content(); ?>
                    </div>
                    
                    <?php if (!get_the_content()) : ?>
                        <p style="color: #666; font-style: italic;">No description provided for this product.</p>
                    <?php endif; ?>
                </div>
                
                <!-- Related Products -->
                <?php
                $related_products = new WP_Query(array(
                    'post_type' => 'product',
                    'post_status' => 'publish',
                    'posts_per_page' => 4,
                    'post__not_in' => array(get_the_ID()),
                    'orderby' => 'rand'
                ));
                
                if ($related_products->have_posts()) :
                ?>
                <div class="related-products" style="margin-top: 40px;">
                    <h3 style="color: #002f34; margin-bottom: 20px; font-size: 20px;">Related Products</h3>
                    <div class="related-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <?php while ($related_products->have_posts()) : $related_products->the_post(); ?>
                            <div class="related-product" style="background: #fff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); cursor: pointer;" onclick="window.location.href='<?php echo esc_url(get_permalink()); ?>'">
                                <div class="related-image" style="height: 150px; overflow: hidden;">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('medium', array('style' => 'width: 100%; height: 100%; object-fit: cover;')); ?>
                                    <?php else : ?>
                                        <img src="https://via.placeholder.com/200x150/f0f0f0/666?text=No+Image" style="width: 100%; height: 100%; object-fit: cover;">
                                    <?php endif; ?>
                                </div>
                                <div class="related-info" style="padding: 15px;">
                                    <div class="related-price" style="font-weight: bold; color: #002f34; margin-bottom: 5px;">
                                        Rs <?php echo esc_html(number_format(get_post_meta(get_the_ID(), '_product_price', true))); ?>
                                    </div>
                                    <div class="related-title" style="font-size: 14px; color: #333;">
                                        <?php echo esc_html(wp_trim_words(get_the_title(), 6)); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                </div>
                <?php 
                wp_reset_postdata();
                endif; 
                ?>
            </div>
            
            <!-- Sidebar with Product Info and Contact -->
            <div class="product-sidebar">
                <!-- Product Info Card -->
                <div class="product-info-card" style="background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px;">
                    <h1 style="color: #002f34; margin-bottom: 15px; font-size: 24px; line-height: 1.3;">
                        <?php the_title(); ?>
                    </h1>
                    
                    <div class="price" style="font-size: 28px; font-weight: bold; color: #002f34; margin-bottom: 20px;">
                        <?php 
                        $price = get_post_meta(get_the_ID(), '_product_price', true);
                        echo $price ? 'Rs ' . number_format($price) : 'Price on request';
                        ?>
                    </div>
                    
                    <div class="product-meta" style="margin-bottom: 20px;">
                        <div class="meta-item" style="display: flex; align-items: center; margin-bottom: 10px; color: #666;">
                            <i class="fas fa-map-marker-alt" style="margin-right: 8px; color: #002f34;"></i>
                            <?php 
                            $location = get_post_meta(get_the_ID(), '_product_location', true);
                            echo esc_html($location ? $location : 'Location not specified');
                            ?>
                        </div>
                        
                        <div class="meta-item" style="display: flex; align-items: center; margin-bottom: 10px; color: #666;">
                            <i class="fas fa-clock" style="margin-right: 8px; color: #002f34;"></i>
                            <?php echo esc_html(human_time_diff(get_the_time('U'), current_time('timestamp')) . ' ago'); ?>
                        </div>
                        
                        <?php 
                        $condition = get_post_meta(get_the_ID(), '_product_condition', true);
                        if ($condition) :
                        ?>
                        <div class="meta-item" style="display: flex; align-items: center; margin-bottom: 10px; color: #666;">
                            <i class="fas fa-info-circle" style="margin-right: 8px; color: #002f34;"></i>
                            Condition: <?php echo esc_html(ucfirst($condition)); ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php
                        $categories = get_the_terms(get_the_ID(), 'product_category');
                        if ($categories && !is_wp_error($categories)) :
                        ?>
                        <div class="meta-item" style="display: flex; align-items: center; margin-bottom: 10px; color: #666;">
                            <i class="fas fa-tag" style="margin-right: 8px; color: #002f34;"></i>
                            <?php echo esc_html($categories[0]->name); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="action-buttons" style="margin-bottom: 20px;">
                        <button class="btn-primary" style="width: 100%; margin-bottom: 10px; background: #002f34; color: white; border: none; padding: 12px; border-radius: 4px; font-size: 16px; cursor: pointer;" onclick="showContactInfo()">
                            <i class="fas fa-phone"></i> Show Contact Info
                        </button>
                        
                        <button class="btn-secondary" style="width: 100%; margin-bottom: 10px; background: #fff; color: #002f34; border: 2px solid #002f34; padding: 12px; border-radius: 4px; font-size: 16px; cursor: pointer;" onclick="toggleFavorite(<?php echo get_the_ID(); ?>)">
                            <i class="fas fa-heart"></i> Add to Favorites
                        </button>
                        
                        <button class="btn-secondary" style="width: 100%; background: #fff; color: #002f34; border: 2px solid #002f34; padding: 12px; border-radius: 4px; font-size: 16px; cursor: pointer;" onclick="shareProduct()">
                            <i class="fas fa-share"></i> Share
                        </button>
                    </div>
                </div>
                
                <!-- Contact Info Card (Hidden by default) -->
                <div id="contactInfo" class="contact-info-card" style="background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; display: none;">
                    <h3 style="color: #002f34; margin-bottom: 15px;">Contact Information</h3>
                    
                    <?php 
                    $contact_phone = get_post_meta(get_the_ID(), '_contact_phone', true);
                    $contact_email = get_post_meta(get_the_ID(), '_contact_email', true);
                    $author_id = get_the_author_meta('ID');
                    ?>
                    
                    <?php if ($contact_phone) : ?>
                    <div class="contact-item" style="margin-bottom: 15px;">
                        <strong>Phone:</strong><br>
                        <a href="tel:<?php echo esc_attr($contact_phone); ?>" style="color: #002f34; text-decoration: none; font-size: 18px;">
                            <?php echo esc_html($contact_phone); ?>
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($contact_email) : ?>
                    <div class="contact-item" style="margin-bottom: 15px;">
                        <strong>Email:</strong><br>
                        <a href="mailto:<?php echo esc_attr($contact_email); ?>" style="color: #002f34; text-decoration: none;">
                            <?php echo esc_html($contact_email); ?>
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <div class="contact-item">
                        <strong>Seller:</strong><br>
                        <?php echo esc_html(get_the_author_meta('display_name', $author_id)); ?>
                    </div>
                </div>
                
                <!-- Safety Tips -->
                <div class="safety-tips" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h4 style="color: #856404; margin-bottom: 10px;"><i class="fas fa-shield-alt"></i> Safety Tips</h4>
                    <ul style="color: #856404; font-size: 14px; margin: 0; padding-left: 20px;">
                        <li>Meet in a public place</li>
                        <li>Check the item before payment</li>
                        <li>Pay only after receiving the item</li>
                        <li>Beware of unrealistic offers</li>
                    </ul>
                </div>
            </div>
        </div>
    <?php endwhile; ?>
</div>

<script>
function showContactInfo() {
    const contactInfo = document.getElementById('contactInfo');
    if (contactInfo.style.display === 'none' || contactInfo.style.display === '') {
        contactInfo.style.display = 'block';
        contactInfo.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    } else {
        contactInfo.style.display = 'none';
    }
}

function toggleFavorite(productId) {
    // This would integrate with WordPress user meta or cookies
    alert('Favorite functionality will be implemented with user accounts.');
}

function shareProduct() {
    if (navigator.share) {
        navigator.share({
            title: '<?php echo esc_js(get_the_title()); ?>',
            text: 'Check out this product on <?php echo esc_js(get_bloginfo('name')); ?>',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(function() {
            alert('Product link copied to clipboard!');
        });
    }
}

// Image gallery functionality
document.addEventListener('DOMContentLoaded', function() {
    const galleryImages = document.querySelectorAll('.image-gallery img');
    const mainImage = document.querySelector('.main-image img');
    
    galleryImages.forEach(function(img) {
        img.addEventListener('click', function() {
            const newSrc = this.src.replace('thumbnail', 'large');
            mainImage.src = newSrc;
        });
    });
});
</script>

<!-- Responsive Styles -->
<style>
@media (max-width: 768px) {
    .product-detail-container {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }
    
    .product-sidebar {
        order: -1;
    }
    
    .main-image img {
        height: 250px !important;
    }
    
    .related-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}
</style>

<?php get_footer(); ?>
