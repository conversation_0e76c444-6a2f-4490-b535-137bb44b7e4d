# WhatsApp Widget Screenshots Optimizer for CodeCanyon
# This script optimizes screenshots according to CodeCanyon requirements

$screenshotFolder = "C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete\WhatsApp_Widget_Screenshots"

Write-Host "🖼️ WhatsApp Widget Screenshots Optimizer" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Check if folder exists
if (Test-Path $screenshotFolder) {
    Write-Host "📁 Found screenshots folder" -ForegroundColor Cyan
    
    # Get all PNG files
    $screenshots = Get-ChildItem -Path $screenshotFolder -Filter "*.png"
    
    Write-Host "📸 Found $($screenshots.Count) screenshot files" -ForegroundColor Yellow
    
    # CodeCanyon Requirements Check
    Write-Host "`n🎯 CodeCanyon Screenshot Requirements:" -ForegroundColor Magenta
    Write-Host "   • Maximum size: 1 MB per image" -ForegroundColor White
    Write-Host "   • Recommended: 500-800 KB per image" -ForegroundColor White
    Write-Host "   • Format: PNG or JPG" -ForegroundColor White
    Write-Host "   • Minimum: 6 screenshots" -ForegroundColor White
    Write-Host "   • Maximum: 20 screenshots" -ForegroundColor White
    
    Write-Host "`n📊 Current Screenshot Analysis:" -ForegroundColor Cyan
    
    $totalSize = 0
    $oversizedFiles = @()
    
    foreach ($file in $screenshots) {
        $sizeKB = [Math]::Round($file.Length / 1KB, 1)
        $sizeMB = [Math]::Round($file.Length / 1MB, 2)
        $totalSize += $file.Length
        
        $status = "✅ OK"
        $color = "Green"
        
        if ($sizeMB -gt 1) {
            $status = "❌ TOO LARGE"
            $color = "Red"
            $oversizedFiles += $file
        } elseif ($sizeKB -gt 800) {
            $status = "⚠️ LARGE"
            $color = "Yellow"
        }
        
        Write-Host "   📸 $($file.Name)" -ForegroundColor White
        Write-Host "      Size: $sizeKB KB ($sizeMB MB) - $status" -ForegroundColor $color
    }
    
    $totalSizeMB = [Math]::Round($totalSize / 1MB, 2)
    Write-Host "`n📊 Total Screenshots Size: $totalSizeMB MB" -ForegroundColor Cyan
    
    # Recommendations
    Write-Host "`n💡 Recommendations:" -ForegroundColor Yellow
    
    if ($screenshots.Count -lt 6) {
        Write-Host "   ⚠️ Need at least 6 screenshots for CodeCanyon" -ForegroundColor Red
        Write-Host "   📸 Missing: Frontend widget views (desktop & mobile)" -ForegroundColor Yellow
    } elseif ($screenshots.Count -ge 6) {
        Write-Host "   ✅ Screenshot count is good ($($screenshots.Count) files)" -ForegroundColor Green
    }
    
    if ($oversizedFiles.Count -gt 0) {
        Write-Host "   ❌ $($oversizedFiles.Count) files are too large (>1MB)" -ForegroundColor Red
        Write-Host "   🔧 Consider compressing these files" -ForegroundColor Yellow
    } else {
        Write-Host "   ✅ All files are within size limits" -ForegroundColor Green
    }
    
    if ($totalSizeMB -gt 10) {
        Write-Host "   ⚠️ Total size is large ($totalSizeMB MB)" -ForegroundColor Yellow
        Write-Host "   💡 Consider optimizing for faster uploads" -ForegroundColor Cyan
    }
    
    # CodeCanyon Status
    Write-Host "`n🎯 CodeCanyon Readiness:" -ForegroundColor Magenta
    
    $readyForCodeCanyon = $true
    
    if ($screenshots.Count -lt 6) {
        Write-Host "   ❌ Not enough screenshots" -ForegroundColor Red
        $readyForCodeCanyon = $false
    }
    
    if ($oversizedFiles.Count -gt 0) {
        Write-Host "   ❌ Some files too large" -ForegroundColor Red
        $readyForCodeCanyon = $false
    }
    
    if ($readyForCodeCanyon) {
        Write-Host "   🎉 READY FOR CODECANYON SUBMISSION!" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️ Needs optimization before submission" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "❌ Screenshots folder not found: $screenshotFolder" -ForegroundColor Red
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
