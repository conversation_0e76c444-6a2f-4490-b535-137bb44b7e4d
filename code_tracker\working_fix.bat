@echo off
echo 🚀 WORKING CodeCanyon Fixer
echo ==========================

set "FINAL_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_WORKING_FIX"
set "SOURCE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete"

echo 🗑️ Cleaning old directory...
if exist "%FINAL_DIR%" rmdir /s /q "%FINAL_DIR%"

echo 📁 Creating structure...
mkdir "%FINAL_DIR%"
mkdir "%FINAL_DIR%\Thumbnail"
mkdir "%FINAL_DIR%\Theme_Preview"
mkdir "%FINAL_DIR%\WordPress_Theme"

echo 📦 Creating CORRECT WordPress ZIP structure...
mkdir "%FINAL_DIR%\temp_plugin"
mkdir "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro"

echo 📄 Copying plugin files to FOLDER structure...
copy "%SOURCE_DIR%\whatsapp-chat-widget.php" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\style.css" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\script.js" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\admin-style.css" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\admin-script.js" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\readme.txt" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\LICENSE.txt" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\INSTALLATION-GUIDE.md" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\FAQ.md" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\DEVELOPER-GUIDE.md" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\" >nul

echo 🌐 Copying demo folder...
if exist "%SOURCE_DIR%\demo" (
    xcopy "%SOURCE_DIR%\demo" "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\" /E /I /Q >nul
)

echo 📦 Creating ZIP with SINGLE TOP-LEVEL FOLDER...
powershell -Command "Compress-Archive -Path '%FINAL_DIR%\temp_plugin\whatsapp-widget-pro' -DestinationPath '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -Force"

echo 🗑️ Cleaning temp files...
rmdir /s /q "%FINAL_DIR%\temp_plugin"

echo ✅ WordPress ZIP structure FIXED!

echo 📸 Copying screenshots for conversion...
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\01_General_Settings_Tab.png" "%FINAL_DIR%\Theme_Preview\temp_01.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\02_Agents_Management_Tab.png" "%FINAL_DIR%\Theme_Preview\temp_02.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\03_Appearance_Themes_Tab.png" "%FINAL_DIR%\Theme_Preview\temp_03.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\04_Working_Hours_Tab.png" "%FINAL_DIR%\Theme_Preview\temp_04.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\05_Advanced_Settings_Tab.png" "%FINAL_DIR%\Theme_Preview\temp_05.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\06_Preview_Tab.png" "%FINAL_DIR%\Theme_Preview\temp_06.png" >nul

echo 🖼️ Copying thumbnail for resize...
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\01_General_Settings_Tab.png" "%FINAL_DIR%\Thumbnail\temp_thumbnail.png" >nul

echo ✅ MAJOR ISSUES FIXED!
echo.
echo 📋 WHAT'S BEEN FIXED:
echo    ✅ WordPress ZIP: Now contains SINGLE TOP-LEVEL FOLDER
echo    ✅ File structure: Proper organization
echo    ✅ All plugin files: Copied correctly
echo.
echo ⚠️ 2 QUICK MANUAL STEPS REMAINING:
echo.
echo 1. THUMBNAIL (2 minutes):
echo    📁 Go to: %FINAL_DIR%\Thumbnail\
echo    🌐 Use: https://www.canva.com/create/thumbnails/
echo    📐 Create: 80x80 pixels, green background, "WA Pro" text
echo    💾 Save as: thumbnail.png
echo    🗑️ Delete: temp_thumbnail.png
echo.
echo 2. SCREENSHOTS (3 minutes):
echo    📁 Go to: %FINAL_DIR%\Theme_Preview\
echo    🌐 Use: https://convertio.co/png-jpg/
echo    🔄 Convert temp_01.png to 01_general_settings.jpg
echo    🔄 Convert temp_02.png to 02_agents_management.jpg
echo    🔄 Convert temp_03.png to 03_appearance_themes.jpg
echo    🔄 Convert temp_04.png to 04_working_hours.jpg
echo    🔄 Convert temp_05.png to 05_advanced_settings.jpg
echo    🔄 Convert temp_06.png to 06_preview_tab.jpg
echo    🗑️ Delete all temp_*.png files
echo.
echo 3. FORM SETTINGS (30 seconds):
echo    ✅ Compatible Browsers: Select ALL
echo    ✅ ThemeForest Files: CSS, JS, PHP
echo.
echo 📁 Location: %FINAL_DIR%
echo 🚀 Total time: 5.5 minutes to complete!
echo.
echo 🎯 After these steps, ALL issues will be resolved!
pause
