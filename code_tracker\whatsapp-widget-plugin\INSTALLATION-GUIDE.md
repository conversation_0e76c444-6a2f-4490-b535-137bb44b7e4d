# WhatsApp Widget Pro - Installation Guide

## Table of Contents
1. [System Requirements](#system-requirements)
2. [Installation Methods](#installation-methods)
3. [Initial Setup](#initial-setup)
4. [Configuration Guide](#configuration-guide)
5. [Troubleshooting](#troubleshooting)

## System Requirements

### Minimum Requirements
- **WordPress:** 5.0 or higher
- **PHP:** 7.4 or higher
- **MySQL:** 5.6 or higher
- **Memory:** 128MB RAM
- **Disk Space:** 5MB free space

### Recommended Requirements
- **WordPress:** 6.0 or higher
- **PHP:** 8.0 or higher
- **MySQL:** 8.0 or higher
- **Memory:** 256MB RAM
- **SSL Certificate:** For secure WhatsApp links

### Browser Compatibility
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## Installation Methods

### Method 1: WordPress Admin (Recommended)

1. **Login to WordPress Admin**
   - Go to your WordPress admin panel
   - Navigate to `Plugins > Add New`

2. **Search for Plugin**
   - Search for "WhatsApp Widget Pro"
   - Click "Install Now" on the plugin

3. **Activate Plugin**
   - Click "Activate" after installation
   - You'll see a success message

### Method 2: Upload ZIP File

1. **Download Plugin**
   - Download the plugin ZIP file
   - Keep the file on your computer

2. **Upload via Admin**
   - Go to `Plugins > Add New`
   - Click "Upload Plugin"
   - Choose the ZIP file and click "Install Now"
   - Click "Activate Plugin"

### Method 3: FTP Upload

1. **Extract Files**
   - Extract the plugin ZIP file
   - You'll get a folder named `whatsapp-widget-pro`

2. **Upload via FTP**
   - Connect to your server via FTP
   - Navigate to `/wp-content/plugins/`
   - Upload the `whatsapp-widget-pro` folder

3. **Activate Plugin**
   - Go to WordPress admin `Plugins` page
   - Find "WhatsApp Widget Pro" and click "Activate"

## Initial Setup

### Step 1: Access Settings
1. After activation, go to `Settings > WhatsApp Widget`
2. You'll see the professional admin dashboard

### Step 2: Basic Configuration
1. **Enable Widget**
   - Toggle "Enable WhatsApp Widget" to ON
   - Choose device visibility (Desktop/Mobile)

2. **Add Phone Number**
   - Enter your WhatsApp number with country code
   - Example: `+************` (Pakistan)
   - Example: `+14155552671` (USA)

3. **Set Default Message**
   - Enter the message users will see
   - Example: "Hello! I need help with..."

4. **Choose Position & Size**
   - Select widget position (bottom-right recommended)
   - Choose size (Medium 60px recommended)

### Step 3: Save Settings
- Click "Save All Settings"
- Check your website to see the widget

## Configuration Guide

### General Settings Tab

**Widget Status**
- Enable/disable the widget globally
- Control visibility on desktop and mobile
- Useful for maintenance or testing

**Contact Information**
- Primary WhatsApp number (required)
- Default message for all conversations
- Include country code for international numbers

**Position & Size**
- 4 position options: bottom-right, bottom-left, top-right, top-left
- 4 size options: 50px (small) to 80px (extra large)
- Consider mobile users when choosing size

### Agents Tab

**Adding Agents**
1. Click "Add New Agent"
2. Fill in agent details:
   - Name (displayed to users)
   - WhatsApp number (with country code)
   - Job title (e.g., "Customer Support")
   - Avatar URL (optional, for profile picture)
3. Enable/disable individual agents

**Agent Management**
- Unlimited agents supported
- Each agent can be enabled/disabled
- Users can choose which agent to contact
- Great for departments (Sales, Support, etc.)

### Appearance Tab

**Theme Selection**
- **Default:** Classic green WhatsApp theme
- **Minimal:** Clean white design with green border
- **Dark:** Dark theme for modern websites
- **Gradient:** Gradient background effect
- **Square:** Rounded square instead of circle
- **Pulse:** Continuous pulsing animation

**Chat Bubble Interface**
- Enable for multi-agent selection
- Shows agent list when clicked
- Customizable title and subtitle
- Professional appearance for businesses

**Custom CSS**
- Add your own styles
- Override default appearance
- Use `!important` for strong overrides
- Preview changes in real-time

### Working Hours Tab

**Enable Working Hours**
- Show/hide widget based on business hours
- Automatic online/offline indicators
- Custom offline messages

**Timezone Configuration**
- Select your business timezone
- Supports major timezones worldwide
- Accurate time calculations

**Weekly Schedule**
- Set hours for each day of the week
- Enable/disable specific days
- Different hours for different days
- Quick actions: Enable All, Weekdays Only

### Advanced Tab

**Shortcode Usage**
```
[whatsapp_widget]
[whatsapp_widget phone="+************" message="Custom message"]
[whatsapp_widget size="70" position="static"]
```

**PHP Integration**
```php
<?php echo do_shortcode('[whatsapp_widget]'); ?>
```

**JavaScript Events**
```javascript
// Custom tracking
window.whatsappWidgetCustomTrack = function(category, action, label) {
    // Your custom code here
};
```

### Preview Tab

**Live Preview**
- See changes in real-time
- Test different settings
- Mobile/desktop preview

**Quick Actions**
- Test widget functionality
- Export settings for backup
- Settings summary overview

## Troubleshooting

### Widget Not Appearing

**Check Plugin Status**
1. Ensure plugin is activated
2. Check if widget is enabled in settings
3. Verify device visibility settings

**Check Theme Compatibility**
1. Switch to default WordPress theme temporarily
2. If widget appears, there's a theme conflict
3. Contact theme developer for support

**Clear Cache**
1. Clear any caching plugins
2. Clear browser cache
3. Check in incognito/private mode

### WhatsApp Link Not Working

**Phone Number Format**
- Must include country code
- No spaces or special characters except +
- Example: `+************` ✓
- Example: `92 300 1234567` ✗

**Message Encoding**
- Special characters may cause issues
- Keep messages simple and clear
- Test with basic messages first

### Styling Issues

**CSS Conflicts**
1. Check browser developer tools
2. Look for CSS conflicts
3. Use custom CSS to override
4. Add `!important` if needed

**Mobile Display**
1. Test on actual mobile devices
2. Check responsive settings
3. Adjust size for mobile screens

### Performance Issues

**Optimize Loading**
1. Use caching plugins
2. Optimize images (avatars)
3. Minimize custom CSS
4. Enable GZIP compression

**Database Optimization**
1. Clean old analytics data periodically
2. Optimize database tables
3. Use database optimization plugins

### Analytics Not Working

**Check Settings**
1. Ensure analytics is enabled
2. Verify AJAX functionality
3. Check browser console for errors

**Database Issues**
1. Check database permissions
2. Verify table creation
3. Contact hosting provider if needed

## Support

### Getting Help

**Documentation**
- Check this installation guide
- Review FAQ section
- Check WordPress.org support forum

**Premium Support**
- Available for licensed users
- Priority response times
- Advanced troubleshooting

**Community Support**
- WordPress.org support forum
- User community discussions
- Share tips and tricks

### Reporting Issues

**Before Reporting**
1. Check troubleshooting section
2. Test with default theme
3. Disable other plugins temporarily
4. Clear all caches

**Information to Include**
- WordPress version
- PHP version
- Plugin version
- Theme name and version
- Error messages (if any)
- Steps to reproduce issue

---

**Need more help?** Contact our support team or visit the documentation for advanced configuration options.
