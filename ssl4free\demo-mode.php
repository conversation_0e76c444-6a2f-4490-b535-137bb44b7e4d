<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL4Free - Demo Mode</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-certificate text-green-600 mr-2"></i>
                SSL4Free - Demo Mode (Certificate Limit Reached)
            </h1>
            
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $domain = trim($_POST['domain']);
                $email = trim($_POST['email']);
                
                echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>";
                echo "<h2 class='text-xl font-semibold mb-4'>Demo: SSL Certificate Process for: $domain</h2>";
                
                // Simulate the certificate creation process
                echo "<div class='space-y-4'>";
                
                // Step 1: CSR Generation
                echo "<div class='bg-green-50 border border-green-200 rounded p-4'>";
                echo "<h3 class='font-semibold text-green-800'>✅ Step 1: CSR Generated Successfully</h3>";
                echo "<p class='text-green-700'>Certificate Signing Request created for domain: $domain</p>";
                echo "<p class='text-green-700 text-sm'>CSR Length: 1,024 characters</p>";
                echo "</div>";
                
                // Step 2: Certificate Creation (Demo)
                echo "<div class='bg-blue-50 border border-blue-200 rounded p-4'>";
                echo "<h3 class='font-semibold text-blue-800'>🎭 Step 2: Demo Certificate Created</h3>";
                echo "<p class='text-blue-700'>Demo Certificate ID: <code class='bg-gray-100 px-2 py-1 rounded'>demo_" . substr(md5($domain), 0, 8) . "</code></p>";
                echo "<p class='text-blue-700'>Status: Ready for verification</p>";
                echo "<p class='text-blue-700'>Validity: 90 days</p>";
                echo "</div>";
                
                // Step 3: Verification Methods
                echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4'>";
                echo "<h3 class='font-semibold text-yellow-800'>📋 Step 3: Domain Verification Required</h3>";
                
                echo "<div class='mt-4 space-y-4'>";
                
                // HTTP File Verification
                echo "<div class='border border-gray-200 rounded p-3'>";
                echo "<h4 class='font-medium text-blue-800 mb-2'>Option 1: HTTP File Verification</h4>";
                echo "<p class='text-sm mb-2'>1. Create this file on your website:</p>";
                echo "<code class='bg-gray-100 px-2 py-1 rounded block mb-2 text-xs'>http://$domain/.well-known/pki-validation/DEMO_" . strtoupper(substr(md5($domain), 0, 16)) . ".txt</code>";
                echo "<p class='text-sm mb-2'>2. File content:</p>";
                echo "<textarea class='w-full p-2 border rounded text-xs font-mono' rows='3' readonly>DEMO_VALIDATION_CONTENT_" . strtoupper(substr(md5($domain . $email), 0, 32)) . "\ncomodoca.com\ndemo_validation_token</textarea>";
                echo "</div>";
                
                // DNS CNAME Verification
                echo "<div class='border border-gray-200 rounded p-3'>";
                echo "<h4 class='font-medium text-green-800 mb-2'>Option 2: DNS CNAME Verification</h4>";
                echo "<p class='text-sm mb-2'>Add this CNAME record to your DNS:</p>";
                echo "<div class='bg-gray-100 p-2 rounded text-sm'>";
                echo "<p><strong>Name:</strong> <code>_DEMO_" . strtoupper(substr(md5($domain), 0, 16)) . ".$domain</code></p>";
                echo "<p><strong>Value:</strong> <code>DEMO_VALIDATION_" . strtoupper(substr(md5($domain . $email), 0, 16)) . ".comodoca.com</code></p>";
                echo "</div>";
                echo "</div>";
                
                echo "</div>";
                echo "</div>";
                
                // Step 4: Next Steps
                echo "<div class='bg-purple-50 border border-purple-200 rounded p-4'>";
                echo "<h3 class='font-semibold text-purple-800'>🚀 Step 4: What Happens Next (In Real Mode)</h3>";
                echo "<ol class='list-decimal list-inside text-purple-700 space-y-1 text-sm'>";
                echo "<li>Complete domain verification using one of the methods above</li>";
                echo "<li>Wait 5-10 minutes for certificate authority to verify</li>";
                echo "<li>Certificate gets automatically issued</li>";
                echo "<li>Download certificate files (certificate.crt, ca_bundle.crt, private.key)</li>";
                echo "<li>Install certificate on your web server</li>";
                echo "</ol>";
                echo "</div>";
                
                // Demo Limitation Notice
                echo "<div class='bg-red-50 border border-red-200 rounded p-4 mt-4'>";
                echo "<h3 class='font-semibold text-red-800'>⚠️ Demo Mode Active</h3>";
                echo "<p class='text-red-700 text-sm mb-2'>This is a demonstration because:</p>";
                echo "<ul class='list-disc list-inside text-red-700 text-sm space-y-1'>";
                echo "<li>ZeroSSL certificate limit has been reached for today</li>";
                echo "<li>Real certificates cannot be created until limit resets</li>";
                echo "<li>The process shown above is exactly how it works in real mode</li>";
                echo "</ul>";
                echo "<p class='text-red-700 text-sm mt-2'><strong>Solution:</strong> Try again tomorrow or use a different certificate provider.</p>";
                echo "</div>";
                
                echo "</div>";
                echo "</div>";
            }
            ?>
            
            <!-- Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Demo: SSL Certificate Generation Process</h2>
                
                <div class="bg-blue-50 border border-blue-200 rounded p-4 mb-6">
                    <h3 class="font-semibold text-blue-800 mb-2">🎭 Demo Mode</h3>
                    <p class="text-blue-700 text-sm">This demo shows exactly how SSL4Free works when certificate limits are not reached.</p>
                </div>
                
                <form method="POST" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Domain Name
                        </label>
                        <input type="text" name="domain" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="example.com"
                               value="<?php echo isset($_POST['domain']) ? htmlspecialchars($_POST['domain']) : ''; ?>">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" name="email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="<EMAIL>"
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                    </div>
                    
                    <button type="submit"
                            class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors">
                        <i class="fas fa-play mr-2"></i>
                        Run Demo Process
                    </button>
                </form>
                
                <div class="mt-6 pt-4 border-t border-gray-200 space-y-2">
                    <a href="index.html" class="block text-blue-600 hover:text-blue-800">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to SSL4Free
                    </a>
                    <a href="debug-certificate.php" class="block text-gray-600 hover:text-gray-800">
                        <i class="fas fa-bug mr-2"></i>
                        Debug Certificate Creation
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
