@echo off
echo 📦 WhatsApp Widget Pro - Complete Package Creator
echo ================================================

set "SOURCE_DIR=%CD%\whatsapp-widget-plugin"
set "DEST_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete"

echo 📁 Creating destination folder...
if not exist "%DEST_DIR%" mkdir "%DEST_DIR%"

echo 📄 Copying core plugin files...
copy "%SOURCE_DIR%\whatsapp-chat-widget.php" "%DEST_DIR%\" >nul
copy "%SOURCE_DIR%\style.css" "%DEST_DIR%\" >nul
copy "%SOURCE_DIR%\script.js" "%DEST_DIR%\" >nul
copy "%SOURCE_DIR%\admin-style.css" "%DEST_DIR%\" >nul
copy "%SOURCE_DIR%\admin-script.js" "%DEST_DIR%\" >nul

echo 📖 Copying documentation files...
copy "%SOURCE_DIR%\readme.txt" "%DEST_DIR%\" >nul
copy "%SOURCE_DIR%\INSTALLATION-GUIDE.md" "%DEST_DIR%\" >nul
copy "%SOURCE_DIR%\FAQ.md" "%DEST_DIR%\" >nul
copy "%SOURCE_DIR%\DEVELOPER-GUIDE.md" "%DEST_DIR%\" >nul
copy "%SOURCE_DIR%\CODECANYON-README.md" "%DEST_DIR%\" >nul
copy "%SOURCE_DIR%\LICENSE.txt" "%DEST_DIR%\" >nul

echo 🌐 Creating demo folder...
if not exist "%DEST_DIR%\demo" mkdir "%DEST_DIR%\demo"
copy "%SOURCE_DIR%\demo\index.html" "%DEST_DIR%\demo\" >nul

echo 📋 Adding package info...
copy "PACKAGE-INFO.md" "%DEST_DIR%\" >nul

echo ✅ Package creation completed!
echo 📁 Location: %DEST_DIR%

echo.
echo 📋 Package Contents:
dir "%DEST_DIR%" /b

echo.
echo 🎉 WhatsApp Widget Pro package ready for CodeCanyon!
pause
