# WhatsApp Widget Screenshots Renamer
# This script renames screenshots to professional names

$folderPath = "C:\Users\<USER>\Desktop\WhatsApp_Widget_Screenshots"

# Check if folder exists
if (Test-Path $folderPath) {
    Write-Host "📁 Found folder: $folderPath" -ForegroundColor Green
    
    # Get all PNG files sorted by creation time
    $files = Get-ChildItem -Path $folderPath -Filter "*.png" | Sort-Object CreationTime
    
    Write-Host "📸 Found $($files.Count) screenshot files" -ForegroundColor Cyan
    
    # Professional naming scheme for WhatsApp Widget tabs
    $newNames = @(
        "01_General_Settings_Tab.png",
        "02_Agents_Management_Tab.png", 
        "03_Appearance_Themes_Tab.png",
        "04_Working_Hours_Tab.png",
        "05_Advanced_Settings_Tab.png",
        "06_Preview_Tab.png"
    )
    
    # Rename files
    for ($i = 0; $i -lt [Math]::Min($files.Count, $newNames.Count); $i++) {
        $oldName = $files[$i].Name
        $newName = $newNames[$i]
        $oldPath = Join-Path $folderPath $oldName
        $newPath = Join-Path $folderPath $newName
        
        try {
            Rename-Item -Path $oldPath -NewName $newName -Force
            Write-Host "✅ Renamed: $oldName → $newName" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Error renaming $oldName : $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "`n🎉 Screenshot renaming completed!" -ForegroundColor Yellow
    Write-Host "📋 Final file list:" -ForegroundColor Cyan
    
    # Show final results
    Get-ChildItem -Path $folderPath -Filter "*.png" | ForEach-Object {
        $sizeKB = [Math]::Round($_.Length / 1KB, 1)
        Write-Host "   📸 $($_.Name) ($sizeKB KB)" -ForegroundColor White
    }
}
else {
    Write-Host "❌ Folder not found: $folderPath" -ForegroundColor Red
    Write-Host "Please check the folder path and try again." -ForegroundColor Yellow
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
