<?php
require_once 'config.php';

setApiHeaders();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo apiResponse(false, 'Method not allowed');
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        echo apiResponse(false, 'Invalid JSON input');
        exit();
    }

    // Validate required fields
    if (!isset($input['certificate_id']) || !isset($input['verification_method'])) {
        echo apiResponse(false, 'Missing required fields: certificate_id and verification_method');
        exit();
    }

    $certificateId = trim($input['certificate_id']);
    $verificationMethod = $input['verification_method'];
    $validationEmail = isset($input['validation_email']) ? $input['validation_email'] : null;

    // Validate verification method according to ZeroSSL API documentation
    $validMethods = ['EMAIL', 'CNAME_CSR_HASH', 'HTTP_CSR_HASH', 'HTTPS_CSR_HASH'];
    if (!in_array($verificationMethod, $validMethods)) {
        echo apiResponse(false, 'Invalid verification method. Valid methods: ' . implode(', ', $validMethods));
        exit();
    }

    logMessage("Initiating domain verification for certificate: $certificateId using method: $verificationMethod", 'INFO');

    // Prepare verification data according to ZeroSSL API
    $verificationData = [
        'validation_method' => $verificationMethod
    ];

    // Add validation email if email method is selected
    if ($verificationMethod === 'EMAIL' && $validationEmail) {
        $verificationData['validation_email'] = $validationEmail;
    }

    logMessage("Verification data: " . json_encode($verificationData), 'INFO');

    // Call ZeroSSL verification endpoint: /certificates/{id}/challenges
    $response = zeroSSLRequest("/certificates/$certificateId/challenges", 'POST', $verificationData);

    if (!$response) {
        echo apiResponse(false, 'Failed to initiate domain verification with ZeroSSL API');
        exit();
    }

    logMessage("ZeroSSL verification response: " . json_encode($response), 'INFO');

    // Handle different response types according to documentation
    if (isset($response['success']) && $response['success'] === false) {
        // Handle verification errors
        $errorMessage = 'Domain verification failed';
        if (isset($response['error']['type'])) {
            $errorMessage .= ': ' . $response['error']['type'];
        }
        if (isset($response['error']['details'])) {
            $errorMessage .= ' - Details: ' . json_encode($response['error']['details']);
        }

        logMessage("Domain verification failed: $errorMessage", 'ERROR');
        echo apiResponse(false, $errorMessage, ['error_details' => $response['error'] ?? null]);
        exit();
    }

    // For successful verification initiation
    $responseData = [
        'certificate_id' => $certificateId,
        'verification_method' => $verificationMethod,
        'status' => 'pending_validation'
    ];

    // Handle different verification methods according to ZeroSSL API documentation
    if ($verificationMethod === 'EMAIL') {
        // For email verification, verification emails are sent automatically
        $responseData['message'] = 'Verification emails have been sent. Check your email and click the verification links.';
        $responseData['next_step'] = 'Check your email for verification instructions and click the verification link.';

    } else {
        // For CNAME and HTTP/HTTPS methods, return the certificate object with validation details
        if (isset($response['status']) && $response['status'] === 'pending_validation') {
            $responseData['message'] = 'Domain verification initiated successfully. Complete the verification process.';
            $responseData['certificate_status'] = $response['status'];

            // Include validation details if available
            if (isset($response['validation'])) {
                $responseData['validation_details'] = $response['validation'];
            }
        } else {
            $responseData['message'] = 'Verification process initiated. Please complete the required steps.';
        }

        $responseData['next_step'] = getNextStepInstructions($verificationMethod);
    }

    logMessage("Domain verification initiated successfully for certificate: $certificateId", 'INFO');

    echo apiResponse(true, 'Domain verification initiated successfully', $responseData);

} catch (Exception $e) {
    logMessage("Error in domain verification: " . $e->getMessage(), 'ERROR');
    echo apiResponse(false, 'Internal server error: ' . $e->getMessage());
}

function getNextStepInstructions($method) {
    switch ($method) {
        case 'EMAIL':
            return 'Check your email for verification instructions and click the verification link.';
        case 'HTTP_CSR_HASH':
            return 'Upload the verification file to your website at the specified URL path.';
        case 'HTTPS_CSR_HASH':
            return 'Upload the verification file to your website at the specified HTTPS URL path.';
        case 'CNAME_CSR_HASH':
            return 'Add the CNAME record to your DNS settings as specified.';
        default:
            return 'Complete the verification process as instructed.';
    }
}
?>
