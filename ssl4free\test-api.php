<?php
require_once 'api/config.php';

header('Content-Type: application/json');

try {
    // Test API key and EAB credentials
    echo json_encode([
        'api_key_configured' => ZEROSSL_API_KEY !== 'YOUR_ZEROSSL_API_KEY_HERE',
        'api_key_preview' => substr(ZEROSSL_API_KEY, 0, 8) . '...',
        'eab_configured' => defined('ZEROSSL_EAB_KID') && defined('ZEROSSL_EAB_HMAC_KEY'),
        'eab_kid_preview' => defined('ZEROSSL_EAB_KID') ? substr(ZEROSSL_EAB_KID, 0, 8) . '...' : 'Not set',
        'api_test_result' => checkZeroSSLApiKey(),
        'enhanced_features' => defined('ZEROSSL_EAB_KID') ? 'Available' : 'Basic only',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);

} catch (Exception $e) {
    echo json_encode([
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_PRETTY_PRINT);
}
?>
