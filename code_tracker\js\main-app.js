// Enhanced SIM Database Pro Application with Minahil Integration
class SIMDatabaseApp {
    constructor() {
        this.searchForm = document.getElementById('simSearchForm');
        this.searchResults = document.getElementById('searchResults');
        this.resultsContent = document.getElementById('resultsContent');

        // Initialize scrapers and utilities
        this.realScraper = window.RealSIMScraper;
        this.minahilScraper = window.MinahilScraper;
        this.corsBypass = window.CORSBypass;
        this.dataParser = window.SIMDataParser;
        this.errorHandler = window.ErrorHandler;

        // Search configuration
        this.searchConfig = {
            useMinahilFirst: true,
            enableRealScraping: true,
            fallbackToGenerated: true,
            maxRetries: 3
        };

        this.init();
    }
    
    init() {
        if (this.searchForm) {
            this.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
        }
        
        // Add input validation
        const searchInput = document.getElementById('searchInput');
        const searchType = document.getElementById('searchType');
        
        if (searchInput && searchType) {
            searchType.addEventListener('change', () => this.updateInputPlaceholder());
            searchInput.addEventListener('input', () => this.validateInput());
        }
    }
    
    updateInputPlaceholder() {
        const searchType = document.getElementById('searchType');
        const searchInput = document.getElementById('searchInput');
        
        if (searchType.value === 'mobile') {
            searchInput.placeholder = 'Enter mobile number (e.g., 03001234567)';
        } else if (searchType.value === 'cnic') {
            searchInput.placeholder = 'Enter CNIC without dashes (e.g., 1234512345671)';
        } else {
            searchInput.placeholder = 'Select search type first';
        }
    }
    
    validateInput() {
        const searchType = document.getElementById('searchType');
        const searchInput = document.getElementById('searchInput');
        const value = searchInput.value.trim();
        
        if (searchType.value === 'mobile') {
            // Remove any non-digits and format
            const cleaned = value.replace(/\D/g, '');
            if (cleaned.length <= 11) {
                searchInput.value = cleaned;
            }
        } else if (searchType.value === 'cnic') {
            // Remove any non-digits
            const cleaned = value.replace(/\D/g, '');
            if (cleaned.length <= 13) {
                searchInput.value = cleaned;
            }
        }
    }
    
    async handleSearch(e) {
        e.preventDefault();
        
        const searchType = document.getElementById('searchType').value;
        const searchInput = document.getElementById('searchInput').value.trim();
        
        if (!searchType || !searchInput) {
            this.showError('Please select search type and enter a number');
            return;
        }
        
        // Validate input format
        if (searchType === 'mobile' && !this.validateMobile(searchInput)) {
            this.showError('Please enter a valid Pakistani mobile number (11 digits starting with 03)');
            return;
        }
        
        if (searchType === 'cnic' && !this.validateCNIC(searchInput)) {
            this.showError('Please enter a valid CNIC number (13 digits)');
            return;
        }
        
        // Show loading and perform search
        this.showLoading();
        
        try {
            if (searchType === 'mobile') {
                const result = await this.errorHandler.retryOperation(
                    () => this.performEnhancedMobileSearch(searchInput),
                    { operation: 'Mobile Search', searchInput }
                );
                this.displayMobileResults(searchInput, result);
            } else if (searchType === 'cnic') {
                const result = await this.errorHandler.retryOperation(
                    () => this.performCNICSearch(searchInput),
                    { operation: 'CNIC Search', searchInput }
                );
                this.displayCNICResults(searchInput, result);
            }
        } catch (error) {
            console.error('Search error:', error);
            const errorInfo = this.errorHandler.handleError(error, {
                operation: `${searchType} search`,
                input: searchInput
            });
            this.showEnhancedError(errorInfo, searchInput, searchType);
        }
    }
    
    validateMobile(mobile) {
        // Pakistani mobile number validation
        const mobileRegex = /^03\d{9}$/;
        return mobileRegex.test(mobile);
    }
    
    validateCNIC(cnic) {
        // CNIC validation (13 digits)
        const cnicRegex = /^\d{13}$/;
        return cnicRegex.test(cnic);
    }

    // Enhanced mobile search with multiple strategies
    async performEnhancedMobileSearch(mobileNumber) {
        console.log(`🚀 Starting enhanced mobile search for: ${mobileNumber}`);

        const searchResults = {
            attempts: [],
            finalResult: null,
            searchStrategies: []
        };

        // Strategy 1: Try Minahil scraper first (if enabled)
        if (this.searchConfig.useMinahilFirst && this.minahilScraper) {
            try {
                console.log('🎯 Trying Minahil Fresh SIM Database...');
                this.updateSearchStatus('Connecting to Minahil Fresh SIM Database...');

                const minahilResult = await this.minahilScraper.scrapeSIMData(mobileNumber);
                searchResults.attempts.push({
                    source: 'Minahil Fresh SIM Database',
                    success: minahilResult.success,
                    data: minahilResult.data || null
                });

                if (minahilResult.success && minahilResult.data) {
                    console.log('✅ Minahil search successful');
                    searchResults.finalResult = minahilResult;
                    searchResults.finalResult.source = 'Minahil Fresh SIM Database';
                    return searchResults.finalResult;
                }
            } catch (error) {
                console.log('❌ Minahil search failed:', error.message);
                searchResults.attempts.push({
                    source: 'Minahil Fresh SIM Database',
                    success: false,
                    error: error.message
                });
            }
        }

        // Strategy 2: Try enhanced real scraping
        if (this.searchConfig.enableRealScraping && this.realScraper) {
            try {
                console.log('🔍 Trying enhanced real scraping...');
                this.updateSearchStatus('Searching multiple SIM databases...');

                const realResult = await this.realScraper.enhancedSIMSearch(mobileNumber, 'comprehensive');
                searchResults.attempts.push({
                    source: 'Enhanced Real Scraping',
                    success: realResult.success,
                    data: realResult.finalData || null
                });

                if (realResult.success && realResult.finalData) {
                    console.log('✅ Enhanced real scraping successful');
                    searchResults.finalResult = {
                        success: true,
                        data: realResult.finalData,
                        source: 'Multiple Database Sources'
                    };
                    return searchResults.finalResult;
                }
            } catch (error) {
                console.log('❌ Enhanced real scraping failed:', error.message);
                searchResults.attempts.push({
                    source: 'Enhanced Real Scraping',
                    success: false,
                    error: error.message
                });
            }
        }

        // Strategy 3: Try standard real scraping
        if (this.realScraper) {
            try {
                console.log('📡 Trying standard real scraping...');
                this.updateSearchStatus('Checking standard SIM databases...');

                const standardResult = await this.realScraper.scrapeSIMData(mobileNumber);
                searchResults.attempts.push({
                    source: 'Standard Real Scraping',
                    success: standardResult.success,
                    data: standardResult.data || null
                });

                if (standardResult.success && standardResult.data) {
                    console.log('✅ Standard real scraping successful');
                    searchResults.finalResult = standardResult;
                    return searchResults.finalResult;
                }
            } catch (error) {
                console.log('❌ Standard real scraping failed:', error.message);
                searchResults.attempts.push({
                    source: 'Standard Real Scraping',
                    success: false,
                    error: error.message
                });
            }
        }

        // Strategy 4: Fallback to generated data (if enabled)
        if (this.searchConfig.fallbackToGenerated) {
            console.log('🎭 Using enhanced fallback data generation...');
            this.updateSearchStatus('Generating sample data for demonstration...');

            const fallbackResult = this.generateEnhancedMobileData(mobileNumber);
            searchResults.attempts.push({
                source: 'Enhanced Generated Data',
                success: true,
                data: fallbackResult.data
            });

            searchResults.finalResult = fallbackResult;
            return searchResults.finalResult;
        }

        // If all strategies fail
        return {
            success: false,
            error: 'All search strategies failed',
            attempts: searchResults.attempts
        };
    }

    // Enhanced CNIC search
    async performCNICSearch(cnicNumber) {
        console.log(`🆔 Starting CNIC search for: ${cnicNumber}`);

        try {
            // For now, use enhanced generated data for CNIC search
            // In a real implementation, this would query actual databases
            this.updateSearchStatus('Searching CNIC database...');

            const result = await this.generateEnhancedCNICData(cnicNumber);
            return result;
        } catch (error) {
            console.error('CNIC search error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    showLoading() {
        this.searchResults.style.display = 'block';
        this.resultsContent.innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner"></i>
                <p>Searching SIM database...</p>
                <small>Fetching data from multiple sources</small>
                <div style="margin-top: 1rem; font-size: 0.8rem; opacity: 0.7;">
                    <div id="status1">🔍 Connecting to database servers...</div>
                    <div id="status2">⏳ Checking network records...</div>
                    <div id="status3">🔄 Processing information...</div>
                    <div id="status4">📡 Finalizing results...</div>
                </div>
            </div>
        `;
        
        this.searchResults.scrollIntoView({ behavior: 'smooth' });
        
        // Animate status messages
        setTimeout(() => this.updateStatus('status1', '✅ Database connection established'), 1000);
        setTimeout(() => this.updateStatus('status2', '✅ Network records found'), 2000);
        setTimeout(() => this.updateStatus('status3', '✅ Information processed'), 3000);
        setTimeout(() => this.updateStatus('status4', '🔍 Displaying results...'), 4000);
    }
    
    updateStatus(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = text;
        }
    }

    updateSearchStatus(message) {
        // Update the current search status
        const statusElements = ['status1', 'status2', 'status3', 'status4'];
        const currentElement = statusElements.find(id => {
            const el = document.getElementById(id);
            return el && !el.innerHTML.includes('✅');
        });

        if (currentElement) {
            this.updateStatus(currentElement, `🔄 ${message}`);
        }
    }

    // Enhanced mobile data generation
    generateEnhancedMobileData(mobileNumber) {
        const network = this.detectNetwork(mobileNumber);
        const seed = parseInt(mobileNumber.replace(/\D/g, '')) % 10000;

        const pakistaniNames = [
            'Muhammad Ahmed Khan', 'Fatima Bibi', 'Ali Hassan Shah', 'Ayesha Malik',
            'Hassan Raza Qureshi', 'Zainab Sheikh', 'Omar Farooq Ahmed', 'Sana Iqbal',
            'Usman Ali Khan', 'Mariam Khan', 'Bilal Ahmed Siddiqui', 'Khadija Bibi',
            'Shahid Afridi', 'Imran Khan', 'Wasim Akram', 'Shoaib Akhtar'
        ];

        const cities = [
            'Karachi', 'Lahore', 'Islamabad', 'Rawalpindi', 'Faisalabad',
            'Multan', 'Peshawar', 'Quetta', 'Sialkot', 'Gujranwala',
            'Hyderabad', 'Sargodha', 'Bahawalpur', 'Sukkur', 'Larkana'
        ];

        const areas = {
            'Karachi': ['Gulshan-e-Iqbal', 'Clifton', 'Defence', 'Nazimabad', 'North Karachi'],
            'Lahore': ['Model Town', 'Gulberg', 'DHA', 'Johar Town', 'Faisal Town'],
            'Islamabad': ['F-6', 'F-7', 'F-8', 'G-9', 'I-8']
        };

        const city = cities[seed % cities.length];
        const cityAreas = areas[city] || ['Block A', 'Block B', 'Sector 1'];
        const area = cityAreas[seed % cityAreas.length];

        return {
            success: true,
            data: {
                mobile: mobileNumber,
                owner: pakistaniNames[seed % pakistaniNames.length],
                cnic: this.generateRealisticCNIC(seed),
                address: `House No. ${1 + (seed % 999)}, Street ${1 + (seed % 50)}, ${area}, ${city}`,
                network: network,
                status: seed % 10 === 0 ? 'Inactive' : 'Active',
                type: seed % 3 === 0 ? 'Postpaid' : 'Prepaid',
                registrationDate: this.generateRealisticDate(seed),
                source: 'Enhanced Educational Data (Learning Purpose)',
                confidence: 'Educational Sample',
                timestamp: new Date().toISOString()
            }
        };
    }

    // Enhanced CNIC data generation
    generateEnhancedCNICData(cnicNumber) {
        const simCount = Math.floor(Math.random() * 4) + 1; // 1-4 SIM cards
        const sims = [];
        const seed = parseInt(cnicNumber.substring(0, 8)) % 10000;

        const networks = ['Jazz', 'Telenor', 'Zong', 'Ufone'];

        for (let i = 0; i < simCount; i++) {
            const networkIndex = (seed + i) % networks.length;
            const network = networks[networkIndex];

            // Generate realistic mobile number based on network
            let prefix;
            switch (network) {
                case 'Jazz': prefix = ['0300', '0301', '0302'][i % 3]; break;
                case 'Telenor': prefix = ['0321', '0322', '0323'][i % 3]; break;
                case 'Zong': prefix = ['0310', '0311', '0312'][i % 3]; break;
                case 'Ufone': prefix = ['0333', '0334', '0335'][i % 3]; break;
                default: prefix = '0300';
            }

            const suffix = Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
            const mobileNumber = prefix + suffix;

            sims.push({
                mobile: mobileNumber,
                network: network,
                status: Math.random() > 0.15 ? 'Active' : 'Inactive',
                type: Math.random() > 0.4 ? 'Prepaid' : 'Postpaid',
                registrationDate: this.generateRealisticDate(seed + i * 100)
            });
        }

        return {
            success: true,
            data: sims,
            source: 'Enhanced CNIC Data (Educational Purpose)'
        };
    }

    generateRealisticCNIC(seed) {
        const provinces = ['42', '61', '35', '37', '54']; // Major cities
        const province = provinces[seed % provinces.length];
        const district = String(101 + (seed % 50)).padStart(3, '0');
        const serial = String(1000000 + (seed * 7 % 8999999));
        const checkDigit = 1 + (seed % 9);

        return `${province}${district}-${serial}-${checkDigit}`;
    }

    generateRealisticDate(seed) {
        const start = new Date(2018, 0, 1).getTime();
        const end = new Date(2024, 11, 31).getTime();
        const timestamp = start + (seed % (end - start));
        return new Date(timestamp).toISOString().split('T')[0];
    }
    
    displayMobileResults(mobile, result) {
        if (!result || !result.success) {
            this.showError('No data found for this mobile number');
            return;
        }
        
        const data = result.data;
        const source = result.source || 'Database';
        
        // Determine source styling
        let sourceIcon = '💾';
        let sourceClass = 'sample-data';
        let sourceMessage = 'Sample Data: For demonstration purposes';
        
        if (source.includes('Real Data')) {
            sourceIcon = '🔍';
            sourceClass = 'real-data';
            sourceMessage = 'Real Data: Information fetched from live databases';
        } else if (source.includes('Generated')) {
            sourceIcon = '🧮';
            sourceClass = 'generated-data';
            sourceMessage = 'Generated Data: Algorithmically created for demonstration';
        }
        
        this.resultsContent.innerHTML = `
            <div class="result-card">
                <h3><i class="fas fa-mobile-alt"></i> SIM Owner Details</h3>
                <div style="text-align: right; margin-bottom: 1rem; font-size: 0.8rem; color: #666;">
                    ${sourceIcon} Source: ${source}
                </div>
                
                <div class="result-item">
                    <span class="result-label">Mobile Number:</span>
                    <span class="result-value">${mobile}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">Owner Name:</span>
                    <span class="result-value">${data.owner}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">CNIC:</span>
                    <span class="result-value">${data.cnic}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">Address:</span>
                    <span class="result-value">${data.address}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">Network:</span>
                    <span class="result-value">${data.network}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">Status:</span>
                    <span class="result-value">${data.status}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">Registration Date:</span>
                    <span class="result-value">${data.registrationDate}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">Type:</span>
                    <span class="result-value">${data.type}</span>
                </div>
                
                <div class="${sourceClass}-notice" style="padding: 1rem; border-radius: var(--border-radius); margin-top: 1rem; font-size: 0.9rem;">
                    <i class="fas fa-info-circle"></i> <strong>${sourceMessage}</strong>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 2rem;">
                <p><strong>Need more detailed information or real-time data?</strong></p>
                <a href="https://wa.me/+923306055177" class="btn btn-secondary" style="margin-top: 10px;">
                    <i class="fab fa-whatsapp"></i> Contact for Premium Services
                </a>
            </div>
        `;
    }
    
    displayCNICResults(cnic, result) {
        if (!result || !result.success) {
            this.showError('No data found for this CNIC number');
            return;
        }
        
        const data = result.data;
        const source = result.source || 'Database';
        
        let html = `
            <div class="result-card">
                <h3><i class="fas fa-id-card"></i> CNIC Information</h3>
                <div style="text-align: right; margin-bottom: 1rem; font-size: 0.8rem; color: #666;">
                    💾 Source: ${source}
                </div>
                
                <div class="result-item">
                    <span class="result-label">CNIC Number:</span>
                    <span class="result-value">${this.formatCNIC(cnic)}</span>
                </div>
                <div class="result-item">
                    <span class="result-label">Total SIM Cards:</span>
                    <span class="result-value">${data.length}</span>
                </div>
            </div>
        `;
        
        data.forEach((sim, index) => {
            html += `
                <div class="result-card">
                    <h4><i class="fas fa-mobile-alt"></i> SIM Card ${index + 1}</h4>
                    <div class="result-item">
                        <span class="result-label">Mobile Number:</span>
                        <span class="result-value">${sim.mobile}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Network:</span>
                        <span class="result-value">${sim.network}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Status:</span>
                        <span class="result-value">${sim.status}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Type:</span>
                        <span class="result-value">${sim.type}</span>
                    </div>
                    ${sim.registrationDate ? `
                        <div class="result-item">
                            <span class="result-label">Registration Date:</span>
                            <span class="result-value">${sim.registrationDate}</span>
                        </div>
                    ` : ''}
                </div>
            `;
        });
        
        html += `
            <div style="text-align: center; margin-top: 2rem;">
                <p><strong>Need detailed information for each SIM?</strong></p>
                <a href="https://wa.me/+923306055177" class="btn btn-secondary" style="margin-top: 10px;">
                    <i class="fab fa-whatsapp"></i> Contact for Premium Services
                </a>
            </div>
        `;
        
        this.resultsContent.innerHTML = html;
    }
    
    async generateCNICData(cnicNumber) {
        try {
            const simCount = Math.floor(Math.random() * 3) + 1; // 1-3 SIM cards
            const sims = [];
            
            for (let i = 0; i < simCount; i++) {
                // Generate mobile number based on CNIC
                const prefixes = ['0300', '0301', '0321', '0322', '0310', '0333'];
                const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
                const suffix = Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
                const mobileNumber = prefix + suffix;
                
                sims.push({
                    mobile: mobileNumber,
                    network: this.detectNetwork(mobileNumber),
                    status: Math.random() > 0.2 ? 'Active' : 'Inactive',
                    type: Math.random() > 0.5 ? 'Prepaid' : 'Postpaid',
                    registrationDate: new Date(Date.now() - Math.random() * 5 * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
                });
            }
            
            return {
                success: true,
                data: sims,
                source: 'Generated CNIC Data'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    detectNetwork(mobile) {
        if (mobile.startsWith('0300') || mobile.startsWith('0301') || mobile.startsWith('0302')) return 'Jazz';
        if (mobile.startsWith('0321') || mobile.startsWith('0322') || mobile.startsWith('0323')) return 'Telenor';
        if (mobile.startsWith('0310') || mobile.startsWith('0311') || mobile.startsWith('0312')) return 'Zong';
        if (mobile.startsWith('0333') || mobile.startsWith('0334') || mobile.startsWith('0335')) return 'Ufone';
        return 'Unknown';
    }
    
    formatCNIC(cnic) {
        // Format CNIC as xxxxx-xxxxxxx-x
        if (cnic.length === 13) {
            return `${cnic.substr(0, 5)}-${cnic.substr(5, 7)}-${cnic.substr(12, 1)}`;
        }
        return cnic;
    }
    
    showError(message) {
        this.searchResults.style.display = 'block';
        this.resultsContent.innerHTML = `
            <div class="error-message" style="text-align: center; padding: 3rem; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                <h3 style="font-size: 1.5rem; margin-bottom: 1rem; color: #dc3545;">Search Error</h3>
                <p style="margin-bottom: 1rem; color: #666;">${message}</p>
                <button class="btn btn-primary" onclick="clearResults()">Try Again</button>
            </div>
        `;
        this.searchResults.scrollIntoView({ behavior: 'smooth' });
    }

    showEnhancedError(errorInfo, searchInput, searchType) {
        this.searchResults.style.display = 'block';

        // Try to provide fallback data
        let fallbackContent = '';
        try {
            const fallbackData = this.errorHandler.generateFallbackResponse(searchInput, searchType);
            if (fallbackData.success) {
                fallbackContent = `
                    <div style="margin-top: 2rem; padding: 1.5rem; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                        <h5><i class="fas fa-info-circle"></i> Sample Data Available</h5>
                        <p>While we couldn't fetch real data, here's sample data for educational purposes:</p>
                        <div style="margin-top: 1rem;">
                            ${searchType === 'mobile' ? this.formatSampleMobileData(fallbackData.data) : this.formatSampleCNICData(fallbackData.data)}
                        </div>
                        <small style="color: #666; font-style: italic;">This is sample data for demonstration only</small>
                    </div>
                `;
            }
        } catch (fallbackError) {
            console.log('Fallback data generation failed:', fallbackError);
        }

        this.resultsContent.innerHTML = `
            <div class="error-message" style="padding: 2rem;">
                <div style="text-align: center; margin-bottom: 2rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #dc3545; margin-bottom: 1rem;"></i>
                    <h3 style="color: #dc3545; margin-bottom: 1rem;">${errorInfo.message}</h3>
                </div>

                ${errorInfo.suggestions.length > 0 ? `
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 1.5rem; margin-bottom: 1.5rem;">
                        <h5><i class="fas fa-lightbulb"></i> Suggestions:</h5>
                        <ul style="margin: 0.5rem 0 0 1rem;">
                            ${errorInfo.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}

                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <button class="btn btn-primary" onclick="clearResults()" style="margin-right: 1rem;">
                        <i class="fas fa-redo"></i> Try Again
                    </button>
                    <button class="btn btn-secondary" onclick="window.location.reload()">
                        <i class="fas fa-refresh"></i> Refresh Page
                    </button>
                </div>

                ${fallbackContent}

                <div style="margin-top: 2rem; padding: 1rem; background: #e9ecef; border-radius: 8px; font-size: 0.9rem; color: #666;">
                    <strong>Technical Details:</strong> ${errorInfo.technical}
                </div>
            </div>
        `;

        this.searchResults.scrollIntoView({ behavior: 'smooth' });
    }

    formatSampleMobileData(data) {
        return `
            <div style="background: white; padding: 1rem; border-radius: 6px; border: 1px solid #dee2e6;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; font-size: 0.9rem;">
                    <div><strong>Mobile:</strong> ${data.mobile}</div>
                    <div><strong>Owner:</strong> ${data.owner}</div>
                    <div><strong>CNIC:</strong> ${data.cnic}</div>
                    <div><strong>Network:</strong> ${data.network}</div>
                </div>
            </div>
        `;
    }

    formatSampleCNICData(data) {
        return `
            <div style="background: white; padding: 1rem; border-radius: 6px; border: 1px solid #dee2e6;">
                <div style="font-size: 0.9rem;">
                    <div><strong>Total SIM Cards:</strong> ${data.length}</div>
                    ${data.map((sim, index) => `
                        <div style="margin-top: 0.5rem; padding: 0.5rem; background: #f8f9fa; border-radius: 4px;">
                            <strong>SIM ${index + 1}:</strong> ${sim.mobile} (${sim.network})
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
}

// Clear results function
function clearResults() {
    const searchResults = document.getElementById('searchResults');
    const searchForm = document.getElementById('simSearchForm');

    if (searchResults) {
        searchResults.style.display = 'none';
    }

    if (searchForm) {
        searchForm.reset();
    }
}

// Fill example data function
function fillExample(number) {
    const searchInput = document.getElementById('searchInput');
    const searchType = document.getElementById('searchType');

    if (searchInput && searchType) {
        searchInput.value = number;

        // Determine search type based on number length
        if (number.length === 11 && number.startsWith('03')) {
            searchType.value = 'mobile';
            searchInput.placeholder = 'Enter mobile number (e.g., 03001234567)';
        } else if (number.length === 13) {
            searchType.value = 'cnic';
            searchInput.placeholder = 'Enter CNIC without dashes (e.g., 1234512345671)';
        }

        // Add visual feedback
        searchInput.style.background = 'rgba(40, 120, 235, 0.05)';
        setTimeout(() => {
            searchInput.style.background = '#fff';
        }, 1000);

        // Focus on the input
        searchInput.focus();
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new SIMDatabaseApp();
    
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add hover effects to service cards
    document.querySelectorAll('.service-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
});
