<!DOCTYPE html>
<html>
<head>
    <title>Test New Certificate Creation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test New Certificate Creation API</h1>
    
    <?php
    $testDomain = 'test.jobzhit.com';
    $testEmail = '<EMAIL>';
    
    echo "<p><strong>Testing Domain:</strong> $testDomain</p>";
    echo "<p><strong>Testing Email:</strong> $testEmail</p>";
    
    // Test the new certificate creation API
    $postData = json_encode([
        'domain' => $testDomain,
        'email' => $testEmail
    ]);
    
    echo "<h2>Testing create-cert-new.php</h2>";
    echo "<p><strong>Request Data:</strong></p>";
    echo "<pre>" . htmlspecialchars($postData) . "</pre>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/ssl4free/create-cert-new.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<h3>Response:</h3>";
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    
    if ($error) {
        echo "<p class='error'>❌ cURL Error: $error</p>";
    } else {
        echo "<p class='success'>✅ cURL Success</p>";
    }
    
    echo "<p><strong>Response Body:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "<h3>Parsed Response:</h3>";
        
        if (isset($responseData['success']) && $responseData['success']) {
            echo "<p class='success'>🎉 SUCCESS! Certificate creation worked!</p>";
            
            if (isset($responseData['data']['certificate_id'])) {
                echo "<p class='success'>Certificate ID: " . $responseData['data']['certificate_id'] . "</p>";
            }
            
            if (isset($responseData['data']['validation'])) {
                echo "<h4>Validation Instructions:</h4>";
                echo "<pre>" . htmlspecialchars(json_encode($responseData['data']['validation'], JSON_PRETTY_PRINT)) . "</pre>";
            }
        } else {
            echo "<p class='error'>❌ FAILED: " . ($responseData['message'] ?? 'Unknown error') . "</p>";
            
            if (isset($responseData['details'])) {
                echo "<h4>Error Details:</h4>";
                echo "<pre>" . htmlspecialchars(json_encode($responseData['details'], JSON_PRETTY_PRINT)) . "</pre>";
            }
        }
    } else {
        echo "<p class='error'>❌ Invalid JSON response</p>";
    }
    ?>
    
    <h2>Next Steps</h2>
    <p>If this test succeeds, we can replace the old create-certificate.php with this working version.</p>
    
    <p><a href="debug-full.php">← Back to Debug</a></p>
</body>
</html>
