<?php
// Contact Form Handler for PakSim Website
// Email: <EMAIL>

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type
header('Content-Type: application/json');

// Check if form was submitted via POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    
    // Sanitize and validate input data
    $name = isset($_POST['name']) ? trim(strip_tags($_POST['name'])) : '';
    $email = isset($_POST['email']) ? trim(strip_tags($_POST['email'])) : '';
    $phone = isset($_POST['phone']) ? trim(strip_tags($_POST['phone'])) : '';
    $subject = isset($_POST['subject']) ? trim(strip_tags($_POST['subject'])) : '';
    $message = isset($_POST['message']) ? trim(strip_tags($_POST['message'])) : '';
    
    // Validation
    $errors = array();
    
    if (empty($name)) {
        $errors[] = "Name is required";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    if (empty($subject)) {
        $errors[] = "Subject is required";
    }
    
    if (empty($message)) {
        $errors[] = "Message is required";
    }
    
    // If no errors, send email
    if (empty($errors)) {
        
        // Email configuration
        $to = "<EMAIL>";
        $email_subject = "PakSim Contact Form: " . $subject;
        
        // Email body
        $email_body = "
        New contact form submission from PakSim website:
        
        Name: $name
        Email: $email
        Phone: $phone
        Subject: $subject
        
        Message:
        $message
        
        ---
        Submitted on: " . date('Y-m-d H:i:s') . "
        IP Address: " . $_SERVER['REMOTE_ADDR'] . "
        User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "
        ";
        
        // Email headers
        $headers = array(
            'From: PakSim Website <<EMAIL>>',
            'Reply-To: ' . $email,
            'X-Mailer: PHP/' . phpversion(),
            'Content-Type: text/plain; charset=UTF-8'
        );
        
        // Send email
        if (mail($to, $email_subject, $email_body, implode("\r\n", $headers))) {
            
            // Success response
            echo json_encode(array(
                'status' => 'success',
                'message' => 'Thank you! Your message has been sent successfully. We will get back to you soon.'
            ));
            
            // Log successful submission (optional)
            $log_entry = date('Y-m-d H:i:s') . " - Contact form submitted by: $name ($email)\n";
            file_put_contents('contact_log.txt', $log_entry, FILE_APPEND | LOCK_EX);
            
        } else {
            // Email sending failed
            echo json_encode(array(
                'status' => 'error',
                'message' => 'Sorry, there was an error sending your message. Please try again later or contact us directly.'
            ));
        }
        
    } else {
        // Validation errors
        echo json_encode(array(
            'status' => 'error',
            'message' => 'Please fix the following errors: ' . implode(', ', $errors)
        ));
    }
    
} else {
    // Not a POST request
    echo json_encode(array(
        'status' => 'error',
        'message' => 'Invalid request method'
    ));
}

// Alternative method using PHPMailer (if available)
/*
require_once 'vendor/autoload.php';
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;

function sendEmailWithPHPMailer($to, $subject, $body, $fromEmail, $fromName) {
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = 'smtp.gmail.com';
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';
        $mail->Password   = 'your-app-password';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;
        
        // Recipients
        $mail->setFrom('<EMAIL>', 'PakSim Website');
        $mail->addAddress($to);
        $mail->addReplyTo($fromEmail, $fromName);
        
        // Content
        $mail->isHTML(false);
        $mail->Subject = $subject;
        $mail->Body    = $body;
        
        $mail->send();
        return true;
    } catch (Exception $e) {
        return false;
    }
}
*/
?>
