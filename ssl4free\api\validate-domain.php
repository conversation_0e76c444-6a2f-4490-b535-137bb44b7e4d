<?php
require_once 'config.php';

setApiHeaders();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo apiResponse(false, 'Method not allowed');
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo apiResponse(false, 'Invalid JSON input');
        exit();
    }
    
    // Validate required fields
    if (!isset($input['domain']) || !isset($input['email'])) {
        echo apiResponse(false, 'Missing required fields: domain and email');
        exit();
    }
    
    $domain = trim($input['domain']);
    $email = trim($input['email']);
    
    // Remove protocol if present
    $domain = preg_replace('/^https?:\/\//', '', $domain);
    $domain = rtrim($domain, '/');
    
    // Validate domain
    if (!validateDomain($domain)) {
        echo apiResponse(false, 'Invalid domain name or domain does not resolve');
        exit();
    }
    
    // Validate email
    if (!validateEmail($email)) {
        echo apiResponse(false, 'Invalid email address');
        exit();
    }
    
    // Check if ZeroSSL API key is configured
    if (!checkZeroSSLApiKey()) {
        echo apiResponse(false, 'ZeroSSL API key not configured. Please contact administrator.');
        exit();
    }
    
    // Log the validation request
    logMessage("Domain validation requested for: $domain by $email", 'INFO');
    
    // Skip domain accessibility check to avoid timeout
    $domainAccessible = true; // Assume accessible for faster processing
    
    // Prepare response data
    $responseData = [
        'domain' => $domain,
        'email' => $email,
        'domain_accessible' => $domainAccessible,
        'validation_methods' => [
            'http' => [
                'available' => $domainAccessible,
                'description' => 'Upload a verification file to your website'
            ],
            'dns' => [
                'available' => true,
                'description' => 'Add a DNS TXT record to your domain'
            ]
        ]
    ];
    
    // Save domain data for next steps
    $tempData = [
        'domain' => $domain,
        'email' => $email,
        'timestamp' => time(),
        'validation_step' => 'domain_validated'
    ];
    
    saveCertificateData($domain, $tempData);
    
    echo apiResponse(true, 'Domain validated successfully', $responseData);
    
} catch (Exception $e) {
    logMessage("Error in domain validation: " . $e->getMessage(), 'ERROR');
    echo apiResponse(false, 'Internal server error: ' . $e->getMessage());
}

function checkDomainAccessibility($domain) {
    try {
        // Try to make a simple HTTP request to the domain
        $url = "http://$domain/";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5); // Reduced timeout
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'SSL4Free Domain Validator');
        curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3); // Connection timeout

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Consider domain accessible if we get any HTTP response (even 404)
        if (!$error && $httpCode > 0) {
            logMessage("Domain $domain is accessible (HTTP $httpCode)", 'INFO');
            return true;
        } else {
            logMessage("Domain $domain accessibility check failed: $error (but allowing anyway)", 'WARNING');
            // Return true anyway - domain might be valid but not accessible from this server
            return true;
        }

    } catch (Exception $e) {
        logMessage("Error checking domain accessibility for $domain: " . $e->getMessage() . " (but allowing anyway)", 'WARNING');
        // Return true anyway - we'll let ZeroSSL handle the actual domain validation
        return true;
    }
}
?>
