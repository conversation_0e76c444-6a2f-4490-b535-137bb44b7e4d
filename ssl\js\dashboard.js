// SSL For Free Dashboard JavaScript
let currentDomain = '';
let currentEmail = '';
let verificationMethod = '';
let certificateData = {};

// Show/Hide sections
function showNewCertificate() {
    hideAllSections();
    document.getElementById('newCertificateSection').style.display = 'block';
    resetSteps();
}

function showCertificateList() {
    hideAllSections();
    document.getElementById('certificateListSection').style.display = 'block';
    loadCertificateList();
}

function showHelp() {
    hideAllSections();
    document.getElementById('helpSection').style.display = 'block';
}

function hideAllSections() {
    document.getElementById('newCertificateSection').style.display = 'none';
    document.getElementById('certificateListSection').style.display = 'none';
    document.getElementById('helpSection').style.display = 'none';
}

function resetSteps() {
    document.getElementById('step1').style.display = 'block';
    document.getElementById('step2').style.display = 'none';
    document.getElementById('step3').style.display = 'none';
    document.getElementById('step4').style.display = 'none';
}

// Step 1: Domain Validation
async function validateDomain() {
    const domain = document.getElementById('domainInput').value.trim();
    const email = document.getElementById('emailInput').value.trim();
    
    if (!domain || !email) {
        alert('Please enter both domain and email address');
        return;
    }
    
    // Basic domain validation
    if (!isValidDomain(domain)) {
        alert('Please enter a valid domain name');
        return;
    }
    
    if (!isValidEmail(email)) {
        alert('Please enter a valid email address');
        return;
    }
    
    currentDomain = domain;
    currentEmail = email;
    
    // Show loading
    showLoading('Validating domain...');
    
    try {
        // Simulate domain validation
        await delay(2000);
        
        hideLoading();
        
        // Show step 2
        document.getElementById('step2').style.display = 'block';
        
        showSuccess('Domain validation successful! Please choose verification method.');
        
    } catch (error) {
        hideLoading();
        showError('Domain validation failed: ' + error.message);
    }
}

// Step 2: Verification Method Selection
function selectVerificationMethod(method) {
    verificationMethod = method;
    
    let details = '';
    
    if (method === 'http') {
        details = `
            <h5 class="font-medium text-blue-800 mb-3">HTTP File Upload Verification</h5>
            <p class="text-blue-700 mb-3">Please upload the following file to your website:</p>
            <div class="bg-white border rounded p-3 mb-3">
                <strong>File Path:</strong> http://${currentDomain}/.well-known/acme-challenge/verification.txt<br>
                <strong>File Content:</strong> ${generateVerificationToken()}
            </div>
            <p class="text-sm text-blue-600">Make sure the file is accessible via HTTP before proceeding.</p>
        `;
    } else if (method === 'dns') {
        details = `
            <h5 class="font-medium text-blue-800 mb-3">DNS TXT Record Verification</h5>
            <p class="text-blue-700 mb-3">Please add the following DNS TXT record:</p>
            <div class="bg-white border rounded p-3 mb-3">
                <strong>Record Type:</strong> TXT<br>
                <strong>Name:</strong> _acme-challenge.${currentDomain}<br>
                <strong>Value:</strong> ${generateVerificationToken()}
            </div>
            <p class="text-sm text-blue-600">DNS changes may take up to 24 hours to propagate.</p>
        `;
    }
    
    document.getElementById('verificationDetails').innerHTML = details;
    document.getElementById('step3').style.display = 'block';
}

// Step 3: Generate Certificate
async function generateCertificate() {
    showLoading('Generating SSL certificate...');
    
    try {
        const response = await fetch('api/debug-real-ssl.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: currentDomain,
                email: currentEmail,
                verification_method: verificationMethod,
                staging: false
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            certificateData = data.data;
            
            hideLoading();
            document.getElementById('step4').style.display = 'block';
            
            // Save certificate to local storage
            saveCertificateToStorage(certificateData);
            
            showSuccess('SSL certificate generated successfully!');
            
        } else {
            hideLoading();
            showError('Certificate generation failed: ' + data.message);
        }
        
    } catch (error) {
        hideLoading();
        showError('Error generating certificate: ' + error.message);
    }
}

// Step 4: Download Files
function downloadFile(type) {
    let content = '';
    let filename = '';
    
    switch (type) {
        case 'certificate':
            content = certificateData.certificate;
            filename = `${currentDomain}.crt`;
            break;
        case 'privatekey':
            content = certificateData.privateKey;
            filename = `${currentDomain}.key`;
            break;
        case 'cabundle':
            content = certificateData.caBundle;
            filename = `${currentDomain}_ca_bundle.crt`;
            break;
    }
    
    if (content) {
        downloadTextFile(content, filename);
        showSuccess(`${filename} downloaded successfully!`);
    }
}

// Certificate Management
function loadCertificateList() {
    const certificates = getCertificatesFromStorage();
    const tbody = document.getElementById('certificateTableBody');
    
    if (certificates.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="px-4 py-8 text-center text-gray-500">
                    <i class="fas fa-certificate text-4xl mb-4"></i><br>
                    No certificates found. <a href="#" onclick="showNewCertificate()" class="text-blue-600">Generate your first certificate</a>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = certificates.map(cert => `
        <tr class="border-b">
            <td class="px-4 py-3">${cert.domain}</td>
            <td class="px-4 py-3">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <i class="fas fa-check-circle mr-1"></i>Active
                </span>
            </td>
            <td class="px-4 py-3">${cert.validTo}</td>
            <td class="px-4 py-3">
                <button onclick="downloadCertificate('${cert.domain}')" class="text-blue-600 hover:text-blue-800 mr-3">
                    <i class="fas fa-download"></i> Download
                </button>
                <button onclick="renewCertificate('${cert.domain}')" class="text-green-600 hover:text-green-800 mr-3">
                    <i class="fas fa-sync"></i> Renew
                </button>
                <button onclick="deleteCertificate('${cert.domain}')" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </td>
        </tr>
    `).join('');
}

// Utility Functions
function isValidDomain(domain) {
    const pattern = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
    return pattern.test(domain);
}

function isValidEmail(email) {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return pattern.test(email);
}

function generateVerificationToken() {
    return 'acme_' + Math.random().toString(36).substr(2, 32);
}

function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function showLoading(message) {
    // Create loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'loadingOverlay';
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    overlay.innerHTML = `
        <div class="bg-white rounded-lg p-6 text-center">
            <i class="fas fa-spinner fa-spin text-3xl text-blue-600 mb-4"></i>
            <p class="text-gray-700">${message}</p>
        </div>
    `;
    document.body.appendChild(overlay);
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.remove();
    }
}

function showSuccess(message) {
    showNotification(message, 'success');
}

function showError(message) {
    showNotification(message, 'error');
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
            ${message}
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

function downloadTextFile(content, filename) {
    const element = document.createElement('a');
    element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(content));
    element.setAttribute('download', filename);
    element.style.display = 'none';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
}

// Local Storage Functions
function saveCertificateToStorage(certData) {
    const certificates = getCertificatesFromStorage();
    certificates.push({
        domain: certData.domain,
        validFrom: certData.validFrom,
        validTo: certData.validTo,
        certificate: certData.certificate,
        privateKey: certData.privateKey,
        caBundle: certData.caBundle,
        createdAt: new Date().toISOString()
    });
    localStorage.setItem('sslCertificates', JSON.stringify(certificates));
}

function getCertificatesFromStorage() {
    const stored = localStorage.getItem('sslCertificates');
    return stored ? JSON.parse(stored) : [];
}

function downloadCertificate(domain) {
    const certificates = getCertificatesFromStorage();
    const cert = certificates.find(c => c.domain === domain);
    if (cert) {
        downloadTextFile(cert.certificate, `${domain}.crt`);
        downloadTextFile(cert.privateKey, `${domain}.key`);
        downloadTextFile(cert.caBundle, `${domain}_ca_bundle.crt`);
        showSuccess('Certificate files downloaded!');
    }
}

function renewCertificate(domain) {
    showNotification('Certificate renewal feature coming soon!', 'info');
}

function deleteCertificate(domain) {
    if (confirm(`Are you sure you want to delete the certificate for ${domain}?`)) {
        const certificates = getCertificatesFromStorage();
        const filtered = certificates.filter(c => c.domain !== domain);
        localStorage.setItem('sslCertificates', JSON.stringify(filtered));
        loadCertificateList();
        showSuccess('Certificate deleted successfully!');
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Show dashboard by default
    showCertificateList();
});
