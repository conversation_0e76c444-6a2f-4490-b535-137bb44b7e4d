<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL4Free - Free SSL Certificate Generator | مفت SSL سرٹیفکیٹ جنریٹر</title>
    <meta name="description" content="Generate free SSL certificates using ZeroSSL API. Secure your website with trusted SSL encryption. مفت SSL سرٹیفکیٹ بنائیں">
    <meta name="keywords" content="free ssl certificate, ssl generator, website security, https, encryption, zerossl">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .step-active { background: linear-gradient(135deg, #10b981, #059669); }
        .step-completed { background: linear-gradient(135deg, #059669, #047857); }
        .step-pending { background: #e5e7eb; }
        .urdu-text { font-family: 'Noto Nastaliq Urdu', serif; }
    </style>
    
    <!-- Google Fonts for Urdu -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Nastaliq+Urdu:wght@400;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-shield-alt text-3xl"></i>
                    <div>
                        <h1 class="text-3xl font-bold">SSL4Free</h1>
                        <p class="text-blue-100 urdu-text">مفت SSL سرٹیفکیٹ جنریٹر</p>
                    </div>
                </div>
                <nav class="hidden md:flex space-x-6">
                    <a href="index.html" class="hover:text-blue-200 font-medium">Home</a>
                    <a href="privacy.html" class="hover:text-blue-200">Privacy</a>
                    <a href="terms.html" class="hover:text-blue-200">Terms</a>
                    <a href="contact.html" class="hover:text-blue-200">Contact</a>
                </nav>
                <button class="md:hidden text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-5xl font-bold text-gray-800 mb-4">
                Free SSL Certificates
            </h2>
            <p class="text-2xl text-gray-600 mb-2 urdu-text">
                مفت SSL سرٹیفکیٹس
            </p>
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Generate trusted SSL certificates for your website using ZeroSSL API. 
                <span class="urdu-text">اپنی ویب سائٹ کے لیے قابل اعتماد SSL سرٹیفکیٹ بنائیں</span>
            </p>
            <button onclick="startSSLGeneration()" class="bg-green-600 text-white px-8 py-4 rounded-lg text-xl font-semibold hover:bg-green-700 transition-colors shadow-lg">
                <i class="fas fa-rocket mr-3"></i>
                Generate SSL Certificate
                <span class="block text-sm urdu-text mt-1">SSL سرٹیفکیٹ بنائیں</span>
            </button>
        </div>
    </section>

    <!-- Progress Bar -->
    <section id="progressSection" class="py-8 bg-gray-100" style="display: none;">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="flex items-center justify-between mb-8">
                    <div id="step1" class="flex flex-col items-center step-active rounded-lg p-4 text-white">
                        <div class="w-12 h-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center mb-2">
                            <i class="fas fa-globe text-xl"></i>
                        </div>
                        <span class="font-medium">Domain</span>
                        <span class="text-xs urdu-text">ڈومین</span>
                    </div>
                    
                    <div class="flex-1 h-2 bg-gray-300 mx-4 rounded"></div>
                    
                    <div id="step2" class="flex flex-col items-center step-pending rounded-lg p-4 text-gray-600">
                        <div class="w-12 h-12 rounded-full bg-gray-400 flex items-center justify-center mb-2">
                            <i class="fas fa-check-circle text-xl text-white"></i>
                        </div>
                        <span class="font-medium">Verification</span>
                        <span class="text-xs urdu-text">تصدیق</span>
                    </div>
                    
                    <div class="flex-1 h-2 bg-gray-300 mx-4 rounded"></div>
                    
                    <div id="step3" class="flex flex-col items-center step-pending rounded-lg p-4 text-gray-600">
                        <div class="w-12 h-12 rounded-full bg-gray-400 flex items-center justify-center mb-2">
                            <i class="fas fa-certificate text-xl text-white"></i>
                        </div>
                        <span class="font-medium">Generate</span>
                        <span class="text-xs urdu-text">بنائیں</span>
                    </div>
                    
                    <div class="flex-1 h-2 bg-gray-300 mx-4 rounded"></div>
                    
                    <div id="step4" class="flex flex-col items-center step-pending rounded-lg p-4 text-gray-600">
                        <div class="w-12 h-12 rounded-full bg-gray-400 flex items-center justify-center mb-2">
                            <i class="fas fa-download text-xl text-white"></i>
                        </div>
                        <span class="font-medium">Done</span>
                        <span class="text-xs urdu-text">مکمل</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Form Section -->
    <section id="formSection" class="py-12" style="display: none;">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto">
                
                <!-- Step 1: Domain Input -->
                <div id="domainStep" class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">
                        Enter Your Domain
                    </h3>
                    <p class="text-gray-600 mb-6 urdu-text">
                        اپنا ڈومین نام درج کریں
                    </p>
                    
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Domain Name / ڈومین نام
                            </label>
                            <input type="text" id="domainInput" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                                   placeholder="example.com">
                            <p class="text-sm text-gray-500 mt-2 urdu-text">
                                مثال: example.com (بغیر https:// کے)
                            </p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Email Address / ای میل ایڈریس
                            </label>
                            <input type="email" id="emailInput"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                                   placeholder="<EMAIL>">
                            <p class="text-sm text-gray-500 mt-2 urdu-text">
                                SSL سرٹیفکیٹ کی معلومات کے لیے
                            </p>
                        </div>

                        <!-- Manual CSR Option -->
                        <div class="border-t border-gray-200 pt-6">
                            <div class="flex items-center mb-4">
                                <input type="checkbox" id="manualCSRToggle" class="mr-3" onchange="toggleManualCSR()">
                                <label for="manualCSRToggle" class="text-sm font-medium text-gray-700">
                                    Use Manual CSR (Advanced) / دستی CSR استعمال کریں
                                </label>
                            </div>

                            <div id="manualCSRSection" style="display: none;" class="space-y-4">
                                <div class="bg-blue-50 border border-blue-200 rounded p-4">
                                    <h4 class="font-medium text-blue-800 mb-2">
                                        <i class="fas fa-info-circle mr-2"></i>
                                        Manual CSR Input
                                    </h4>
                                    <p class="text-blue-700 text-sm mb-2">
                                        If automatic CSR generation fails, generate a CSR manually and paste it here.
                                    </p>
                                    <p class="text-blue-700 text-sm">
                                        Generate CSR at: <a href="https://www.ssl.com/online-csr-generator/" target="_blank" class="underline">SSL.com CSR Generator</a>
                                    </p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Certificate Signing Request (CSR)
                                    </label>
                                    <textarea id="manualCSRInput" rows="8"
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-xs"
                                              placeholder="-----BEGIN CERTIFICATE REQUEST-----
Paste your CSR here...
-----END CERTIFICATE REQUEST-----"></textarea>
                                </div>
                            </div>
                        </div>

                        <button onclick="validateDomain()"
                                class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors">
                            <i class="fas fa-arrow-right mr-2"></i>
                            Continue / جاری رکھیں
                        </button>
                    </div>
                </div>

                <!-- Step 2: Verification Method -->
                <div id="verificationStep" class="bg-white rounded-lg shadow-lg p-8" style="display: none;">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">
                        Choose Verification Method
                    </h3>
                    <p class="text-gray-600 mb-6 urdu-text">
                        تصدیق کا طریقہ منتخب کریں
                    </p>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-blue-500 transition-colors"
                             onclick="selectVerificationMethod('http')">
                            <div class="text-center">
                                <i class="fas fa-file-alt text-4xl text-blue-500 mb-4"></i>
                                <h4 class="text-lg font-semibold text-gray-800 mb-2">HTTP File Upload</h4>
                                <p class="text-sm text-gray-600 mb-2 urdu-text">HTTP فائل اپ لوڈ</p>
                                <p class="text-sm text-gray-500">Upload a verification file to your website</p>
                            </div>
                        </div>
                        
                        <div class="border-2 border-gray-200 rounded-lg p-6 cursor-pointer hover:border-green-500 transition-colors"
                             onclick="selectVerificationMethod('dns')">
                            <div class="text-center">
                                <i class="fas fa-globe text-4xl text-green-500 mb-4"></i>
                                <h4 class="text-lg font-semibold text-gray-800 mb-2">DNS TXT Record</h4>
                                <p class="text-sm text-gray-600 mb-2 urdu-text">DNS TXT ریکارڈ</p>
                                <p class="text-sm text-gray-500">Add a DNS TXT record to your domain</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Verification Instructions -->
                <div id="instructionsStep" class="bg-white rounded-lg shadow-lg p-8" style="display: none;">
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">
                        Verification Instructions
                    </h3>
                    <p class="text-gray-600 mb-6 urdu-text">
                        تصدیق کی ہدایات
                    </p>
                    
                    <div id="verificationInstructions">
                        <!-- Instructions will be populated here -->
                    </div>
                    
                    <div class="mt-8 flex space-x-4">
                        <button onclick="verifyDomain()" 
                                class="flex-1 bg-green-600 text-white py-3 px-6 rounded-lg text-lg font-semibold hover:bg-green-700 transition-colors">
                            <i class="fas fa-check mr-2"></i>
                            Verify & Generate / تصدیق کریں
                        </button>
                        <button onclick="goBack()" 
                                class="bg-gray-500 text-white py-3 px-6 rounded-lg hover:bg-gray-600 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Back / واپس
                        </button>
                    </div>
                </div>

                <!-- Step 4: Certificate Download -->
                <div id="downloadStep" class="bg-white rounded-lg shadow-lg p-8" style="display: none;">
                    <div class="text-center mb-8">
                        <i class="fas fa-check-circle text-6xl text-green-500 mb-4"></i>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">
                            SSL Certificate Generated!
                        </h3>
                        <p class="text-gray-600 urdu-text">
                            SSL سرٹیفکیٹ کامیابی سے بن گیا!
                        </p>
                    </div>
                    
                    <div class="grid md:grid-cols-3 gap-4 mb-8">
                        <button onclick="downloadFile('certificate')" 
                                class="bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>
                            certificate.crt
                        </button>
                        <button onclick="downloadFile('privatekey')" 
                                class="bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>
                            private.key
                        </button>
                        <button onclick="downloadFile('cabundle')" 
                                class="bg-orange-600 text-white py-3 px-4 rounded-lg hover:bg-orange-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>
                            ca_bundle.crt
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <button onclick="emailCertificates()" 
                                class="bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors mr-4">
                            <i class="fas fa-envelope mr-2"></i>
                            Email Certificates / ای میل کریں
                        </button>
                        <button onclick="startOver()" 
                                class="bg-gray-500 text-white py-3 px-6 rounded-lg hover:bg-gray-600 transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Generate Another / دوبارہ بنائیں
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">Why Choose SSL4Free?</h2>
                <p class="text-gray-600 urdu-text">SSL4Free کیوں منتخب کریں؟</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center p-6 card-hover">
                    <i class="fas fa-shield-alt text-4xl text-green-500 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">Trusted SSL</h3>
                    <p class="text-gray-600 urdu-text mb-2">قابل اعتماد SSL</p>
                    <p class="text-gray-500">Powered by ZeroSSL API with 99.9% browser compatibility</p>
                </div>
                
                <div class="text-center p-6 card-hover">
                    <i class="fas fa-clock text-4xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">Quick Setup</h3>
                    <p class="text-gray-600 urdu-text mb-2">فوری سیٹ اپ</p>
                    <p class="text-gray-500">Generate SSL certificates in just 5 minutes</p>
                </div>
                
                <div class="text-center p-6 card-hover">
                    <i class="fas fa-gift text-4xl text-purple-500 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">100% Free</h3>
                    <p class="text-gray-600 urdu-text mb-2">مکمل مفت</p>
                    <p class="text-gray-500">No hidden charges, completely free SSL certificates</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">SSL4Free</h3>
                    <p class="text-gray-400">Free SSL certificates for everyone</p>
                    <p class="text-gray-400 urdu-text mt-2">سب کے لیے مفت SSL سرٹیفکیٹس</p>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="index.html" class="hover:text-white">Home</a></li>
                        <li><a href="privacy.html" class="hover:text-white">Privacy Policy</a></li>
                        <li><a href="terms.html" class="hover:text-white">Terms of Service</a></li>
                        <li><a href="contact.html" class="hover:text-white">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Help Center</a></li>
                        <li><a href="#" class="hover:text-white">Installation Guide</a></li>
                        <li><a href="#" class="hover:text-white">FAQ</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Connect</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-twitter text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-facebook text-xl"></i></a>
                        <a href="#" class="text-gray-400 hover:text-white"><i class="fab fa-github text-xl"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 SSL4Free. All rights reserved. | Powered by ZeroSSL API</p>
                <p class="mt-2">From Waji with ❤️</p>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg p-8 text-center">
            <i class="fas fa-spinner fa-spin text-4xl text-blue-600 mb-4"></i>
            <p class="text-lg font-semibold text-gray-800">Processing...</p>
            <p class="text-gray-600 urdu-text">براہ کرم انتظار کریں</p>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
