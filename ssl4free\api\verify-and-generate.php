<?php
require_once 'config.php';

setApiHeaders();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo apiResponse(false, 'Method not allowed');
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo apiResponse(false, 'Invalid JSON input');
        exit();
    }
    
    // Validate required fields
    if (!isset($input['domain']) || !isset($input['email'])) {
        echo apiResponse(false, 'Missing required fields');
        exit();
    }
    
    $domain = trim($input['domain']);
    $email = trim($input['email']);
    
    // Load certificate data
    $tempData = loadCertificateData($domain);
    if (!$tempData || $tempData['validation_step'] !== 'certificate_created') {
        echo apiResponse(false, 'Certificate not created. Please start the process again.');
        exit();
    }
    
    $certificateId = $tempData['certificate_id'];
    $verificationMethod = $tempData['verification_method'];
    
    logMessage("Verifying domain and generating certificate for: $domain (ID: $certificateId)", 'INFO');
    
    // Initiate domain verification using ZeroSSL API
    $verificationResult = initiateZeroSSLVerification($certificateId, $verificationMethod, $tempData);

    if (!$verificationResult) {
        echo apiResponse(false, 'Failed to initiate domain verification. Please try again.');
        exit();
    }

    // Check certificate status and download if ready
    $certificates = checkAndDownloadCertificate($certificateId, $domain);

    if (!$certificates) {
        echo apiResponse(false, 'Certificate not ready yet. Please wait a few minutes and try again.');
        exit();
    }
    
    // Update temp data
    $tempData['validation_step'] = 'completed';
    $tempData['certificates'] = $certificates;
    $tempData['completion_time'] = time();
    
    saveCertificateData($domain, $tempData);
    
    logMessage("Certificate successfully generated for domain: $domain", 'INFO');
    
    echo apiResponse(true, 'SSL Certificate generated successfully!', [
        'certificates' => $certificates,
        'domain' => $domain,
        'valid_from' => date('Y-m-d H:i:s'),
        'valid_to' => date('Y-m-d H:i:s', strtotime('+90 days'))
    ]);
    
} catch (Exception $e) {
    logMessage("Error in verify-and-generate: " . $e->getMessage(), 'ERROR');
    echo apiResponse(false, 'Internal server error: ' . $e->getMessage());
}

function verifyDomainOwnership($domain, $certificateId, $method, $verification) {
    try {
        // First, let's check if verification is already complete
        $statusResponse = zeroSSLRequest("/certificates/$certificateId/status");
        
        if ($statusResponse && isset($statusResponse['validation']) && $statusResponse['validation']['other_methods']) {
            // Check if domain is already verified
            foreach ($statusResponse['validation']['other_methods'] as $domainValidation => $status) {
                if ($status === 'verified') {
                    logMessage("Domain $domain already verified", 'INFO');
                    return true;
                }
            }
        }
        
        // Perform verification based on method
        if ($method === 'http') {
            return verifyHttpMethod($domain, $verification);
        } else {
            return verifyDnsMethod($domain, $verification);
        }
        
    } catch (Exception $e) {
        logMessage("Error in verifyDomainOwnership: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

function verifyHttpMethod($domain, $verification) {
    try {
        // Check if the verification file is accessible
        $verificationUrl = "http://$domain/.well-known/acme-challenge/" . $verification['file_name'];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $verificationUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'SSL4Free Verification Bot');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            logMessage("HTTP verification failed for $domain: $error", 'ERROR');
            return false;
        }
        
        if ($httpCode !== 200) {
            logMessage("HTTP verification failed for $domain: HTTP $httpCode", 'ERROR');
            return false;
        }
        
        // Check if response matches expected content
        $expectedContent = trim($verification['file_content']);
        $actualContent = trim($response);
        
        if ($expectedContent === $actualContent) {
            logMessage("HTTP verification successful for $domain", 'INFO');
            return true;
        } else {
            logMessage("HTTP verification content mismatch for $domain. Expected: $expectedContent, Got: $actualContent", 'ERROR');
            return false;
        }
        
    } catch (Exception $e) {
        logMessage("Exception in verifyHttpMethod: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

function verifyDnsMethod($domain, $verification) {
    try {
        // Check DNS TXT record
        $recordName = $verification['record_name'];
        $expectedValue = $verification['record_value'];
        
        // Query DNS TXT records
        $txtRecords = dns_get_record($recordName, DNS_TXT);
        
        if (!$txtRecords) {
            logMessage("No TXT records found for $recordName", 'ERROR');
            return false;
        }
        
        // Check if expected value exists in TXT records
        foreach ($txtRecords as $record) {
            if (isset($record['txt']) && trim($record['txt']) === trim($expectedValue)) {
                logMessage("DNS verification successful for $domain", 'INFO');
                return true;
            }
        }
        
        logMessage("DNS verification failed for $domain. Expected value not found in TXT records.", 'ERROR');
        return false;
        
    } catch (Exception $e) {
        logMessage("Exception in verifyDnsMethod: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

function downloadCertificate($certificateId, $domain) {
    try {
        // Trigger certificate issuance
        $issueResponse = zeroSSLRequest("/certificates/$certificateId/download/return", 'GET');
        
        if (!$issueResponse) {
            logMessage("Failed to trigger certificate issuance for ID: $certificateId", 'ERROR');
            return false;
        }
        
        // Wait a moment for certificate to be issued
        sleep(2);
        
        // Download certificate files
        $certificateResponse = zeroSSLRequest("/certificates/$certificateId/download/return", 'GET');
        
        if (!$certificateResponse || !isset($certificateResponse['certificate.crt'])) {
            logMessage("Certificate not ready for download. ID: $certificateId", 'ERROR');
            return false;
        }
        
        // Get private key from temporary storage
        $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
        $privateKey = '';
        
        if (file_exists($keyFile)) {
            $privateKey = file_get_contents($keyFile);
            unlink($keyFile); // Clean up
        }
        
        // Prepare certificate data
        $certificates = [
            'certificate' => $certificateResponse['certificate.crt'],
            'private_key' => $privateKey,
            'ca_bundle' => $certificateResponse['ca_bundle.crt'] ?? ''
        ];
        
        // Save certificates to files for download
        $certDir = TEMP_DIR . 'certs_' . md5($domain) . '/';
        if (!file_exists($certDir)) {
            mkdir($certDir, 0755, true);
        }
        
        file_put_contents($certDir . 'certificate.crt', $certificates['certificate']);
        file_put_contents($certDir . 'private.key', $certificates['private_key']);
        file_put_contents($certDir . 'ca_bundle.crt', $certificates['ca_bundle']);
        
        logMessage("Certificate files saved for domain: $domain", 'INFO');
        
        return $certificates;
        
    } catch (Exception $e) {
        logMessage("Exception in downloadCertificate: " . $e->getMessage(), 'ERROR');
        return false;
    }
}
?>
