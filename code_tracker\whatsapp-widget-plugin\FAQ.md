# WhatsApp Widget Pro - Frequently Asked Questions

## Table of Contents
1. [General Questions](#general-questions)
2. [Setup & Configuration](#setup--configuration)
3. [Features & Functionality](#features--functionality)
4. [Troubleshooting](#troubleshooting)
5. [Advanced Usage](#advanced-usage)

## General Questions

### What is WhatsApp Widget Pro?
WhatsApp Widget Pro is a professional WordPress plugin that adds a floating WhatsApp chat button to your website. It includes advanced features like multiple agents, working hours, custom themes, analytics dashboard, and much more.

### What's the difference between the free and pro version?
The Pro version includes:
- Multiple agents support
- Working hours with timezone support
- Advanced analytics dashboard
- 6 professional themes
- Chat bubble interface
- Custom CSS editor
- Shortcode support
- Click tracking and reporting
- Premium support

### Is it compatible with my WordPress theme?
Yes! The plugin is designed to work with all WordPress themes. It uses absolute positioning and high z-index to ensure it appears above all other content.

### Does it work on mobile devices?
Absolutely! The widget is fully responsive and automatically adjusts for mobile devices. You can also set different visibility settings for mobile vs desktop users.

### Is it GDPR compliant?
Yes, the plugin is GDPR compliant. It only collects minimal analytics data (when enabled) and stores it locally in your WordPress database. No data is shared with third parties.

## Setup & Configuration

### How do I get my WhatsApp number in the correct format?
Use your WhatsApp number with the country code:
- **Pakistan:** +************
- **USA:** +14155552671
- **UK:** +************
- **India:** +************
- **UAE:** +************

### Can I add multiple WhatsApp numbers?
Yes! Go to the Agents tab and add unlimited agents with their own WhatsApp numbers, names, titles, and profile pictures.

### How do I set up working hours?
1. Go to Settings > WhatsApp Widget > Working Hours tab
2. Enable "Working Hours"
3. Select your timezone
4. Set hours for each day of the week
5. Customize the offline message

### How do I change the widget appearance?
1. Go to the Appearance tab
2. Choose from 6 built-in themes
3. Enable chat bubble interface if desired
4. Add custom CSS for advanced styling
5. Preview changes in real-time

### Can I customize the colors?
Yes! Use the Custom CSS editor in the Appearance tab:
```css
.whatsapp-widget a {
    background: #your-color !important;
}
```

## Features & Functionality

### How does the chat bubble interface work?
When enabled, clicking the widget opens a chat bubble showing all available agents. Users can choose which agent to contact based on their needs (Sales, Support, etc.).

### What analytics data is collected?
When analytics is enabled, the plugin tracks:
- Click timestamps
- Agent performance
- Page URLs where clicks occurred
- User IP addresses (for geographic insights)
- Device information (mobile vs desktop)

### How do I view analytics reports?
Go to Settings > WhatsApp Analytics to view:
- Real-time click statistics
- Daily, weekly, monthly reports
- Agent performance metrics
- Visual charts and graphs
- Recent activity logs

### Can I use shortcodes?
Yes! Use these shortcodes anywhere in your content:
```
[whatsapp_widget]
[whatsapp_widget phone="+************" message="Custom message"]
[whatsapp_widget size="70" position="static"]
```

### How do I disable the widget on specific pages?
You can use conditional logic in your theme or add custom code:
```php
// Hide on specific pages
if (is_page('contact')) {
    remove_action('wp_footer', array($whatsapp_widget, 'display_widget'));
}
```

## Troubleshooting

### The widget is not appearing on my website
**Check these common issues:**
1. Ensure the plugin is activated
2. Verify widget is enabled in settings
3. Check device visibility settings
4. Clear any caching plugins
5. Test in incognito/private browser mode
6. Check for JavaScript errors in browser console

### WhatsApp link is not working
**Phone number issues:**
- Ensure country code is included (+************)
- Remove any spaces or special characters
- Test the number manually on WhatsApp Web

**Message encoding issues:**
- Avoid special characters in default message
- Keep messages simple and clear
- Test with basic messages first

### Widget appears behind other elements
**Z-index conflicts:**
1. Add custom CSS to increase z-index:
```css
.whatsapp-widget {
    z-index: 99999 !important;
}
```
2. Check for theme elements with high z-index
3. Contact theme developer if conflicts persist

### Analytics not tracking clicks
**Common solutions:**
1. Ensure analytics is enabled in settings
2. Check if AJAX is working properly
3. Verify database permissions
4. Clear browser cache and test again
5. Check browser console for JavaScript errors

### Working hours not functioning correctly
**Timezone issues:**
1. Verify correct timezone is selected
2. Check server time vs your local time
3. Test with different time settings
4. Ensure working hours are properly configured

### Mobile display issues
**Responsive problems:**
1. Test on actual mobile devices
2. Check mobile visibility settings
3. Adjust widget size for mobile screens
4. Verify theme's mobile compatibility

## Advanced Usage

### How do I integrate with Google Analytics?
The plugin automatically tracks events if Google Analytics is installed:
```javascript
// Events are automatically sent as:
gtag('event', 'click', {
    'event_category': 'WhatsApp Widget',
    'event_label': 'Chat Button'
});
```

### Can I add custom tracking?
Yes! Use the custom tracking hook:
```javascript
window.whatsappWidgetCustomTrack = function(category, action, label) {
    // Your custom tracking code
    console.log('WhatsApp click:', category, action, label);
    
    // Send to your analytics service
    yourAnalytics.track(action, {
        category: category,
        label: label
    });
};
```

### How do I style the widget for my brand?
Use custom CSS to match your brand:
```css
/* Custom brand colors */
.whatsapp-widget a {
    background: linear-gradient(135deg, #your-color1, #your-color2) !important;
}

/* Custom hover effects */
.whatsapp-widget a:hover {
    transform: scale(1.2) !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
}

/* Custom animations */
.whatsapp-widget {
    animation: customBounce 2s infinite !important;
}

@keyframes customBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}
```

### How do I create agent-specific landing pages?
Use different shortcodes for different agents:
```
<!-- Sales page -->
[whatsapp_widget phone="+923001111111" message="I'm interested in your products"]

<!-- Support page -->
[whatsapp_widget phone="+923002222222" message="I need technical support"]
```

### Can I integrate with CRM systems?
Yes! Use the JavaScript API to send data to your CRM:
```javascript
window.whatsappWidgetCustomTrack = function(category, action, label) {
    // Send lead data to CRM
    fetch('/api/crm/lead', {
        method: 'POST',
        body: JSON.stringify({
            source: 'whatsapp_widget',
            agent: label,
            timestamp: new Date().toISOString()
        })
    });
};
```

### How do I backup and restore settings?
**Backup:**
1. Go to Preview tab
2. Click "Export Settings"
3. Save the JSON file

**Restore:**
1. Import the JSON file
2. Copy settings manually
3. Or use WordPress export/import tools

### Performance optimization tips
**For high-traffic websites:**
1. Enable caching plugins
2. Optimize database regularly
3. Use CDN for static assets
4. Minimize custom CSS
5. Clean old analytics data periodically

### Multi-language support
**For international websites:**
1. Use translation plugins (WPML, Polylang)
2. Set different agents for different languages
3. Customize messages per language
4. Use appropriate timezones per region

## Still Need Help?

### Documentation Resources
- Installation Guide
- Video Tutorials (coming soon)
- Code Examples
- Best Practices Guide

### Support Channels
- **Free Support:** WordPress.org support forum
- **Premium Support:** Priority email support for licensed users
- **Community:** User forums and discussions

### Reporting Bugs
If you find a bug, please report it with:
1. WordPress version
2. Plugin version
3. Steps to reproduce
4. Error messages
5. Browser and device information

---

**Can't find your answer?** Contact our support team or check the documentation for more detailed information.
