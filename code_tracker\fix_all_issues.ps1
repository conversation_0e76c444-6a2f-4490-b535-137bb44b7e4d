# CodeCanyon Issues Auto-Fixer
# This script fixes all CodeCanyon submission issues

Write-Host "🔧 CodeCanyon Auto-Fixer Starting..." -ForegroundColor Green

$baseDir = "C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_Fixed"
$sourceDir = "C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete"

# Create base directory structure
Write-Host "📁 Creating directory structure..." -ForegroundColor Cyan
New-Item -ItemType Directory -Path $baseDir -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir\Thumbnail" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir\Theme_Preview" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir\WordPress_Theme" -Force | Out-Null

# 1. Create proper 80x80 thumbnail
Write-Host "🖼️ Creating 80x80 thumbnail..." -ForegroundColor Yellow
Add-Type -AssemblyName System.Drawing

# Create 80x80 bitmap
$bitmap = New-Object System.Drawing.Bitmap(80, 80)
$graphics = [System.Drawing.Graphics]::FromImage($bitmap)

# Fill with WhatsApp green background
$greenBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(37, 211, 102))
$graphics.FillRectangle($greenBrush, 0, 0, 80, 80)

# Add white text
$font = New-Object System.Drawing.Font("Arial", 10, [System.Drawing.FontStyle]::Bold)
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$graphics.DrawString("WA", $font, $whiteBrush, 22, 20)
$graphics.DrawString("Pro", $font, $whiteBrush, 18, 40)

# Save as PNG
$thumbnailPath = "$baseDir\Thumbnail\thumbnail.png"
$bitmap.Save($thumbnailPath, [System.Drawing.Imaging.ImageFormat]::Png)

# Cleanup
$graphics.Dispose()
$bitmap.Dispose()
$greenBrush.Dispose()
$whiteBrush.Dispose()
$font.Dispose()

Write-Host "✅ Thumbnail created: $thumbnailPath" -ForegroundColor Green

# 2. Convert PNG screenshots to JPG
Write-Host "🎨 Converting screenshots to JPG..." -ForegroundColor Yellow

$screenshotMappings = @{
    "01_General_Settings_Tab.png" = "01_general_settings.jpg"
    "02_Agents_Management_Tab.png" = "02_agents_management.jpg"
    "03_Appearance_Themes_Tab.png" = "03_appearance_themes.jpg"
    "04_Working_Hours_Tab.png" = "04_working_hours.jpg"
    "05_Advanced_Settings_Tab.png" = "05_advanced_settings.jpg"
    "06_Preview_Tab.png" = "06_preview_tab.jpg"
}

$screenshotSourceDir = "$sourceDir\WhatsApp_Widget_Screenshots"

foreach ($mapping in $screenshotMappings.GetEnumerator()) {
    $sourcePath = "$screenshotSourceDir\$($mapping.Key)"
    $destPath = "$baseDir\Theme_Preview\$($mapping.Value)"
    
    if (Test-Path $sourcePath) {
        try {
            $image = [System.Drawing.Image]::FromFile($sourcePath)
            $image.Save($destPath, [System.Drawing.Imaging.ImageFormat]::Jpeg)
            $image.Dispose()
            Write-Host "✅ Converted: $($mapping.Key) → $($mapping.Value)" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Error converting $($mapping.Key): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "⚠️ Source file not found: $sourcePath" -ForegroundColor Yellow
    }
}

# 3. Create proper WordPress Theme ZIP structure
Write-Host "📦 Creating WordPress Theme ZIP..." -ForegroundColor Yellow

$tempPluginDir = "$baseDir\temp_plugin\whatsapp-widget-pro"
New-Item -ItemType Directory -Path $tempPluginDir -Force | Out-Null

# Copy all plugin files to temp directory
$filesToCopy = @(
    "whatsapp-chat-widget.php",
    "style.css",
    "script.js", 
    "admin-style.css",
    "admin-script.js",
    "readme.txt",
    "LICENSE.txt",
    "INSTALLATION-GUIDE.md",
    "FAQ.md",
    "DEVELOPER-GUIDE.md"
)

foreach ($file in $filesToCopy) {
    $sourcePath = "$sourceDir\$file"
    $destPath = "$tempPluginDir\$file"
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $destPath -Force
        Write-Host "✅ Copied: $file" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️ File not found: $file" -ForegroundColor Yellow
    }
}

# Copy demo folder
$demoSourceDir = "$sourceDir\demo"
$demoDestDir = "$tempPluginDir\demo"

if (Test-Path $demoSourceDir) {
    Copy-Item $demoSourceDir $demoDestDir -Recurse -Force
    Write-Host "✅ Copied: demo folder" -ForegroundColor Green
}

# Create ZIP with proper structure
$zipPath = "$baseDir\WordPress_Theme\whatsapp-widget-pro.zip"
Compress-Archive -Path "$baseDir\temp_plugin\whatsapp-widget-pro" -DestinationPath $zipPath -Force

# Cleanup temp directory
Remove-Item "$baseDir\temp_plugin" -Recurse -Force

Write-Host "✅ WordPress Theme ZIP created: $zipPath" -ForegroundColor Green

# 4. Verify file sizes
Write-Host "📊 Verifying file sizes..." -ForegroundColor Cyan

$thumbnailSize = (Get-Item $thumbnailPath).Length
$thumbnailSizeKB = [Math]::Round($thumbnailSize / 1KB, 1)

Write-Host "📸 Thumbnail size: $thumbnailSizeKB KB" -ForegroundColor White

if ($thumbnailSizeKB -le 50) {
    Write-Host "✅ Thumbnail size OK (under 50KB)" -ForegroundColor Green
} else {
    Write-Host "❌ Thumbnail too large (over 50KB)" -ForegroundColor Red
}

# List all created files
Write-Host "`n📋 Files created:" -ForegroundColor Cyan
Get-ChildItem $baseDir -Recurse | ForEach-Object {
    if (-not $_.PSIsContainer) {
        $sizeKB = [Math]::Round($_.Length / 1KB, 1)
        Write-Host "   📄 $($_.FullName.Replace($baseDir, '')) ($sizeKB KB)" -ForegroundColor White
    }
}

Write-Host "`n🎉 All CodeCanyon issues fixed!" -ForegroundColor Green
Write-Host "📁 Fixed files location: $baseDir" -ForegroundColor Cyan

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
Read-Host "Press Enter to continue"
