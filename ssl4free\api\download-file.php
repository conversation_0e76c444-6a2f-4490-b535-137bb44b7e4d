<?php
require_once 'config.php';

// Security check - only allow downloads from temp directory
if (!isset($_GET['file']) || !isset($_GET['name'])) {
    http_response_code(400);
    echo 'Missing file parameters';
    exit();
}

$filepath = $_GET['file'];
$filename = $_GET['name'];

// Security validation
if (!file_exists($filepath) || strpos(realpath($filepath), realpath(TEMP_DIR)) !== 0) {
    http_response_code(404);
    echo 'File not found or access denied';
    exit();
}

// Validate filename
$allowedExtensions = ['crt', 'key', 'pem', 'txt'];
$fileExtension = pathinfo($filename, PATHINFO_EXTENSION);

if (!in_array($fileExtension, $allowedExtensions)) {
    http_response_code(403);
    echo 'File type not allowed';
    exit();
}

// Set appropriate headers for download
$mimeTypes = [
    'crt' => 'application/x-x509-ca-cert',
    'key' => 'application/x-pem-file',
    'pem' => 'application/x-pem-file',
    'txt' => 'text/plain'
];

$mimeType = $mimeTypes[$fileExtension] ?? 'application/octet-stream';

header('Content-Type: ' . $mimeType);
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($filepath));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// Output file content
readfile($filepath);

// Log download
logMessage("File downloaded: $filename", 'INFO');

// Clean up file after download (optional)
// unlink($filepath);
?>
