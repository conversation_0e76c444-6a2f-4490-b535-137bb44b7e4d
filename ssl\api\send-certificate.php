<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!$input || !isset($input['email']) || !isset($input['domain']) || !isset($input['certificate']) || !isset($input['privateKey'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit();
}

$email = filter_var($input['email'], FILTER_VALIDATE_EMAIL);
$domain = htmlspecialchars($input['domain']);
$certificate = $input['certificate'];
$privateKey = $input['privateKey'];
$caBundle = isset($input['caBundle']) ? $input['caBundle'] : '';
$fullChain = isset($input['fullChain']) ? $input['fullChain'] : '';

if (!$email) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid email address']);
    exit();
}

// Email configuration
$to = $email;
$subject = "Your SSL Certificate for " . $domain;

// Create email body
$message = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Your SSL Certificate</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
        .certificate-box { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 4px; margin: 10px 0; }
        .certificate-content { font-family: monospace; font-size: 12px; background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .warning { background: #fef2f2; border: 1px solid #fecaca; color: #dc2626; padding: 15px; border-radius: 4px; margin: 15px 0; }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🔒 Your SSL Certificate is Ready!</h1>
            <p>SSL Certificate for: <strong>" . $domain . "</strong></p>
        </div>
        
        <div class='content'>
            <h2>Congratulations!</h2>
            <p>Your SSL certificate has been successfully generated. Please find your certificate and private key below:</p>
            
            <div class='certificate-box'>
                <h3>🔒 SSL Certificate (CRT)</h3>
                <div class='certificate-content'>" . htmlspecialchars($certificate) . "</div>
            </div>

            <div class='certificate-box'>
                <h3>🔑 Private Key (KEY)</h3>
                <div class='certificate-content'>" . htmlspecialchars($privateKey) . "</div>
            </div>";

if (!empty($caBundle)) {
    $message .= "
            <div class='certificate-box'>
                <h3>🛡️ Certificate Authority Bundle (CABUNDLE)</h3>
                <div class='certificate-content'>" . htmlspecialchars($caBundle) . "</div>
            </div>";
}

$message .= "
            
            <div class='warning'>
                <h3>⚠️ Important Security Notice</h3>
                <ul>
                    <li>Keep your private key secure and never share it publicly</li>
                    <li>Store both files in a safe location on your server</li>
                    <li>This certificate is valid for 90 days</li>
                    <li>Set up automatic renewal before expiration</li>
                </ul>
            </div>
            
            <h3>📋 Installation Instructions</h3>
            <ol>
                <li><strong>Save the files:</strong>
                    <ul>
                        <li>Certificate: <code>" . $domain . ".crt</code></li>
                        <li>Private Key: <code>" . $domain . ".key</code></li>
                        <li>CA Bundle: <code>" . $domain . "_ca_bundle.crt</code></li>
                    </ul>
                </li>
                <li><strong>Upload to your web server</strong> in a secure directory</li>
                <li><strong>Configure your web server:</strong>
                    <ul>
                        <li><strong>Apache:</strong> Update SSLCertificateFile, SSLCertificateKeyFile, and SSLCertificateChainFile</li>
                        <li><strong>Nginx:</strong> Update ssl_certificate and ssl_certificate_key directives</li>
                    </ul>
                </li>
                <li><strong>Test your SSL installation</strong> using online SSL checkers</li>
                <li><strong>Set up automatic renewal</strong> before the 90-day expiration</li>
            </ol>

            <div style='background: #f0f9ff; border: 1px solid #0ea5e9; padding: 15px; border-radius: 4px; margin: 15px 0;'>
                <h4 style='color: #0369a1; margin-top: 0;'>💡 Pro Tip</h4>
                <p style='margin-bottom: 0; color: #0369a1;'>
                    The CA Bundle file ensures maximum browser compatibility. Always include it in your SSL configuration for the best results.
                </p>
            </div>
            
            <h3>Need Help?</h3>
            <p>If you need assistance with SSL installation, please refer to your web hosting provider's documentation or contact their support team.</p>
        </div>
        
        <div class='footer'>
            <p>This email was sent from SSL Generator Tool</p>
            <p>Generated on: " . date('Y-m-d H:i:s') . " UTC</p>
        </div>
    </div>
</body>
</html>
";

// Email headers
$headers = array(
    'MIME-Version: 1.0',
    'Content-type: text/html; charset=UTF-8',
    'From: SSL Generator <<EMAIL>>',
    'Reply-To: <EMAIL>',
    'X-Mailer: PHP/' . phpversion()
);

// Try to send email
try {
    $mailSent = mail($to, $subject, $message, implode("\r\n", $headers));
    
    if ($mailSent) {
        // Log successful email (optional)
        error_log("SSL Certificate sent to: " . $email . " for domain: " . $domain);
        
        echo json_encode([
            'success' => true,
            'message' => 'Certificate sent successfully to ' . $email
        ]);
    } else {
        // Log failed email
        error_log("Failed to send SSL Certificate to: " . $email . " for domain: " . $domain);
        
        echo json_encode([
            'success' => false,
            'message' => 'Failed to send email. Please check your email address and try again.'
        ]);
    }
} catch (Exception $e) {
    error_log("Email sending error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while sending the email.'
    ]);
}
?>
