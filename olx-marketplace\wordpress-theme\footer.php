</main><!-- #main -->

<!-- Footer -->
<footer class="footer">
    <div class="container">
        <div class="footer-content">
            <div class="footer-section">
                <h3><?php bloginfo('name'); ?></h3>
                <p><?php echo esc_html(get_bloginfo('description', 'display')); ?></p>
                <?php if (!get_bloginfo('description')) : ?>
                    <p>Pakistan's largest marketplace for buying and selling everything.</p>
                <?php endif; ?>
                
                <div class="social-links">
                    <?php
                    // You can customize these social links in WordPress Customizer
                    $facebook_url = get_theme_mod('facebook_url', '#');
                    $twitter_url = get_theme_mod('twitter_url', '#');
                    $instagram_url = get_theme_mod('instagram_url', '#');
                    ?>
                    <a href="<?php echo esc_url($facebook_url); ?>" target="_blank" rel="noopener"><i class="fab fa-facebook"></i></a>
                    <a href="<?php echo esc_url($twitter_url); ?>" target="_blank" rel="noopener"><i class="fab fa-twitter"></i></a>
                    <a href="<?php echo esc_url($instagram_url); ?>" target="_blank" rel="noopener"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
            
            <div class="footer-section">
                <h4>Popular Categories</h4>
                <ul>
                    <?php
                    $popular_categories = get_terms(array(
                        'taxonomy' => 'product_category',
                        'hide_empty' => false,
                        'number' => 4,
                        'orderby' => 'count',
                        'order' => 'DESC'
                    ));
                    
                    if (!empty($popular_categories)) :
                        foreach ($popular_categories as $category) :
                            ?>
                            <li><a href="<?php echo esc_url(get_term_link($category)); ?>"><?php echo esc_html($category->name); ?></a></li>
                            <?php
                        endforeach;
                    else :
                        // Default links if no categories exist
                        ?>
                        <li><a href="#">Cars</a></li>
                        <li><a href="#">Mobiles</a></li>
                        <li><a href="#">Houses for Sale</a></li>
                        <li><a href="#">Jobs</a></li>
                        <?php
                    endif;
                    ?>
                </ul>
            </div>
            
            <div class="footer-section">
                <h4>Popular Cities</h4>
                <ul>
                    <li><a href="<?php echo esc_url(add_query_arg('location', 'karachi', home_url())); ?>">Karachi</a></li>
                    <li><a href="<?php echo esc_url(add_query_arg('location', 'lahore', home_url())); ?>">Lahore</a></li>
                    <li><a href="<?php echo esc_url(add_query_arg('location', 'islamabad', home_url())); ?>">Islamabad</a></li>
                    <li><a href="<?php echo esc_url(add_query_arg('location', 'rawalpindi', home_url())); ?>">Rawalpindi</a></li>
                </ul>
            </div>
            
            <div class="footer-section">
                <h4>Help & Support</h4>
                <ul>
                    <?php
                    // Create these pages in WordPress admin
                    $help_page = get_page_by_path('help');
                    $privacy_page = get_page_by_path('privacy-policy');
                    $contact_page = get_page_by_path('contact');
                    ?>
                    
                    <?php if ($help_page) : ?>
                        <li><a href="<?php echo esc_url(get_permalink($help_page)); ?>">Help</a></li>
                    <?php else : ?>
                        <li><a href="#">Help</a></li>
                    <?php endif; ?>
                    
                    <li><a href="<?php echo esc_url(get_sitemap_url()); ?>">Sitemap</a></li>
                    
                    <?php if ($privacy_page) : ?>
                        <li><a href="<?php echo esc_url(get_permalink($privacy_page)); ?>">Legal & Privacy</a></li>
                    <?php else : ?>
                        <li><a href="<?php echo esc_url(get_privacy_policy_url()); ?>">Legal & Privacy</a></li>
                    <?php endif; ?>
                    
                    <?php if ($contact_page) : ?>
                        <li><a href="<?php echo esc_url(get_permalink($contact_page)); ?>">Contact Us</a></li>
                    <?php else : ?>
                        <li><a href="#">Contact Us</a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
        
        <!-- Footer Widgets -->
        <?php if (is_active_sidebar('footer-widgets')) : ?>
            <div class="footer-widgets">
                <?php dynamic_sidebar('footer-widgets'); ?>
            </div>
        <?php endif; ?>
        
        <div class="footer-bottom">
            <p>&copy; <?php echo esc_html(date('Y')); ?> <?php bloginfo('name'); ?>. All rights reserved.</p>
            <?php
            // Display footer menu if it exists
            if (has_nav_menu('footer')) :
                wp_nav_menu(array(
                    'theme_location' => 'footer',
                    'menu_class' => 'footer-menu',
                    'container' => 'nav',
                    'container_class' => 'footer-navigation',
                    'depth' => 1,
                ));
            endif;
            ?>
        </div>
    </div>
</footer>

<!-- Back to Top Button -->
<button id="backToTop" class="back-to-top" style="display: none; position: fixed; bottom: 20px; right: 20px; background: #002f34; color: white; border: none; border-radius: 50%; width: 50px; height: 50px; cursor: pointer; z-index: 1000; transition: all 0.3s;">
    <i class="fas fa-arrow-up"></i>
</button>

<!-- WordPress Footer -->
<?php wp_footer(); ?>

<script>
// Back to top functionality
document.addEventListener('DOMContentLoaded', function() {
    const backToTopBtn = document.getElementById('backToTop');
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Enhanced search functionality for WordPress
    const searchInput = document.getElementById('searchInput');
    const locationSelect = document.getElementById('locationSelect');
    const searchBtn = document.querySelector('.search-btn');
    
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            performSearch();
        });
    }
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    }
    
    function performSearch() {
        const searchTerm = searchInput.value.trim();
        const location = locationSelect.value;
        
        let searchUrl = '<?php echo esc_url(home_url('/')); ?>';
        
        if (searchTerm) {
            searchUrl += '?s=' + encodeURIComponent(searchTerm);
            searchUrl += '&post_type=product';
            
            if (location) {
                searchUrl += '&location=' + encodeURIComponent(location);
            }
        } else if (location) {
            searchUrl += '?location=' + encodeURIComponent(location);
            searchUrl += '&post_type=product';
        }
        
        window.location.href = searchUrl;
    }
    
    // Modal functionality for non-logged-in users
    <?php if (!is_user_logged_in()) : ?>
    const loginModal = document.getElementById('loginModal');
    const registerModal = document.getElementById('registerModal');
    const loginLink = document.getElementById('loginLink');
    const sellBtn = document.getElementById('sellBtn');
    const showRegister = document.getElementById('showRegister');
    const showLogin = document.getElementById('showLogin');
    
    if (loginLink) {
        loginLink.addEventListener('click', function(e) {
            e.preventDefault();
            loginModal.style.display = 'block';
        });
    }
    
    if (sellBtn) {
        sellBtn.addEventListener('click', function(e) {
            e.preventDefault();
            loginModal.style.display = 'block';
        });
    }
    
    if (showRegister) {
        showRegister.addEventListener('click', function(e) {
            e.preventDefault();
            loginModal.style.display = 'none';
            registerModal.style.display = 'block';
        });
    }
    
    if (showLogin) {
        showLogin.addEventListener('click', function(e) {
            e.preventDefault();
            registerModal.style.display = 'none';
            loginModal.style.display = 'block';
        });
    }
    
    // Close modals
    document.querySelectorAll('.close').forEach(function(closeBtn) {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });
    
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });
    <?php endif; ?>
});
</script>

</body>
</html>
