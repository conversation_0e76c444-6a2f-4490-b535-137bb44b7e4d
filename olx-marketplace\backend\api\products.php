<?php
/**
 * Products API Endpoints
 * OLX Marketplace Backend
 */

header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';
require_once '../classes/Product.php';
require_once '../classes/User.php';

$database = new Database();
$db = $database->getConnection();
$product = new Product($db);

$action = isset($_GET['action']) ? $_GET['action'] : '';

switch($action) {
    case 'create':
        createProduct();
        break;
    case 'read':
        getProducts();
        break;
    case 'read-one':
        getProduct();
        break;
    case 'update':
        updateProduct();
        break;
    case 'delete':
        deleteProduct();
        break;
    case 'mark-sold':
        markAsSold();
        break;
    case 'my-products':
        getMyProducts();
        break;
    case 'upload-images':
        uploadImages();
        break;
    case 'search':
        searchProducts();
        break;
    case 'featured':
        getFeaturedProducts();
        break;
    default:
        http_response_code(400);
        echo json_encode(array("message" => "Invalid action"));
        break;
}

function createProduct() {
    global $product;
    
    session_start();
    if(!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(array("message" => "Authentication required"));
        return;
    }

    $data = json_decode(file_get_contents("php://input"));
    
    if(!$data) {
        http_response_code(400);
        echo json_encode(array("message" => "Invalid JSON data"));
        return;
    }

    // Validate input
    $validation_errors = Product::validateProduct((array)$data);
    if(!empty($validation_errors)) {
        http_response_code(400);
        echo json_encode(array("message" => "Validation failed", "errors" => $validation_errors));
        return;
    }

    // Set product properties
    $product->user_id = $_SESSION['user_id'];
    $product->category_id = $data->category_id;
    $product->title = $data->title;
    $product->description = $data->description;
    $product->price = $data->price;
    $product->condition_type = isset($data->condition_type) ? $data->condition_type : 'used';
    $product->location = $data->location;
    $product->contact_phone = isset($data->contact_phone) ? $data->contact_phone : '';
    $product->contact_email = isset($data->contact_email) ? $data->contact_email : $_SESSION['email'];

    if($product->create()) {
        http_response_code(201);
        echo json_encode(array(
            "message" => "Product created successfully",
            "product_id" => $product->id,
            "slug" => $product->slug
        ));
    } else {
        http_response_code(500);
        echo json_encode(array("message" => "Product creation failed"));
    }
}

function getProducts() {
    global $product;
    
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : PRODUCTS_PER_PAGE;
    
    // Build filters array
    $filters = [];
    if(isset($_GET['search'])) $filters['search'] = $_GET['search'];
    if(isset($_GET['category_id'])) $filters['category_id'] = $_GET['category_id'];
    if(isset($_GET['location'])) $filters['location'] = $_GET['location'];
    if(isset($_GET['min_price'])) $filters['min_price'] = $_GET['min_price'];
    if(isset($_GET['max_price'])) $filters['max_price'] = $_GET['max_price'];
    if(isset($_GET['condition'])) $filters['condition'] = $_GET['condition'];
    if(isset($_GET['sort_by'])) $filters['sort_by'] = $_GET['sort_by'];
    if(isset($_GET['sort_order'])) $filters['sort_order'] = $_GET['sort_order'];

    $products = $product->read($page, $limit, $filters);
    $total_count = $product->getTotalCount($filters);
    $total_pages = ceil($total_count / $limit);

    http_response_code(200);
    echo json_encode(array(
        "products" => $products,
        "pagination" => array(
            "current_page" => $page,
            "total_pages" => $total_pages,
            "total_count" => $total_count,
            "per_page" => $limit
        )
    ));
}

function getProduct() {
    global $product;
    
    $identifier = isset($_GET['id']) ? $_GET['id'] : (isset($_GET['slug']) ? $_GET['slug'] : '');
    $by_slug = isset($_GET['slug']);
    
    if(empty($identifier)) {
        http_response_code(400);
        echo json_encode(array("message" => "Product ID or slug is required"));
        return;
    }

    $product_data = $product->readOne($identifier, $by_slug);
    
    if($product_data) {
        http_response_code(200);
        echo json_encode($product_data);
    } else {
        http_response_code(404);
        echo json_encode(array("message" => "Product not found"));
    }
}

function updateProduct() {
    global $product;
    
    session_start();
    if(!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(array("message" => "Authentication required"));
        return;
    }

    $product_id = isset($_GET['id']) ? $_GET['id'] : '';
    if(empty($product_id)) {
        http_response_code(400);
        echo json_encode(array("message" => "Product ID is required"));
        return;
    }

    $data = json_decode(file_get_contents("php://input"));
    
    if(!$data) {
        http_response_code(400);
        echo json_encode(array("message" => "Invalid JSON data"));
        return;
    }

    // Validate input
    $validation_errors = Product::validateProduct((array)$data);
    if(!empty($validation_errors)) {
        http_response_code(400);
        echo json_encode(array("message" => "Validation failed", "errors" => $validation_errors));
        return;
    }

    // Set product properties
    $product->id = $product_id;
    $product->user_id = $_SESSION['user_id'];
    $product->category_id = $data->category_id;
    $product->title = $data->title;
    $product->description = $data->description;
    $product->price = $data->price;
    $product->condition_type = isset($data->condition_type) ? $data->condition_type : 'used';
    $product->location = $data->location;
    $product->contact_phone = isset($data->contact_phone) ? $data->contact_phone : '';
    $product->contact_email = isset($data->contact_email) ? $data->contact_email : $_SESSION['email'];

    if($product->update()) {
        http_response_code(200);
        echo json_encode(array("message" => "Product updated successfully"));
    } else {
        http_response_code(500);
        echo json_encode(array("message" => "Product update failed"));
    }
}

function deleteProduct() {
    global $product;
    
    session_start();
    if(!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(array("message" => "Authentication required"));
        return;
    }

    $product_id = isset($_GET['id']) ? $_GET['id'] : '';
    if(empty($product_id)) {
        http_response_code(400);
        echo json_encode(array("message" => "Product ID is required"));
        return;
    }

    $product->id = $product_id;
    $product->user_id = $_SESSION['user_id'];

    if($product->delete()) {
        http_response_code(200);
        echo json_encode(array("message" => "Product deleted successfully"));
    } else {
        http_response_code(500);
        echo json_encode(array("message" => "Product deletion failed"));
    }
}

function markAsSold() {
    global $product;
    
    session_start();
    if(!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(array("message" => "Authentication required"));
        return;
    }

    $product_id = isset($_GET['id']) ? $_GET['id'] : '';
    if(empty($product_id)) {
        http_response_code(400);
        echo json_encode(array("message" => "Product ID is required"));
        return;
    }

    $product->id = $product_id;
    $product->user_id = $_SESSION['user_id'];

    if($product->markAsSold()) {
        http_response_code(200);
        echo json_encode(array("message" => "Product marked as sold"));
    } else {
        http_response_code(500);
        echo json_encode(array("message" => "Failed to mark product as sold"));
    }
}

function getMyProducts() {
    global $product;
    
    session_start();
    if(!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(array("message" => "Authentication required"));
        return;
    }

    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : PRODUCTS_PER_PAGE;
    
    $filters = ['user_id' => $_SESSION['user_id']];
    if(isset($_GET['status'])) {
        if($_GET['status'] == 'sold') {
            $filters['is_sold'] = 1;
        } elseif($_GET['status'] == 'active') {
            $filters['is_sold'] = 0;
        }
    }

    $products = $product->read($page, $limit, $filters);
    $total_count = $product->getTotalCount($filters);
    $total_pages = ceil($total_count / $limit);

    http_response_code(200);
    echo json_encode(array(
        "products" => $products,
        "pagination" => array(
            "current_page" => $page,
            "total_pages" => $total_pages,
            "total_count" => $total_count,
            "per_page" => $limit
        )
    ));
}

function uploadImages() {
    session_start();
    if(!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(array("message" => "Authentication required"));
        return;
    }

    $product_id = isset($_POST['product_id']) ? $_POST['product_id'] : '';
    if(empty($product_id)) {
        http_response_code(400);
        echo json_encode(array("message" => "Product ID is required"));
        return;
    }

    if(!isset($_FILES['images']) || empty($_FILES['images']['name'][0])) {
        http_response_code(400);
        echo json_encode(array("message" => "No images uploaded"));
        return;
    }

    $upload_dir = '../uploads/products/';
    if(!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    $uploaded_images = [];
    $errors = [];

    foreach($_FILES['images']['name'] as $key => $name) {
        if($_FILES['images']['error'][$key] == 0) {
            $file_size = $_FILES['images']['size'][$key];
            $file_tmp = $_FILES['images']['tmp_name'][$key];
            $file_ext = strtolower(pathinfo($name, PATHINFO_EXTENSION));

            // Validate file
            if($file_size > MAX_FILE_SIZE) {
                $errors[] = "File $name is too large";
                continue;
            }

            if(!in_array($file_ext, ALLOWED_EXTENSIONS)) {
                $errors[] = "File $name has invalid extension";
                continue;
            }

            // Generate unique filename
            $new_filename = uniqid() . '_' . $product_id . '.' . $file_ext;
            $upload_path = $upload_dir . $new_filename;

            if(move_uploaded_file($file_tmp, $upload_path)) {
                // Resize image
                resize_image($upload_path, $upload_path, 800, 600);

                // Save to database
                $db = new Database();
                $conn = $db->getConnection();

                $query = "INSERT INTO product_images (product_id, image_path, alt_text, is_primary)
                         VALUES (:product_id, :image_path, :alt_text, :is_primary)";

                $stmt = $conn->prepare($query);
                $is_primary = empty($uploaded_images) ? 1 : 0; // First image is primary

                $stmt->bindParam(":product_id", $product_id);
                $stmt->bindParam(":image_path", $new_filename);
                $stmt->bindParam(":alt_text", $name);
                $stmt->bindParam(":is_primary", $is_primary);

                if($stmt->execute()) {
                    $uploaded_images[] = array(
                        'id' => $conn->lastInsertId(),
                        'filename' => $new_filename,
                        'original_name' => $name,
                        'is_primary' => $is_primary
                    );
                }
            } else {
                $errors[] = "Failed to upload $name";
            }
        }
    }

    if(!empty($uploaded_images)) {
        http_response_code(200);
        echo json_encode(array(
            "message" => "Images uploaded successfully",
            "images" => $uploaded_images,
            "errors" => $errors
        ));
    } else {
        http_response_code(500);
        echo json_encode(array(
            "message" => "No images were uploaded",
            "errors" => $errors
        ));
    }
}

function searchProducts() {
    global $product;

    $search_term = isset($_GET['q']) ? $_GET['q'] : '';
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : PRODUCTS_PER_PAGE;

    if(empty($search_term)) {
        http_response_code(400);
        echo json_encode(array("message" => "Search term is required"));
        return;
    }

    $filters = ['search' => $search_term];
    if(isset($_GET['category_id'])) $filters['category_id'] = $_GET['category_id'];
    if(isset($_GET['location'])) $filters['location'] = $_GET['location'];

    $products = $product->read($page, $limit, $filters);
    $total_count = $product->getTotalCount($filters);
    $total_pages = ceil($total_count / $limit);

    // Log search for analytics
    logSearch($search_term, $filters, $total_count);

    http_response_code(200);
    echo json_encode(array(
        "products" => $products,
        "search_term" => $search_term,
        "pagination" => array(
            "current_page" => $page,
            "total_pages" => $total_pages,
            "total_count" => $total_count,
            "per_page" => $limit
        )
    ));
}

function getFeaturedProducts() {
    global $product;

    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 8;

    $filters = ['is_featured' => 1];
    $products = $product->read(1, $limit, $filters);

    http_response_code(200);
    echo json_encode(array("products" => $products));
}

function logSearch($search_term, $filters, $results_count) {
    session_start();

    $db = new Database();
    $conn = $db->getConnection();

    $query = "INSERT INTO search_logs (user_id, search_term, category_id, location, results_count, ip_address, user_agent)
              VALUES (:user_id, :search_term, :category_id, :location, :results_count, :ip_address, :user_agent)";

    $stmt = $conn->prepare($query);

    $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
    $category_id = isset($filters['category_id']) ? $filters['category_id'] : null;
    $location = isset($filters['location']) ? $filters['location'] : null;
    $ip_address = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'];

    $stmt->bindParam(":user_id", $user_id);
    $stmt->bindParam(":search_term", $search_term);
    $stmt->bindParam(":category_id", $category_id);
    $stmt->bindParam(":location", $location);
    $stmt->bindParam(":results_count", $results_count);
    $stmt->bindParam(":ip_address", $ip_address);
    $stmt->bindParam(":user_agent", $user_agent);

    $stmt->execute();
}
?>
