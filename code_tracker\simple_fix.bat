@echo off
echo 🔧 CodeCanyon Simple Fixer
echo =========================

set "BASE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_Fixed"
set "SOURCE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete"

echo 📁 Creating folder structure...
if not exist "%BASE_DIR%" mkdir "%BASE_DIR%"
if not exist "%BASE_DIR%\Thumbnail" mkdir "%BASE_DIR%\Thumbnail"
if not exist "%BASE_DIR%\Theme_Preview" mkdir "%BASE_DIR%\Theme_Preview"
if not exist "%BASE_DIR%\WordPress_Theme" mkdir "%BASE_DIR%\WordPress_Theme"

echo 📦 Creating WordPress Theme proper structure...
if not exist "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro" mkdir "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro"

echo 📄 Copying plugin files...
copy "%SOURCE_DIR%\whatsapp-chat-widget.php" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\style.css" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\script.js" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\admin-style.css" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\admin-script.js" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\readme.txt" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\LICENSE.txt" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\INSTALLATION-GUIDE.md" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\FAQ.md" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\DEVELOPER-GUIDE.md" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul

echo 🌐 Copying demo folder...
if not exist "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\demo" mkdir "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\demo"
copy "%SOURCE_DIR%\demo\index.html" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\demo\" >nul

echo 📦 Creating ZIP file...
powershell -Command "Compress-Archive -Path '%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro' -DestinationPath '%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -Force"

echo 🗑️ Cleaning up folder (keeping only ZIP)...
rmdir /s /q "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro"

echo ✅ Basic structure created!
echo.
echo 📋 What's been fixed:
echo    ✅ Proper folder structure created
echo    ✅ WordPress Theme ZIP with correct structure
echo    ✅ All plugin files copied
echo.
echo ⚠️ MANUAL TASKS REMAINING:
echo    1. Create 80x80 thumbnail.png in Thumbnail folder
echo    2. Convert PNG screenshots to JPG in Theme_Preview folder
echo    3. Set Compatible Browsers in form (all browsers)
echo    4. Set ThemeForest Files in form (CSS, JS, PHP)
echo.
echo 📁 Location: %BASE_DIR%
pause
