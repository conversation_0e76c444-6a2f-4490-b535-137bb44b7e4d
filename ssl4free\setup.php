<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL4Free Setup - Configure ZeroSSL API Key</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center space-x-3">
                <i class="fas fa-cog text-3xl"></i>
                <div>
                    <h1 class="text-3xl font-bold">SSL4Free Setup</h1>
                    <p class="text-blue-100">Configure ZeroSSL API Key</p>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-12">
        <div class="max-w-2xl mx-auto">
            
            <?php
            // Handle form submission
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['api_key'])) {
                $apiKey = trim($_POST['api_key']);
                
                if (empty($apiKey)) {
                    $error = "Please enter a valid API key";
                } else {
                    // Test the API key
                    $testResult = testZeroSSLApiKey($apiKey);
                    
                    if ($testResult['success']) {
                        // Update config file
                        $configUpdated = updateConfigFile($apiKey);
                        
                        if ($configUpdated) {
                            $success = "API key configured successfully! You can now use SSL4Free.";
                        } else {
                            $error = "Failed to update config file. Please check file permissions.";
                        }
                    } else {
                        $error = "Invalid API key: " . $testResult['error'];
                    }
                }
            }
            
            // Check current API key status
            $currentStatus = checkCurrentApiKey();
            ?>
            
            <!-- Current Status -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    Current Status
                </h2>
                
                <?php if ($currentStatus['configured']): ?>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                            <div>
                                <h3 class="font-medium text-green-800">API Key Configured</h3>
                                <p class="text-green-700 text-sm">ZeroSSL API is ready to use!</p>
                                <p class="text-green-600 text-xs mt-1">Key: <?php echo substr($currentStatus['key'], 0, 10) . '...'; ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <a href="index.html" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-arrow-right mr-2"></i>
                            Go to SSL4Free
                        </a>
                    </div>
                <?php else: ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i>
                            <div>
                                <h3 class="font-medium text-red-800">API Key Not Configured</h3>
                                <p class="text-red-700 text-sm">Please configure your ZeroSSL API key below.</p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Setup Form -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">
                    <i class="fas fa-key text-yellow-600 mr-2"></i>
                    Configure ZeroSSL API Key
                </h2>
                
                <!-- Instructions -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                    <h3 class="font-semibold text-blue-800 mb-3">How to get your ZeroSSL API Key:</h3>
                    <ol class="list-decimal list-inside text-blue-700 space-y-2">
                        <li>Visit <a href="https://app.zerossl.com/developer" target="_blank" class="underline hover:text-blue-900">ZeroSSL Developer Portal</a></li>
                        <li>Sign up for a free account (if you don't have one)</li>
                        <li>Go to the "Developer" section</li>
                        <li>Click "Create API Key" or copy your existing key</li>
                        <li>Paste the API key in the form below</li>
                    </ol>
                </div>
                
                <!-- Error/Success Messages -->
                <?php if (isset($error)): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                            <span class="text-red-800"><?php echo htmlspecialchars($error); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($success)): ?>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span class="text-green-800"><?php echo htmlspecialchars($success); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Form -->
                <form method="POST" class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            ZeroSSL API Key
                        </label>
                        <input type="text" name="api_key" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono"
                               placeholder="Enter your ZeroSSL API key here..."
                               value="<?php echo isset($_POST['api_key']) ? htmlspecialchars($_POST['api_key']) : ''; ?>">
                        <p class="text-sm text-gray-500 mt-2">
                            Your API key should be a long string of letters and numbers (e.g., abc123def456...)
                        </p>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        Test & Save API Key
                    </button>
                </form>
                
                <!-- Additional Info -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <h3 class="font-semibold text-gray-800 mb-3">Important Notes:</h3>
                    <ul class="text-gray-600 space-y-1 text-sm">
                        <li>• ZeroSSL API is completely free for basic usage</li>
                        <li>• Your API key is stored securely on your server</li>
                        <li>• You can change your API key anytime by visiting this page</li>
                        <li>• Free accounts have rate limits (sufficient for most users)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 SSL4Free. All rights reserved.</p>
            <p class="mt-2">From Waji with ❤️</p>
        </div>
    </footer>
</body>
</html>

<?php
function checkCurrentApiKey() {
    $configFile = __DIR__ . '/api/config.php';
    
    if (!file_exists($configFile)) {
        return ['configured' => false, 'key' => null];
    }
    
    $content = file_get_contents($configFile);
    
    // Extract API key from config
    if (preg_match("/define\('ZEROSSL_API_KEY',\s*'([^']+)'\);/", $content, $matches)) {
        $apiKey = $matches[1];
        
        if ($apiKey !== 'YOUR_ZEROSSL_API_KEY_HERE' && !empty($apiKey)) {
            return ['configured' => true, 'key' => $apiKey];
        }
    }
    
    return ['configured' => false, 'key' => null];
}

function testZeroSSLApiKey($apiKey) {
    $url = 'https://api.zerossl.com/certificates?limit=1';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $apiKey,
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['success' => false, 'error' => 'Connection error: ' . $error];
    }
    
    if ($httpCode === 200) {
        return ['success' => true, 'error' => null];
    } elseif ($httpCode === 401) {
        return ['success' => false, 'error' => 'Invalid API key'];
    } else {
        return ['success' => false, 'error' => 'HTTP ' . $httpCode . ': ' . $response];
    }
}

function updateConfigFile($apiKey) {
    $configFile = __DIR__ . '/api/config.php';
    
    if (!file_exists($configFile)) {
        return false;
    }
    
    $content = file_get_contents($configFile);
    
    // Replace the API key
    $newContent = preg_replace(
        "/define\('ZEROSSL_API_KEY',\s*'[^']*'\);/",
        "define('ZEROSSL_API_KEY', '" . addslashes($apiKey) . "');",
        $content
    );
    
    if ($newContent === $content) {
        return false; // No replacement made
    }
    
    return file_put_contents($configFile, $newContent) !== false;
}
?>
