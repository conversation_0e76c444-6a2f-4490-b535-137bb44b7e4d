// SSL4Free Main Application JavaScript
let currentStep = 1;
let domainData = {};
let verificationMethod = '';
let certificateData = {};

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    console.log('SSL4Free Application Loaded');
});

// Start SSL generation process
function startSSLGeneration() {
    document.getElementById('progressSection').style.display = 'block';
    document.getElementById('formSection').style.display = 'block';
    document.getElementById('domainStep').style.display = 'block';
    
    // Scroll to form
    document.getElementById('formSection').scrollIntoView({ behavior: 'smooth' });
    
    updateProgressBar(1);
}

// Update progress bar
function updateProgressBar(step) {
    // Reset all steps
    for (let i = 1; i <= 4; i++) {
        const stepElement = document.getElementById(`step${i}`);
        if (i < step) {
            stepElement.className = stepElement.className.replace(/step-\w+/, 'step-completed');
        } else if (i === step) {
            stepElement.className = stepElement.className.replace(/step-\w+/, 'step-active');
        } else {
            stepElement.className = stepElement.className.replace(/step-\w+/, 'step-pending');
        }
    }
    currentStep = step;
}

// Validate domain input
async function validateDomain() {
    const domain = document.getElementById('domainInput').value.trim();
    const email = document.getElementById('emailInput').value.trim();
    
    // Basic validation
    if (!domain || !email) {
        showNotification('Please enter both domain and email / ڈومین اور ای میل دونوں درج کریں', 'error');
        return;
    }
    
    if (!isValidDomain(domain)) {
        showNotification('Please enter a valid domain name / صحیح ڈومین نام درج کریں', 'error');
        return;
    }
    
    if (!isValidEmail(email)) {
        showNotification('Please enter a valid email address / صحیح ای میل ایڈریس درج کریں', 'error');
        return;
    }
    
    // Check for manual CSR
    const manualCSR = document.getElementById('manualCSRInput').value.trim();
    const useManualCSR = document.getElementById('manualCSRToggle').checked;

    // Validate manual CSR if provided
    if (useManualCSR) {
        if (!manualCSR) {
            showNotification('Please enter a CSR or uncheck manual CSR option / براہ کرم CSR درج کریں', 'error');
            return;
        }

        if (!manualCSR.includes('-----BEGIN CERTIFICATE REQUEST-----') ||
            !manualCSR.includes('-----END CERTIFICATE REQUEST-----')) {
            showNotification('Please enter a valid CSR format / براہ کرم صحیح CSR فارمیٹ درج کریں', 'error');
            return;
        }
    }

    // Store domain data
    domainData = { domain, email, manualCSR: useManualCSR ? manualCSR : null };
    
    showLoading('Validating domain... / ڈومین کی تصدیق...');
    
    try {
        // Call backend API to validate domain
        const response = await fetch('api/validate-domain.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ domain, email })
        });
        
        const result = await response.json();
        
        hideLoading();
        
        if (result.success) {
            // Move to verification step
            document.getElementById('domainStep').style.display = 'none';
            document.getElementById('verificationStep').style.display = 'block';
            updateProgressBar(2);
            showNotification('Domain validated successfully! / ڈومین کامیابی سے تصدیق ہو گیا!', 'success');
        } else {
            showNotification(result.message, 'error');
        }
        
    } catch (error) {
        hideLoading();
        showNotification('Error validating domain / ڈومین تصدیق میں خرابی: ' + error.message, 'error');
    }
}

// Select verification method
function selectVerificationMethod(method) {
    verificationMethod = method;
    
    // Visual feedback
    const httpCard = document.querySelector('[onclick="selectVerificationMethod(\'http\')"]');
    const dnsCard = document.querySelector('[onclick="selectVerificationMethod(\'dns\')"]');
    
    httpCard.classList.remove('border-blue-500', 'bg-blue-50');
    dnsCard.classList.remove('border-green-500', 'bg-green-50');
    
    if (method === 'http') {
        httpCard.classList.add('border-blue-500', 'bg-blue-50');
    } else {
        dnsCard.classList.add('border-green-500', 'bg-green-50');
    }
    
    // Generate verification instructions
    setTimeout(() => {
        generateVerificationInstructions(method);
    }, 500);
}

// Generate verification instructions
async function generateVerificationInstructions(method) {
    showLoading('Creating certificate and generating verification instructions... / سرٹیفکیٹ بنایا جا رہا ہے...');

    try {
        // Step 1: Create certificate first
        const createResponse = await fetch('api/create-certificate.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: domainData.domain,
                email: domainData.email,
                manual_csr: domainData.manualCSR
            })
        });

        const createResult = await createResponse.json();

        console.log('Certificate creation result:', createResult); // Debug log

        if (!createResult.success) {
            hideLoading();
            showNotification('Certificate creation failed: ' + createResult.message, 'error');
            return;
        }

        // Store certificate ID and verification data
        domainData.certificate_id = createResult.data.certificate_id;
        domainData.verification = createResult.data.verification;

        console.log('Stored certificate ID:', domainData.certificate_id); // Debug log

        // Step 2: Initiate domain verification using ZeroSSL API
        const verificationMethod = method === 'http' ? 'HTTP_CSR_HASH' : 'CNAME_CSR_HASH';

        const verifyResponse = await fetch('api/verify-domain.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                certificate_id: domainData.certificate_id,
                verification_method: verificationMethod,
                validation_email: method === 'email' ? domainData.email : null
            })
        });

        const verifyResult = await verifyResponse.json();

        hideLoading();

        if (verifyResult.success) {
            // Display verification instructions
            displayVerificationInstructions(method, domainData.verification);

            // Move to instructions step
            document.getElementById('verificationStep').style.display = 'none';
            document.getElementById('instructionsStep').style.display = 'block';
            updateProgressBar(2);

            showNotification('Certificate created successfully! Complete verification to proceed.', 'success');
        } else {
            showNotification('Verification initiation failed: ' + verifyResult.message, 'error');
        }

    } catch (error) {
        hideLoading();
        showNotification('Error generating instructions / ہدایات بنانے میں خرابی: ' + error.message, 'error');
    }
}

// Display verification instructions
function displayVerificationInstructions(method, verification) {
    const instructionsDiv = document.getElementById('verificationInstructions');
    
    if (method === 'http') {
        instructionsDiv.innerHTML = `
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h4 class="text-lg font-semibold text-blue-800 mb-4">
                    <i class="fas fa-file-alt mr-2"></i>
                    HTTP File Upload Verification
                </h4>
                <p class="text-blue-700 mb-4 urdu-text">HTTP فائل اپ لوڈ تصدیق</p>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-blue-800 mb-2">1. Create this file on your server:</label>
                        <div class="bg-white border rounded p-3 font-mono text-sm">
                            <strong>File Path:</strong> http://${domainData.domain}/.well-known/acme-challenge/${verification.file_name}
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-blue-800 mb-2">2. File Content:</label>
                        <div class="bg-white border rounded p-3 font-mono text-sm break-all">
                            ${verification.file_content}
                        </div>
                        <button onclick="copyToClipboard('${verification.file_content}')" 
                                class="mt-2 bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">
                            <i class="fas fa-copy mr-1"></i> Copy Content
                        </button>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded p-4">
                        <p class="text-sm text-yellow-800">
                            <i class="fas fa-info-circle mr-2"></i>
                            Make sure the file is accessible via HTTP (not HTTPS) before clicking verify.
                        </p>
                        <p class="text-sm text-yellow-700 urdu-text mt-1">
                            تصدیق کلک کرنے سے پہلے یقینی بنائیں کہ فائل HTTP سے قابل رسائی ہے۔
                        </p>
                    </div>
                </div>
            </div>
        `;
    } else {
        instructionsDiv.innerHTML = `
            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <h4 class="text-lg font-semibold text-green-800 mb-4">
                    <i class="fas fa-globe mr-2"></i>
                    DNS TXT Record Verification
                </h4>
                <p class="text-green-700 mb-4 urdu-text">DNS TXT ریکارڈ تصدیق</p>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-green-800 mb-2">Add this DNS TXT record:</label>
                        <div class="bg-white border rounded p-4 space-y-2">
                            <div><strong>Record Type:</strong> TXT</div>
                            <div><strong>Name:</strong> ${verification.record_name}</div>
                            <div><strong>Value:</strong> <span class="font-mono break-all">${verification.record_value}</span></div>
                            <div><strong>TTL:</strong> 300 (or default)</div>
                        </div>
                        <button onclick="copyToClipboard('${verification.record_value}')" 
                                class="mt-2 bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700">
                            <i class="fas fa-copy mr-1"></i> Copy Value
                        </button>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded p-4">
                        <p class="text-sm text-yellow-800">
                            <i class="fas fa-clock mr-2"></i>
                            DNS changes may take up to 24 hours to propagate. Wait at least 5 minutes before verifying.
                        </p>
                        <p class="text-sm text-yellow-700 urdu-text mt-1">
                            DNS تبدیلیاں 24 گھنٹے تک لگ سکتی ہیں۔ تصدیق سے پہلے کم از کم 5 منٹ انتظار کریں۔
                        </p>
                    </div>
                </div>
            </div>
        `;
    }
}

// Verify domain and generate certificate
async function verifyDomain() {
    showLoading('Verifying domain and generating certificate... / ڈومین کی تصدیق اور سرٹیفکیٹ بنایا جا رہا ہے...');
    
    try {
        const response = await fetch('api/verify-and-generate.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: domainData.domain,
                email: domainData.email,
                verification_method: verificationMethod,
                verification: domainData.verification
            })
        });
        
        const result = await response.json();
        
        hideLoading();
        
        if (result.success) {
            certificateData = result.certificates;
            
            // Move to download step
            document.getElementById('instructionsStep').style.display = 'none';
            document.getElementById('downloadStep').style.display = 'block';
            updateProgressBar(4);
            
            showNotification('SSL Certificate generated successfully! / SSL سرٹیفکیٹ کامیابی سے بن گیا!', 'success');
        } else {
            showNotification(result.message, 'error');
        }
        
    } catch (error) {
        hideLoading();
        showNotification('Error generating certificate / سرٹیفکیٹ بنانے میں خرابی: ' + error.message, 'error');
    }
}

// Download certificate files
function downloadFile(type) {
    let content = '';
    let filename = '';
    
    switch (type) {
        case 'certificate':
            content = certificateData.certificate;
            filename = `${domainData.domain}.crt`;
            break;
        case 'privatekey':
            content = certificateData.private_key;
            filename = `${domainData.domain}.key`;
            break;
        case 'cabundle':
            content = certificateData.ca_bundle;
            filename = `${domainData.domain}_ca_bundle.crt`;
            break;
    }
    
    if (content) {
        downloadTextFile(content, filename);
        showNotification(`${filename} downloaded successfully! / ${filename} کامیابی سے ڈاؤن لوڈ ہو گیا!`, 'success');
    }
}

// Email certificates
async function emailCertificates() {
    showLoading('Sending certificates via email... / ای میل کے ذریعے سرٹیفکیٹس بھیجے جا رہے ہیں...');
    
    try {
        const response = await fetch('api/email-certificates.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                domain: domainData.domain,
                email: domainData.email,
                certificates: certificateData
            })
        });
        
        const result = await response.json();
        
        hideLoading();
        
        if (result.success) {
            showNotification('Certificates sent to your email! / سرٹیفکیٹس آپ کے ای میل پر بھیج دیے گئے!', 'success');
        } else {
            showNotification(result.message, 'error');
        }
        
    } catch (error) {
        hideLoading();
        showNotification('Error sending email / ای میل بھیجنے میں خرابی: ' + error.message, 'error');
    }
}

// Start over
function startOver() {
    // Reset all data
    currentStep = 1;
    domainData = {};
    verificationMethod = '';
    certificateData = {};
    
    // Reset form
    document.getElementById('domainInput').value = '';
    document.getElementById('emailInput').value = '';
    
    // Hide all steps
    document.getElementById('verificationStep').style.display = 'none';
    document.getElementById('instructionsStep').style.display = 'none';
    document.getElementById('downloadStep').style.display = 'none';
    
    // Show domain step
    document.getElementById('domainStep').style.display = 'block';
    updateProgressBar(1);
    
    // Scroll to top
    document.getElementById('formSection').scrollIntoView({ behavior: 'smooth' });
}

// Go back to previous step
function goBack() {
    if (currentStep === 2) {
        document.getElementById('verificationStep').style.display = 'none';
        document.getElementById('domainStep').style.display = 'block';
        updateProgressBar(1);
    } else if (currentStep === 3) {
        document.getElementById('instructionsStep').style.display = 'none';
        document.getElementById('verificationStep').style.display = 'block';
        updateProgressBar(2);
    }
}

// Utility Functions
function isValidDomain(domain) {
    // More flexible domain validation that allows subdomains
    const pattern = /^[a-zA-Z0-9][a-zA-Z0-9.-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;

    // Remove protocol if present
    domain = domain.replace(/^https?:\/\//, '');
    domain = domain.replace(/\/$/, '');

    // Basic format check
    if (!pattern.test(domain)) {
        return false;
    }

    // Check for valid TLD
    const parts = domain.split('.');
    if (parts.length < 2) {
        return false;
    }

    // Last part should be valid TLD (at least 2 characters)
    const tld = parts[parts.length - 1];
    if (tld.length < 2) {
        return false;
    }

    return true;
}

function isValidEmail(email) {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return pattern.test(email);
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard! / کلپ بورڈ میں کاپی ہو گیا!', 'success');
    });
}

function downloadTextFile(content, filename) {
    const element = document.createElement('a');
    element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(content));
    element.setAttribute('download', filename);
    element.style.display = 'none';
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
}

function showLoading(message) {
    const overlay = document.getElementById('loadingOverlay');
    overlay.querySelector('p').textContent = message.split(' / ')[0];
    overlay.querySelector('.urdu-text').textContent = message.split(' / ')[1] || '';
    overlay.style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 max-w-md ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    
    const [english, urdu] = message.split(' / ');
    
    notification.innerHTML = `
        <div class="flex items-start">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-3 mt-1"></i>
            <div>
                <div>${english}</div>
                ${urdu ? `<div class="text-sm mt-1 urdu-text">${urdu}</div>` : ''}
            </div>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Toggle manual CSR section
function toggleManualCSR() {
    const toggle = document.getElementById('manualCSRToggle');
    const section = document.getElementById('manualCSRSection');

    if (toggle.checked) {
        section.style.display = 'block';
    } else {
        section.style.display = 'none';
    }
}
