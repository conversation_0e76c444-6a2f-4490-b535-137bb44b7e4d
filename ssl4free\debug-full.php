<!DOCTYPE html>
<html>
<head>
    <title>SSL4Free - Complete Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .section { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .step { background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>SSL4Free - Complete Debug Analysis</h1>
    
    <?php
    require_once 'api/config.php';
    
    $testDomain = 'test.jobzhit.com';
    $testEmail = '<EMAIL>';
    
    echo "<div class='section step'>";
    echo "<h2>🔧 Configuration Check</h2>";
    echo "<p><strong>API Key:</strong> " . ZEROSSL_API_KEY . "</p>";
    echo "<p><strong>API URL:</strong> " . ZEROSSL_API_URL . "</p>";
    echo "<p><strong>Test Domain:</strong> $testDomain</p>";
    echo "<p><strong>Test Email:</strong> $testEmail</p>";
    echo "</div>";
    
    // Step 1: Test API Connection
    echo "<div class='section step'>";
    echo "<h2>📡 Step 1: ZeroSSL API Connection Test</h2>";
    
    $url = ZEROSSL_API_URL . '/certificates?access_key=' . ZEROSSL_API_KEY . '&limit=1';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p class='error'>❌ API Connection Failed: $error</p>";
    } else {
        echo "<p class='success'>✅ API Connection Success (HTTP $httpCode)</p>";
        
        $apiData = json_decode($response, true);
        if (isset($apiData['error'])) {
            echo "<p class='error'>❌ API Error: " . json_encode($apiData['error']) . "</p>";
        } else {
            echo "<p class='success'>✅ API Working - Total Certificates: " . ($apiData['total_count'] ?? 0) . "</p>";
        }
    }
    echo "</div>";
    
    // Step 2: Test Domain Validation
    echo "<div class='section step'>";
    echo "<h2>🌐 Step 2: Domain Validation Test</h2>";
    
    // Test validateDomain function
    if (function_exists('validateDomain')) {
        if (validateDomain($testDomain)) {
            echo "<p class='success'>✅ validateDomain() passed</p>";
        } else {
            echo "<p class='error'>❌ validateDomain() failed</p>";
        }
    } else {
        echo "<p class='error'>❌ validateDomain() function not found</p>";
    }
    
    // Test validateEmail function
    if (function_exists('validateEmail')) {
        if (validateEmail($testEmail)) {
            echo "<p class='success'>✅ validateEmail() passed</p>";
        } else {
            echo "<p class='error'>❌ validateEmail() failed</p>";
        }
    } else {
        echo "<p class='error'>❌ validateEmail() function not found</p>";
    }
    echo "</div>";
    
    // Step 3: Test validate-domain.php API
    echo "<div class='section step'>";
    echo "<h2>🔍 Step 3: Domain Validation API Test</h2>";
    
    $postData = json_encode([
        'domain' => $testDomain,
        'email' => $testEmail
    ]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/ssl4free/api/validate-domain.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    
    if ($error) {
        echo "<p class='error'>❌ Domain Validation API Error: $error</p>";
    } else {
        echo "<p class='success'>✅ Domain Validation API Call Success</p>";
        echo "<h4>Response:</h4>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        $validationResult = json_decode($response, true);
        if (isset($validationResult['success']) && $validationResult['success']) {
            echo "<p class='success'>🎉 Domain Validation: SUCCESS</p>";
        } else {
            echo "<p class='error'>❌ Domain Validation: FAILED</p>";
            if (isset($validationResult['message'])) {
                echo "<p class='error'>Error Message: " . htmlspecialchars($validationResult['message']) . "</p>";
            }
        }
    }
    echo "</div>";
    
    // Step 4: Test Certificate Creation
    echo "<div class='section step'>";
    echo "<h2>🔐 Step 4: Certificate Creation Test</h2>";
    
    $postData = json_encode([
        'domain' => $testDomain,
        'email' => $testEmail
    ]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/ssl4free/api/create-certificate.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
    
    if ($error) {
        echo "<p class='error'>❌ Certificate Creation API Error: $error</p>";
    } else {
        echo "<p class='success'>✅ Certificate Creation API Call Success</p>";
        echo "<h4>Response:</h4>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        $certResult = json_decode($response, true);
        if (isset($certResult['success']) && $certResult['success']) {
            echo "<p class='success'>🎉 Certificate Creation: SUCCESS</p>";
            if (isset($certResult['data']['certificate_id'])) {
                echo "<p class='success'>Certificate ID: " . $certResult['data']['certificate_id'] . "</p>";
            }
        } else {
            echo "<p class='error'>❌ Certificate Creation: FAILED</p>";
            if (isset($certResult['message'])) {
                echo "<p class='error'>Error Message: " . htmlspecialchars($certResult['message']) . "</p>";
            }
        }
    }
    echo "</div>";
    
    // Step 5: Summary and Solutions
    echo "<div class='section step'>";
    echo "<h2>📋 Step 5: Summary & Solutions</h2>";
    
    echo "<h3>Common Issues & Solutions:</h3>";
    echo "<ul>";
    echo "<li><strong>API Connection Failed:</strong> Check internet connection and ZeroSSL API status</li>";
    echo "<li><strong>Certificate Limit Reached:</strong> Use different domain or new ZeroSSL account</li>";
    echo "<li><strong>Duplicate Domain:</strong> Use subdomain like test.yourdomain.com</li>";
    echo "<li><strong>Domain Validation Failed:</strong> Check domain format and accessibility</li>";
    echo "<li><strong>CSR Generation Failed:</strong> Use manual CSR input option</li>";
    echo "</ul>";
    
    echo "<h3>Recommended Next Steps:</h3>";
    echo "<ol>";
    echo "<li>If API connection works, try different domain names</li>";
    echo "<li>Use subdomains: test.jobzhit.com, staging.jobzhit.com</li>";
    echo "<li>Enable manual CSR option if automatic fails</li>";
    echo "<li>Check browser console for JavaScript errors</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<p><a href='index.html'>← Back to SSL4Free</a> | <a href='simple-test.php'>Simple Test</a></p>";
    ?>
</body>
</html>
