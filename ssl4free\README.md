# SSL4Free - Free SSL Certificate Generator

A complete SSL certificate generator website using ZeroSSL API with bilingual support (English/Urdu).

## 🚀 Features

- **Free SSL Certificates** using ZeroSSL API
- **Bilingual Interface** (English + Urdu)
- **Multiple Verification Methods** (HTTP File Upload + DNS TXT Record)
- **Email Delivery** of certificates
- **Responsive Design** with TailwindCSS
- **Step-by-step Process** with progress tracking
- **Professional UI/UX** similar to SSLForFree.com

## 📋 Requirements

- PHP 7.4 or higher
- cURL extension enabled
- OpenSSL extension enabled
- Web server (Apache/Nginx)
- ZeroSSL API key (free from https://app.zerossl.com/developer)

## 🛠️ Installation

### 1. Download and Setup

1. Copy all files to your web server directory
2. Ensure proper permissions (755 for directories, 644 for files)
3. Create necessary directories:
   ```bash
   mkdir temp logs
   chmod 755 temp logs
   ```

### 2. Configure ZeroSSL API

1. Sign up at https://app.zerossl.com/developer
2. Get your free API key
3. Edit `api/config.php`:
   ```php
   define('ZEROSSL_API_KEY', 'your_actual_api_key_here');
   ```

### 3. Email Configuration (Optional)

Edit `api/config.php` for email settings:
```php
define('SMTP_HOST', 'your_smtp_host');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_password');
```

### 4. Test Installation

1. Visit your website
2. Try generating a test certificate
3. Check logs in `logs/ssl4free.log`

## 📁 File Structure

```
ssl4free/
├── index.html              # Main homepage
├── privacy.html            # Privacy policy
├── terms.html              # Terms of service
├── contact.html            # Contact page
├── js/
│   └── app.js              # Main JavaScript application
├── api/
│   ├── config.php          # Configuration file
│   ├── validate-domain.php # Domain validation
│   ├── create-certificate.php # Certificate creation
│   ├── verify-and-generate.php # Verification & generation
│   └── email-certificates.php # Email delivery
├── temp/                   # Temporary files (auto-created)
├── logs/                   # Log files (auto-created)
└── README.md              # This file
```

## 🔧 API Endpoints

- `POST /api/validate-domain.php` - Validate domain and email
- `POST /api/create-certificate.php` - Create certificate request
- `POST /api/verify-and-generate.php` - Verify domain and generate certificate
- `POST /api/email-certificates.php` - Email certificates to user

## 🌐 Usage Flow

1. **Domain Input** - User enters domain and email
2. **Verification Method** - Choose HTTP file upload or DNS TXT record
3. **Domain Verification** - Complete verification process
4. **Certificate Generation** - Generate SSL certificate via ZeroSSL
5. **Download/Email** - Download files or receive via email

## 🔒 Security Features

- Input validation and sanitization
- Rate limiting protection
- Temporary file cleanup
- Secure API communication
- No sensitive data storage

## 🌍 Bilingual Support

- English and Urdu labels throughout
- RTL text support for Urdu
- Cultural considerations in design
- Accessible to diverse users

## 📧 Email Templates

Professional HTML email templates with:
- Certificate details
- Installation instructions
- Security warnings
- Branded design

## 🐛 Troubleshooting

### Common Issues:

1. **API Key Error**
   - Verify ZeroSSL API key is correct
   - Check API key permissions

2. **Domain Verification Fails**
   - Ensure domain is publicly accessible
   - Check DNS propagation for DNS method
   - Verify file upload for HTTP method

3. **Certificate Generation Fails**
   - Check ZeroSSL API status
   - Verify domain ownership
   - Review error logs

### Log Files:

Check `logs/ssl4free.log` for detailed error information.

## 📞 Support

- **Email**: <EMAIL>
- **Issues**: Create GitHub issue
- **Documentation**: See contact.html page

## 👨‍💻 Developer

Created with ❤️ by **Waji**

## 📄 License

This project is open source. Please check individual components for their licenses.

## 🙏 Acknowledgments

- ZeroSSL for providing free SSL certificates
- TailwindCSS for the beautiful UI framework
- Font Awesome for icons
- Google Fonts for Urdu typography

---

**From Waji with ❤️**
