<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<!-- Header -->
<header class="header">
    <div class="container">
        <div class="nav-wrapper">
            <div class="logo">
                <?php if (has_custom_logo()) : ?>
                    <?php the_custom_logo(); ?>
                <?php else : ?>
                    <h1>
                        <a href="<?php echo esc_url(home_url('/')); ?>" style="text-decoration: none; color: inherit;">
                            <i class="fas fa-store"></i> 
                            <?php bloginfo('name'); ?>
                        </a>
                    </h1>
                <?php endif; ?>
            </div>
            
            <!-- Search Bar -->
            <div class="search-container">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="What are you looking for?" value="<?php echo esc_attr(get_search_query()); ?>">
                    <select id="locationSelect">
                        <option value="">All Pakistan</option>
                        <option value="karachi">Karachi</option>
                        <option value="lahore">Lahore</option>
                        <option value="islamabad">Islamabad</option>
                        <option value="rawalpindi">Rawalpindi</option>
                        <option value="faisalabad">Faisalabad</option>
                        <option value="multan">Multan</option>
                        <option value="peshawar">Peshawar</option>
                        <option value="quetta">Quetta</option>
                    </select>
                    <button class="search-btn" type="button"><i class="fas fa-search"></i></button>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="nav-menu">
                <?php if (is_user_logged_in()) : ?>
                    <a href="<?php echo esc_url(get_author_posts_url(get_current_user_id())); ?>" class="nav-link">
                        <i class="fas fa-user"></i> My Account
                    </a>
                    <a href="#" class="nav-link" id="favoritesLink">
                        <i class="fas fa-heart"></i> Favorites
                    </a>
                    <a href="<?php echo esc_url(wp_logout_url(home_url())); ?>" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                <?php else : ?>
                    <a href="#" class="nav-link" id="loginLink">
                        <i class="fas fa-user"></i> Login
                    </a>
                    <a href="#" class="nav-link" id="favoritesLink">
                        <i class="fas fa-heart"></i> Favorites
                    </a>
                <?php endif; ?>
                
                <?php if (is_user_logged_in()) : ?>
                    <a href="<?php echo esc_url(admin_url('post-new.php?post_type=product')); ?>" class="sell-btn">
                        <i class="fas fa-plus"></i> SELL
                    </a>
                <?php else : ?>
                    <a href="#" class="sell-btn" id="sellBtn">
                        <i class="fas fa-plus"></i> SELL
                    </a>
                <?php endif; ?>
            </nav>
        </div>
    </div>
</header>

<!-- Login Modal -->
<?php if (!is_user_logged_in()) : ?>
<div id="loginModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>Login to Your Account</h2>
        <form id="loginForm" method="post" action="<?php echo esc_url(wp_login_url()); ?>">
            <div class="form-group">
                <input type="text" name="log" placeholder="Username or Email" required>
            </div>
            <div class="form-group">
                <input type="password" name="pwd" placeholder="Password" required>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="rememberme" value="forever"> Remember Me
                </label>
            </div>
            <button type="submit" class="btn-primary">Login</button>
            <input type="hidden" name="redirect_to" value="<?php echo esc_url(home_url()); ?>">
            <p>Don't have an account? <a href="#" id="showRegister">Register here</a></p>
            <p><a href="<?php echo esc_url(wp_lostpassword_url()); ?>">Forgot Password?</a></p>
        </form>
    </div>
</div>

<!-- Register Modal -->
<div id="registerModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>Create Account</h2>
        <form id="registerForm" method="post" action="<?php echo esc_url(wp_registration_url()); ?>">
            <div class="form-group">
                <input type="text" name="user_login" placeholder="Username" required>
            </div>
            <div class="form-group">
                <input type="email" name="user_email" placeholder="Email" required>
            </div>
            <div class="form-group">
                <input type="password" name="user_pass" placeholder="Password" required>
            </div>
            <div class="form-group">
                <input type="password" name="user_pass_confirm" placeholder="Confirm Password" required>
            </div>
            <button type="submit" class="btn-primary">Register</button>
            <p>Already have an account? <a href="#" id="showLogin">Login here</a></p>
        </form>
    </div>
</div>
<?php endif; ?>

<!-- Breadcrumb Navigation -->
<?php if (!is_front_page()) : ?>
<div class="breadcrumb-container" style="background: #f8f9fa; padding: 15px 0; border-bottom: 1px solid #eee;">
    <div class="container">
        <nav class="breadcrumb" style="font-size: 14px; color: #666;">
            <a href="<?php echo esc_url(home_url('/')); ?>" style="color: #002f34; text-decoration: none;">Home</a>
            
            <?php if (is_single() && get_post_type() == 'product') : ?>
                <span style="margin: 0 8px;">/</span>
                <a href="<?php echo esc_url(get_post_type_archive_link('product')); ?>" style="color: #002f34; text-decoration: none;">Products</a>
                <span style="margin: 0 8px;">/</span>
                <span><?php echo esc_html(wp_trim_words(get_the_title(), 5)); ?></span>
                
            <?php elseif (is_archive()) : ?>
                <span style="margin: 0 8px;">/</span>
                <span><?php echo esc_html(get_the_archive_title()); ?></span>
                
            <?php elseif (is_search()) : ?>
                <span style="margin: 0 8px;">/</span>
                <span>Search Results for "<?php echo esc_html(get_search_query()); ?>"</span>
                
            <?php elseif (is_single()) : ?>
                <span style="margin: 0 8px;">/</span>
                <a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>" style="color: #002f34; text-decoration: none;">Blog</a>
                <span style="margin: 0 8px;">/</span>
                <span><?php echo esc_html(wp_trim_words(get_the_title(), 5)); ?></span>
                
            <?php elseif (is_page()) : ?>
                <span style="margin: 0 8px;">/</span>
                <span><?php echo esc_html(get_the_title()); ?></span>
            <?php endif; ?>
        </nav>
    </div>
</div>
<?php endif; ?>

<main id="main" class="site-main"><?php // Main content starts here ?>
