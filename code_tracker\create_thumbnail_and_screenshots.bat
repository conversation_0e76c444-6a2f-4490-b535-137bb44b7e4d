@echo off
echo 🖼️ Creating Thumbnail and Screenshots
echo ====================================

set "BASE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_Fixed"
set "SOURCE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete"

echo 📸 Copying and renaming screenshots...

:: Copy screenshots from source to Theme_Preview (we'll rename them manually)
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\01_General_Settings_Tab.png" "%BASE_DIR%\Theme_Preview\01_general_settings.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\02_Agents_Management_Tab.png" "%BASE_DIR%\Theme_Preview\02_agents_management.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\03_Appearance_Themes_Tab.png" "%BASE_DIR%\Theme_Preview\03_appearance_themes.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\04_Working_Hours_Tab.png" "%BASE_DIR%\Theme_Preview\04_working_hours.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\05_Advanced_Settings_Tab.png" "%BASE_DIR%\Theme_Preview\05_advanced_settings.png" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\06_Preview_Tab.png" "%BASE_DIR%\Theme_Preview\06_preview_tab.png" >nul

echo ✅ Screenshots copied!

echo 🖼️ Creating simple thumbnail...
:: Copy one of the screenshots as thumbnail (we'll resize it manually)
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\01_General_Settings_Tab.png" "%BASE_DIR%\Thumbnail\thumbnail_original.png" >nul

echo ✅ Files prepared!
echo.
echo 📋 NEXT STEPS (Manual):
echo.
echo 1. THUMBNAIL (80x80px, under 50KB):
echo    📁 Go to: %BASE_DIR%\Thumbnail\
echo    🖼️ Use online tool: https://www.iloveimg.com/resize-image
echo    📏 Resize thumbnail_original.png to 80x80 pixels
echo    💾 Save as: thumbnail.png
echo    🗑️ Delete thumbnail_original.png
echo.
echo 2. SCREENSHOTS (Convert PNG to JPG):
echo    📁 Go to: %BASE_DIR%\Theme_Preview\
echo    🔄 Use online tool: https://convertio.co/png-jpg/
echo    📸 Convert all PNG files to JPG format
echo    📝 Keep the same names but change extension to .jpg
echo    🗑️ Delete original PNG files
echo.
echo 3. FORM SETTINGS:
echo    ✅ Compatible Browsers: Select ALL (IE11, Firefox, Safari, Opera, Chrome, Edge)
echo    ✅ ThemeForest Files: Select CSS Files, JS Files, PHP Files
echo.
echo 📁 Fixed folder location: %BASE_DIR%
echo.
echo 🎯 After manual steps, your submission will be ready!
pause
