/**
 * WhatsApp Widget Pro - Enhanced JavaScript
 * Version: 2.0.0
 */

jQuery(document).ready(function($) {

    // Initialize WhatsApp Widget
    const WhatsAppWidget = {

        init: function() {
            this.bindEvents();
            this.loadWidget();
            this.initBubbleWidget();
        },

        loadWidget: function() {
            // Add entrance animation after page load
            setTimeout(function() {
                $('#whatsapp-widget, #whatsapp-bubble-widget, #whatsapp-widget-offline').addClass('widget-loaded');
            }, 1000);
        },

        bindEvents: function() {
            // Simple widget click tracking
            $(document).on('click', '#whatsapp-widget a, .whatsapp-link', this.trackClick);

            // Bubble widget events
            $(document).on('click', '.bubble-trigger', this.toggleBubble);
            $(document).on('click', '.bubble-close', this.closeBubble);
            $(document).on('click', '.agent-chat-btn', this.trackAgentClick);

            // Close bubble when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.whatsapp-bubble-widget').length) {
                    $('.whatsapp-bubble-widget').removeClass('active');
                }
            });

            // Hover effects
            $(document).on('mouseenter', '#whatsapp-widget, .bubble-trigger', this.pauseAnimation);
            $(document).on('mouseleave', '#whatsapp-widget, .bubble-trigger', this.resumeAnimation);
        },

        toggleBubble: function(e) {
            e.preventDefault();
            const $widget = $(this).closest('.whatsapp-bubble-widget');
            $widget.toggleClass('active');

            // Track bubble open
            WhatsAppWidget.trackEvent('Bubble Toggle', 'open');
        },

        closeBubble: function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).closest('.whatsapp-bubble-widget').removeClass('active');

            // Track bubble close
            WhatsAppWidget.trackEvent('Bubble Toggle', 'close');
        },

        trackClick: function(e) {
            const agentPhone = $(this).closest('[data-agent-phone]').data('agent-phone') ||
                             $(this).data('agent-phone') ||
                             'unknown';

            console.log('WhatsApp widget clicked - Agent:', agentPhone);

            // Send analytics data to WordPress
            WhatsAppWidget.sendAnalytics(agentPhone);

            // Track with external services
            WhatsAppWidget.trackEvent('WhatsApp Click', 'simple_widget', agentPhone);
        },

        trackAgentClick: function(e) {
            const agentPhone = $(this).closest('.agent-item').data('agent-phone');
            const agentName = $(this).closest('.agent-item').find('h5').text();

            console.log('Agent chat clicked:', agentName, agentPhone);

            // Send analytics data to WordPress
            WhatsAppWidget.sendAnalytics(agentPhone, agentName);

            // Track with external services
            WhatsAppWidget.trackEvent('WhatsApp Click', 'bubble_agent', agentPhone);
        },

        trackEvent: function(category, action, label) {
            // Google Analytics 4
            if (typeof gtag !== 'undefined') {
                gtag('event', action, {
                    'event_category': category,
                    'event_label': label || '',
                    'custom_parameter_1': 'whatsapp_widget_pro'
                });
            }

            // Google Analytics Universal
            if (typeof ga !== 'undefined') {
                ga('send', 'event', category, action, label);
            }

            // Facebook Pixel
            if (typeof fbq !== 'undefined') {
                fbq('track', 'Contact', {
                    content_name: 'WhatsApp Widget Pro',
                    content_category: category,
                    value: 1
                });
            }

            // Custom tracking hook for developers
            if (typeof window.whatsappWidgetCustomTrack === 'function') {
                window.whatsappWidgetCustomTrack(category, action, label);
            }
        },

        sendAnalytics: function(agentPhone, agentName) {
            // Send click data to WordPress for analytics dashboard
            if (typeof whatsapp_widget_ajax !== 'undefined') {
                $.ajax({
                    url: whatsapp_widget_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'whatsapp_widget_track_click',
                        agent_phone: agentPhone,
                        agent_name: agentName || '',
                        page_url: window.location.href,
                        user_agent: navigator.userAgent,
                        referrer: document.referrer,
                        timestamp: new Date().toISOString(),
                        nonce: whatsapp_widget_ajax.nonce
                    },
                    success: function(response) {
                        console.log('Analytics data sent successfully');
                    },
                    error: function() {
                        console.log('Failed to send analytics data');
                    }
                });
            }
        },

        pauseAnimation: function() {
            $(this).find('i').css('animation-play-state', 'paused');
        },

        resumeAnimation: function() {
            $(this).find('i').css('animation-play-state', 'running');
        },

        initBubbleWidget: function() {
            // Add typing indicator animation for bubble
            $('.bubble-content').each(function() {
                const $this = $(this);

                // Add subtle entrance delay for agents
                $this.find('.agent-item').each(function(index) {
                    $(this).css({
                        'animation-delay': (index * 0.1) + 's',
                        'animation': 'slideInUp 0.3s ease forwards'
                    });
                });
            });
        }
    };

    // Initialize the widget
    WhatsAppWidget.init();

    // Make WhatsAppWidget globally accessible for custom integrations
    window.WhatsAppWidget = WhatsAppWidget;

});

// CSS Animation for agent items
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .agent-item {
        opacity: 0;
    }
`;
document.head.appendChild(style);