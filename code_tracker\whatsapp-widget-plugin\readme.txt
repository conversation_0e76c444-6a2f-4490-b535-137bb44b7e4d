=== WhatsApp Chat Widget Pro ===
Contributors: yourname
Tags: whatsapp, chat, widget, contact, support, customer service, live chat, messaging
Requires at least: 5.0
Tested up to: 6.4
Stable tag: 2.0.0
Requires PHP: 7.4
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Professional WhatsApp chat widget with multiple agents, working hours, custom themes, and advanced analytics.

== Description ==

WhatsApp Chat Widget Pro is the most advanced WhatsApp integration plugin for WordPress. Transform your website's customer support with a professional floating WhatsApp button that includes multiple agents, working hours, custom themes, and comprehensive analytics.

Perfect for businesses, e-commerce stores, service providers, and any website that wants to provide instant customer support through WhatsApp.

= 🚀 Key Features =

**Multiple Agents Support**
* Add unlimited WhatsApp agents
* Agent profiles with names, titles, and avatars
* Round-robin or manual agent selection
* Individual agent enable/disable controls

**Working Hours Management**
* Set business hours for each day of the week
* Timezone support for global businesses
* Automatic online/offline status indicators
* Custom offline messages

**Advanced Themes & Customization**
* 6 built-in professional themes
* Custom CSS editor for unlimited customization
* Multiple widget sizes and positions
* Chat bubble interface option
* Responsive design for all devices

**Analytics Dashboard**
* Real-time click tracking
* Agent performance metrics
* Daily, weekly, monthly reports
* Visual charts and graphs
* Export analytics data

**Developer-Friendly Features**
* Shortcode support: `[whatsapp_widget]`
* JavaScript API for custom integrations
* WordPress hooks and filters
* Custom CSS classes
* AJAX-powered interface

= 🎯 Perfect For =

* E-commerce stores
* Service businesses
* Real estate agencies
* Healthcare providers
* Educational institutions
* SaaS companies
* Any business wanting instant customer support

= 🌟 Premium Features =

* Multiple agent management
* Working hours with timezone support
* Advanced analytics dashboard
* Custom themes and styling
* Chat bubble interface
* Shortcode integration
* Mobile-specific settings
* Custom CSS editor
* Click tracking and reporting
* Agent performance metrics

== Installation ==

= Automatic Installation =
1. Log in to your WordPress admin panel
2. Go to Plugins > Add New
3. Search for "WhatsApp Chat Widget Pro"
4. Click "Install Now" and then "Activate"
5. Go to Settings > WhatsApp Widget to configure

= Manual Installation =
1. Download the plugin zip file
2. Upload the plugin files to `/wp-content/plugins/whatsapp-widget-pro/`
3. Activate the plugin through the 'Plugins' screen in WordPress
4. Go to Settings > WhatsApp Widget to configure

= Quick Setup =
1. Enter your WhatsApp number with country code (e.g., +923001234567)
2. Add your agents in the Agents tab
3. Customize appearance in the Appearance tab
4. Set working hours if needed
5. Save settings and test the widget!

== Frequently Asked Questions ==

= How do I add multiple agents? =
Go to Settings > WhatsApp Widget > Agents tab. Click "Add New Agent" to add unlimited agents with their own phone numbers, names, and titles.

= Can I set working hours? =
Yes! Go to the Working Hours tab to set business hours for each day of the week. The widget will automatically show online/offline status.

= How do I customize the appearance? =
Use the Appearance tab to choose from 6 built-in themes, enable chat bubble mode, or add custom CSS for unlimited customization.

= Does it work on mobile devices? =
Absolutely! The widget is fully responsive and automatically adjusts for mobile devices. You can also set different visibility for mobile vs desktop.

= Can I track analytics? =
Yes! The plugin includes a comprehensive analytics dashboard showing click tracking, agent performance, and detailed reports.

= How do I use shortcodes? =
Use `[whatsapp_widget]` to display the widget anywhere. You can also use parameters like `[whatsapp_widget phone="+923001234567" size="70"]`.

= Is it compatible with caching plugins? =
Yes, the plugin is compatible with all major caching plugins including WP Rocket, W3 Total Cache, and WP Super Cache.

= Can I customize the CSS? =
Yes! Use the Custom CSS editor in the Appearance tab to add your own styles. The plugin also provides CSS classes for advanced customization.

= Does it work with WhatsApp Business? =
Yes, it works perfectly with both personal WhatsApp and WhatsApp Business numbers.

= How do I export my settings? =
Go to the Preview tab and click "Export Settings" to download your configuration as a JSON file for backup or migration.

= Can I disable the widget on specific pages? =
Yes, you can use the working hours feature or add custom code to conditionally display the widget based on your needs.

= Is the plugin translation ready? =
Yes, the plugin is fully translation ready and includes text domain for easy localization.

= How do I get support? =
For support, please visit our documentation or contact us through our support channels. Premium support is available for licensed users.

== Screenshots ==

1. Professional admin dashboard with tabbed interface
2. Multiple agents management with profiles
3. Working hours configuration with timezone support
4. Advanced appearance customization options
5. Real-time analytics dashboard with charts
6. Chat bubble interface with agent selection
7. Mobile responsive widget design
8. Live preview with theme selection
9. Custom CSS editor for advanced styling
10. Analytics reports and agent performance

== Changelog ==

= 2.0.0 =
* Complete rewrite with professional features
* Added multiple agents support
* Implemented working hours with timezone support
* Created advanced analytics dashboard
* Added 6 professional themes
* Introduced chat bubble interface
* Built custom CSS editor
* Added shortcode support
* Implemented click tracking and reporting
* Created tabbed admin interface
* Added mobile-specific settings
* Improved responsive design
* Enhanced security and performance
* Added developer hooks and filters

= 1.0 =
* Initial release
* Basic widget functionality
* Admin settings panel
* Responsive design
* Animation effects

== Upgrade Notice ==

= 2.0.0 =
Major update with professional features! Multiple agents, working hours, analytics dashboard, custom themes, and much more. Backup your settings before upgrading.

= 1.0 =
First version of WhatsApp Chat Widget plugin.

== Technical Requirements ==

* WordPress 5.0 or higher
* PHP 7.4 or higher
* MySQL 5.6 or higher
* Modern web browser with JavaScript enabled

== Support ==

For technical support, documentation, and feature requests:

* Documentation: Available in plugin admin panel
* Support Forum: WordPress.org support forum
* Premium Support: Available for licensed users
* GitHub: Open source contributions welcome

== Privacy Policy ==

This plugin collects minimal analytics data when enabled:
* Click timestamps and IP addresses
* Page URLs where clicks occurred
* User agent strings for device identification
* No personal information is stored or transmitted

All data is stored locally in your WordPress database and is not shared with third parties.

== Credits ==

* Font Awesome icons
* Chart.js for analytics visualization
* WordPress coding standards
* Extensive testing across devices and browsers

== License ==

This plugin is licensed under the GPL v2 or later.

== Roadmap ==

Upcoming features in future versions:
* Multi-language support
* Advanced scheduling options
* Integration with popular CRM systems
* WhatsApp Web API integration
* Advanced reporting features
* Team collaboration tools