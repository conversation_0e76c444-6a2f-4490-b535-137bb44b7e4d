/**
 * Comprehensive Error Handling and Retry System
 * For SIM Database Scraping Operations
 * Educational purpose only
 */

class ErrorHandler {
    constructor() {
        this.errorTypes = {
            NETWORK_ERROR: 'NETWORK_ERROR',
            CORS_ERROR: 'CORS_ERROR',
            TIMEOUT_ERROR: 'TIMEOUT_ERROR',
            PARSE_ERROR: 'PARSE_ERROR',
            VALIDATION_ERROR: 'VALIDATION_ERROR',
            RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
            SERVER_ERROR: 'SERVER_ERROR',
            UNKNOWN_ERROR: 'UNKNOWN_ERROR'
        };

        this.retryConfig = {
            maxRetries: 3,
            baseDelay: 1000, // 1 second
            maxDelay: 10000, // 10 seconds
            backoffMultiplier: 2
        };

        this.errorLog = [];
        this.maxLogSize = 100;
    }

    // Main error handling function
    handleError(error, context = {}) {
        const errorInfo = this.analyzeError(error, context);
        this.logError(errorInfo);
        
        console.error(`🚨 Error in ${context.operation || 'Unknown Operation'}:`, errorInfo);
        
        return {
            type: errorInfo.type,
            message: errorInfo.userMessage,
            technical: errorInfo.technicalMessage,
            retryable: errorInfo.retryable,
            suggestions: errorInfo.suggestions
        };
    }

    // Analyze error and categorize it
    analyzeError(error, context) {
        let errorType = this.errorTypes.UNKNOWN_ERROR;
        let userMessage = 'An unexpected error occurred';
        let technicalMessage = error.message || 'Unknown error';
        let retryable = false;
        let suggestions = [];

        // Network-related errors
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorType = this.errorTypes.NETWORK_ERROR;
            userMessage = 'Network connection failed';
            retryable = true;
            suggestions = [
                'Check your internet connection',
                'Try again in a few moments',
                'Contact support if the problem persists'
            ];
        }
        
        // CORS errors
        else if (error.message.includes('CORS') || error.message.includes('cross-origin')) {
            errorType = this.errorTypes.CORS_ERROR;
            userMessage = 'Cross-origin request blocked';
            retryable = true;
            suggestions = [
                'The website is blocking cross-origin requests',
                'Trying alternative proxy methods',
                'Some data may be simulated for demonstration'
            ];
        }
        
        // Timeout errors
        else if (error.name === 'AbortError' || error.message.includes('timeout')) {
            errorType = this.errorTypes.TIMEOUT_ERROR;
            userMessage = 'Request timed out';
            retryable = true;
            suggestions = [
                'The server is taking too long to respond',
                'Try again with a shorter timeout',
                'Check if the service is available'
            ];
        }
        
        // Parsing errors
        else if (error.name === 'SyntaxError' || error.message.includes('parse')) {
            errorType = this.errorTypes.PARSE_ERROR;
            userMessage = 'Failed to process server response';
            retryable = false;
            suggestions = [
                'The server returned invalid data',
                'This might be a temporary server issue',
                'Using fallback data for demonstration'
            ];
        }
        
        // Validation errors
        else if (error.message.includes('validation') || error.message.includes('invalid')) {
            errorType = this.errorTypes.VALIDATION_ERROR;
            userMessage = 'Invalid input provided';
            retryable = false;
            suggestions = [
                'Please check your input format',
                'Ensure mobile number is 11 digits starting with 03',
                'Ensure CNIC is 13 digits'
            ];
        }
        
        // Rate limiting errors
        else if (error.message.includes('rate limit') || error.message.includes('429')) {
            errorType = this.errorTypes.RATE_LIMIT_ERROR;
            userMessage = 'Too many requests';
            retryable = true;
            suggestions = [
                'Please wait a moment before trying again',
                'The service has rate limiting in place',
                'Try again in a few minutes'
            ];
        }
        
        // Server errors
        else if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
            errorType = this.errorTypes.SERVER_ERROR;
            userMessage = 'Server temporarily unavailable';
            retryable = true;
            suggestions = [
                'The server is experiencing issues',
                'Try again in a few minutes',
                'Using backup data sources'
            ];
        }

        return {
            type: errorType,
            userMessage,
            technicalMessage,
            retryable,
            suggestions,
            timestamp: new Date().toISOString(),
            context
        };
    }

    // Log error for debugging
    logError(errorInfo) {
        this.errorLog.push(errorInfo);
        
        // Keep log size manageable
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog = this.errorLog.slice(-this.maxLogSize);
        }
    }

    // Retry mechanism with exponential backoff
    async retryOperation(operation, context = {}) {
        let lastError = null;
        
        for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
            try {
                console.log(`🔄 Attempt ${attempt + 1}/${this.retryConfig.maxRetries + 1} for ${context.operation || 'operation'}`);
                
                const result = await operation();
                
                if (attempt > 0) {
                    console.log(`✅ Operation succeeded on attempt ${attempt + 1}`);
                }
                
                return result;
            } catch (error) {
                lastError = error;
                const errorInfo = this.analyzeError(error, context);
                
                console.log(`❌ Attempt ${attempt + 1} failed: ${errorInfo.userMessage}`);
                
                // Don't retry if error is not retryable
                if (!errorInfo.retryable) {
                    console.log('🚫 Error is not retryable, stopping attempts');
                    break;
                }
                
                // Don't wait after the last attempt
                if (attempt < this.retryConfig.maxRetries) {
                    const delay = this.calculateDelay(attempt);
                    console.log(`⏳ Waiting ${delay}ms before next attempt`);
                    await this.delay(delay);
                }
            }
        }
        
        // All attempts failed
        throw lastError;
    }

    // Calculate delay for exponential backoff
    calculateDelay(attempt) {
        const delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffMultiplier, attempt);
        return Math.min(delay, this.retryConfig.maxDelay);
    }

    // Delay utility
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get error statistics
    getErrorStats() {
        const stats = {};
        
        this.errorLog.forEach(error => {
            stats[error.type] = (stats[error.type] || 0) + 1;
        });
        
        return {
            totalErrors: this.errorLog.length,
            errorsByType: stats,
            recentErrors: this.errorLog.slice(-10)
        };
    }

    // Clear error log
    clearErrorLog() {
        this.errorLog = [];
    }

    // Create user-friendly error message
    createUserMessage(error, context = {}) {
        const errorInfo = this.analyzeError(error, context);
        
        let message = `<div class="error-details">
            <h4><i class="fas fa-exclamation-triangle"></i> ${errorInfo.userMessage}</h4>
            <p>We encountered an issue while processing your request.</p>
        `;
        
        if (errorInfo.suggestions.length > 0) {
            message += `<div class="error-suggestions">
                <h5>Suggestions:</h5>
                <ul>`;
            
            errorInfo.suggestions.forEach(suggestion => {
                message += `<li>${suggestion}</li>`;
            });
            
            message += `</ul></div>`;
        }
        
        if (errorInfo.retryable) {
            message += `<div class="error-actions">
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-redo"></i> Try Again
                </button>
            </div>`;
        }
        
        message += `</div>`;
        
        return message;
    }

    // Fallback data generator for when all else fails
    generateFallbackResponse(searchInput, searchType) {
        console.log('🎭 Generating fallback response for educational purposes');
        
        if (searchType === 'mobile') {
            return {
                success: true,
                data: {
                    mobile: searchInput,
                    owner: 'Sample User Name',
                    cnic: '42000-1234567-1',
                    address: 'Sample Address, Karachi',
                    network: this.detectNetwork(searchInput),
                    status: 'Active',
                    type: 'Prepaid',
                    registrationDate: '2023-01-15',
                    source: 'Educational Fallback Data',
                    confidence: 'Sample Data Only',
                    disclaimer: 'This is sample data for educational purposes only'
                }
            };
        } else if (searchType === 'cnic') {
            return {
                success: true,
                data: [
                    {
                        mobile: '03001234567',
                        network: 'Jazz',
                        status: 'Active',
                        type: 'Prepaid',
                        registrationDate: '2023-01-15'
                    }
                ],
                source: 'Educational Fallback Data'
            };
        }
        
        return {
            success: false,
            error: 'Unable to generate fallback data'
        };
    }

    detectNetwork(mobile) {
        if (!mobile) return 'Unknown';
        
        const prefix = mobile.substring(0, 4);
        
        if (['0300', '0301', '0302', '0303'].includes(prefix)) return 'Jazz';
        if (['0321', '0322', '0323', '0324'].includes(prefix)) return 'Telenor';
        if (['0310', '0311', '0312', '0313'].includes(prefix)) return 'Zong';
        if (['0333', '0334', '0335', '0336'].includes(prefix)) return 'Ufone';
        
        return 'Unknown';
    }
}

// Circuit breaker pattern for preventing cascading failures
class CircuitBreaker {
    constructor(threshold = 5, timeout = 60000) {
        this.threshold = threshold;
        this.timeout = timeout;
        this.failureCount = 0;
        this.lastFailureTime = null;
        this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    }

    async execute(operation) {
        if (this.state === 'OPEN') {
            if (Date.now() - this.lastFailureTime > this.timeout) {
                this.state = 'HALF_OPEN';
            } else {
                throw new Error('Circuit breaker is OPEN');
            }
        }

        try {
            const result = await operation();
            this.onSuccess();
            return result;
        } catch (error) {
            this.onFailure();
            throw error;
        }
    }

    onSuccess() {
        this.failureCount = 0;
        this.state = 'CLOSED';
    }

    onFailure() {
        this.failureCount++;
        this.lastFailureTime = Date.now();
        
        if (this.failureCount >= this.threshold) {
            this.state = 'OPEN';
        }
    }
}

// Export error handling utilities
window.ErrorHandler = new ErrorHandler();
window.CircuitBreaker = CircuitBreaker;

console.log('🛡️ Error handling system loaded - Educational use only');
