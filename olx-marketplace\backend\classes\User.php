<?php
/**
 * User Class - Handle all user operations
 * OLX Marketplace Backend
 */

require_once '../config/database.php';

class User {
    private $conn;
    private $table_name = "users";

    public $id;
    public $username;
    public $email;
    public $password;
    public $full_name;
    public $phone;
    public $avatar;
    public $location;
    public $is_verified;
    public $is_active;
    public $is_admin;
    public $verification_token;
    public $reset_token;
    public $reset_token_expires;
    public $created_at;
    public $updated_at;
    public $last_login;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Register new user
    public function register() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET username=:username, email=:email, password=:password, 
                      full_name=:full_name, phone=:phone, location=:location,
                      verification_token=:verification_token";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->full_name = htmlspecialchars(strip_tags($this->full_name));
        $this->phone = htmlspecialchars(strip_tags($this->phone));
        $this->location = htmlspecialchars(strip_tags($this->location));

        // Hash password
        $password_hash = password_hash($this->password, PASSWORD_BCRYPT);
        
        // Generate verification token
        $verification_token = bin2hex(random_bytes(32));

        // Bind values
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":password", $password_hash);
        $stmt->bindParam(":full_name", $this->full_name);
        $stmt->bindParam(":phone", $this->phone);
        $stmt->bindParam(":location", $this->location);
        $stmt->bindParam(":verification_token", $verification_token);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            $this->verification_token = $verification_token;
            return true;
        }

        return false;
    }

    // Login user
    public function login() {
        $query = "SELECT id, username, email, password, full_name, phone, avatar, 
                         location, is_verified, is_active, is_admin 
                  FROM " . $this->table_name . " 
                  WHERE email = :email AND is_active = 1 LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":email", $this->email);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row && password_verify($this->password, $row['password'])) {
            $this->id = $row['id'];
            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->full_name = $row['full_name'];
            $this->phone = $row['phone'];
            $this->avatar = $row['avatar'];
            $this->location = $row['location'];
            $this->is_verified = $row['is_verified'];
            $this->is_active = $row['is_active'];
            $this->is_admin = $row['is_admin'];

            // Update last login
            $this->updateLastLogin();

            return true;
        }

        return false;
    }

    // Check if email exists
    public function emailExists() {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":email", $this->email);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    // Check if username exists
    public function usernameExists() {
        $query = "SELECT id FROM " . $this->table_name . " WHERE username = :username LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":username", $this->username);
        $stmt->execute();

        return $stmt->rowCount() > 0;
    }

    // Verify email
    public function verifyEmail($token) {
        $query = "UPDATE " . $this->table_name . " 
                  SET is_verified = 1, verification_token = NULL 
                  WHERE verification_token = :token";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":token", $token);

        return $stmt->execute() && $stmt->rowCount() > 0;
    }

    // Generate password reset token
    public function generateResetToken() {
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

        $query = "UPDATE " . $this->table_name . " 
                  SET reset_token = :token, reset_token_expires = :expires 
                  WHERE email = :email";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":token", $token);
        $stmt->bindParam(":expires", $expires);
        $stmt->bindParam(":email", $this->email);

        if($stmt->execute() && $stmt->rowCount() > 0) {
            return $token;
        }

        return false;
    }

    // Reset password
    public function resetPassword($token, $new_password) {
        $query = "SELECT id FROM " . $this->table_name . " 
                  WHERE reset_token = :token AND reset_token_expires > NOW() LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":token", $token);
        $stmt->execute();

        if($stmt->rowCount() > 0) {
            $password_hash = password_hash($new_password, PASSWORD_BCRYPT);
            
            $update_query = "UPDATE " . $this->table_name . " 
                            SET password = :password, reset_token = NULL, reset_token_expires = NULL 
                            WHERE reset_token = :token";

            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(":password", $password_hash);
            $update_stmt->bindParam(":token", $token);

            return $update_stmt->execute();
        }

        return false;
    }

    // Update profile
    public function updateProfile() {
        $query = "UPDATE " . $this->table_name . " 
                  SET full_name = :full_name, phone = :phone, location = :location";

        if(!empty($this->avatar)) {
            $query .= ", avatar = :avatar";
        }

        $query .= " WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        $this->full_name = htmlspecialchars(strip_tags($this->full_name));
        $this->phone = htmlspecialchars(strip_tags($this->phone));
        $this->location = htmlspecialchars(strip_tags($this->location));

        $stmt->bindParam(":full_name", $this->full_name);
        $stmt->bindParam(":phone", $this->phone);
        $stmt->bindParam(":location", $this->location);
        $stmt->bindParam(":id", $this->id);

        if(!empty($this->avatar)) {
            $stmt->bindParam(":avatar", $this->avatar);
        }

        return $stmt->execute();
    }

    // Change password
    public function changePassword($current_password, $new_password) {
        // First verify current password
        $query = "SELECT password FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row && password_verify($current_password, $row['password'])) {
            $password_hash = password_hash($new_password, PASSWORD_BCRYPT);
            
            $update_query = "UPDATE " . $this->table_name . " SET password = :password WHERE id = :id";
            $update_stmt = $this->conn->prepare($update_query);
            $update_stmt->bindParam(":password", $password_hash);
            $update_stmt->bindParam(":id", $this->id);

            return $update_stmt->execute();
        }

        return false;
    }

    // Get user by ID
    public function getUserById($user_id) {
        $query = "SELECT id, username, email, full_name, phone, avatar, location, 
                         is_verified, is_active, is_admin, created_at 
                  FROM " . $this->table_name . " 
                  WHERE id = :id LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $user_id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Get user products count
    public function getProductsCount($user_id) {
        $query = "SELECT COUNT(*) as count FROM products WHERE user_id = :user_id AND is_active = 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'];
    }

    // Update last login
    private function updateLastLogin() {
        $query = "UPDATE " . $this->table_name . " SET last_login = NOW() WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();
    }

    // Validate user input
    public static function validateRegistration($data) {
        $errors = [];

        if(empty($data['username']) || strlen($data['username']) < 3) {
            $errors[] = "Username must be at least 3 characters long";
        }

        if(empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Valid email is required";
        }

        if(empty($data['password']) || strlen($data['password']) < 8) {
            $errors[] = "Password must be at least 8 characters long";
        }

        if(empty($data['full_name'])) {
            $errors[] = "Full name is required";
        }

        if(!empty($data['phone']) && !preg_match('/^(\+92|0)?[0-9]{10}$/', $data['phone'])) {
            $errors[] = "Invalid phone number format";
        }

        return $errors;
    }

    // Get all users (for admin)
    public function getAllUsers($page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;
        
        $query = "SELECT id, username, email, full_name, phone, location, 
                         is_verified, is_active, is_admin, created_at, last_login 
                  FROM " . $this->table_name . " 
                  ORDER BY created_at DESC 
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindParam(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get total users count
    public function getTotalUsersCount() {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'];
    }

    // Toggle user status (for admin)
    public function toggleUserStatus($user_id) {
        $query = "UPDATE " . $this->table_name . " 
                  SET is_active = NOT is_active 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $user_id);

        return $stmt->execute();
    }
}
?>
