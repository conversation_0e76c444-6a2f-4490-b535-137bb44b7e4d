/* WhatsApp Widget Pro - Enhanced Styles */

.whatsapp-widget {
    position: fixed;
    z-index: 9999;
    opacity: 0;
    transform: scale(0);
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    cursor: pointer;
}

.whatsapp-widget.widget-loaded {
    opacity: 1;
    transform: scale(1);
}

/* Enhanced positioning */

.whatsapp-widget.bottom-right {
    bottom: 20px;
    right: 20px;
}

.whatsapp-widget.bottom-left {
    bottom: 20px;
    left: 20px;
}

.whatsapp-widget.top-right {
    top: 20px;
    right: 20px;
}

.whatsapp-widget.top-left {
    top: 20px;
    left: 20px;
}

/* Enhanced button styles */

.whatsapp-widget a,
.whatsapp-widget .bubble-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #25D366;
    border-radius: 50%;
    color: white;
    font-size: 30px;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.whatsapp-widget a:hover,
.whatsapp-widget .bubble-trigger:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.6);
    background: #128C7E;
}

.whatsapp-widget a:active,
.whatsapp-widget .bubble-trigger:active {
    transform: scale(0.95);
}

.whatsapp-widget i {
    animation: pulse 2s infinite;
    z-index: 1;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Online/Offline Indicators */
.online-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    background: #4CAF50;
    border: 2px solid white;
    border-radius: 50%;
    animation: blink 1.5s infinite;
}

.offline-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    background: #f44336;
    border: 2px solid white;
    border-radius: 50%;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Offline Widget Styles */
.whatsapp-widget-offline {
    cursor: default;
}

.whatsapp-widget-offline .offline-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.whatsapp-widget-offline:hover .offline-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(-10px);
}

.offline-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10000;
}

.offline-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
}

/* Bubble Widget Styles */
.whatsapp-bubble-widget {
    position: fixed;
    z-index: 9999;
}

.whatsapp-bubble-widget .bubble-trigger {
    cursor: pointer;
    border: none;
    outline: none;
}

.bubble-content {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 300px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.whatsapp-bubble-widget.active .bubble-content {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.bubble-header {
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
    padding: 16px;
    position: relative;
}

.bubble-header h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
}

.bubble-header p {
    margin: 0;
    font-size: 13px;
    opacity: 0.9;
}

.bubble-close {
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.bubble-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.bubble-agents {
    max-height: 250px;
    overflow-y: auto;
}

.agent-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.2s ease;
}

.agent-item:hover {
    background: #f8f9fa;
}

.agent-item:last-child {
    border-bottom: none;
}

.agent-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    overflow: hidden;
}

.agent-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.agent-avatar i {
    color: #6c757d;
    font-size: 18px;
}

.agent-info {
    flex: 1;
}

.agent-info h5 {
    margin: 0 0 2px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.agent-info p {
    margin: 0;
    font-size: 12px;
    color: #6c757d;
}

.agent-chat-btn {
    background: #25D366;
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.agent-chat-btn:hover {
    background: #128C7E;
    transform: translateY(-1px);
}

/* Position adjustments for bubble */
.whatsapp-bubble-widget.bottom-left .bubble-content {
    right: auto;
    left: 0;
}

.whatsapp-bubble-widget.top-right .bubble-content,
.whatsapp-bubble-widget.top-left .bubble-content {
    bottom: auto;
    top: 80px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .whatsapp-widget,
    .whatsapp-bubble-widget .bubble-trigger {
        width: 50px !important;
        height: 50px !important;
    }

    .whatsapp-widget a,
    .whatsapp-bubble-widget .bubble-trigger {
        font-size: 24px;
    }

    .whatsapp-widget.bottom-right,
    .whatsapp-widget.bottom-left,
    .whatsapp-bubble-widget.bottom-right,
    .whatsapp-bubble-widget.bottom-left {
        bottom: 15px;
    }

    .whatsapp-widget.bottom-right,
    .whatsapp-bubble-widget.bottom-right {
        right: 15px;
    }

    .whatsapp-widget.bottom-left,
    .whatsapp-bubble-widget.bottom-left {
        left: 15px;
    }

    .bubble-content {
        width: 280px;
        max-width: calc(100vw - 40px);
    }

    .online-indicator,
    .offline-indicator {
        width: 10px;
        height: 10px;
        top: 6px;
        right: 6px;
    }
}

/* Theme Variations */

/* Default Theme (already defined above) */

/* Minimal Theme */
.whatsapp-theme-minimal a,
.whatsapp-theme-minimal .bubble-trigger {
    background: #fff;
    color: #25D366;
    border: 2px solid #25D366;
    box-shadow: 0 2px 8px rgba(37, 211, 102, 0.2);
}

.whatsapp-theme-minimal a:hover,
.whatsapp-theme-minimal .bubble-trigger:hover {
    background: #25D366;
    color: white;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
}

/* Dark Theme */
.whatsapp-theme-dark a,
.whatsapp-theme-dark .bubble-trigger {
    background: #2c2c2c;
    color: #25D366;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.whatsapp-theme-dark a:hover,
.whatsapp-theme-dark .bubble-trigger:hover {
    background: #25D366;
    color: white;
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
}

/* Gradient Theme */
.whatsapp-theme-gradient a,
.whatsapp-theme-gradient .bubble-trigger {
    background: linear-gradient(135deg, #25D366, #128C7E);
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
}

.whatsapp-theme-gradient a:hover,
.whatsapp-theme-gradient .bubble-trigger:hover {
    background: linear-gradient(135deg, #128C7E, #25D366);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

/* Rounded Square Theme */
.whatsapp-theme-square a,
.whatsapp-theme-square .bubble-trigger {
    border-radius: 15px;
}

/* Pulsing Theme */
.whatsapp-theme-pulse a,
.whatsapp-theme-pulse .bubble-trigger {
    animation: continuousPulse 2s infinite;
}

@keyframes continuousPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    }
}

/* Custom CSS placeholder */
.whatsapp-widget-custom-css {
    /* Custom styles will be injected here */
}