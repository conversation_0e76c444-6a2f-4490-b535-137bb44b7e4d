/* New Modern Style for SIM Database Website */

:root {
    --primary-color: #2878eb;
    --secondary-color: #ff723a;
    --dark-color: #333;
    --light-color: #f8f9fa;
    --gradient-primary: linear-gradient(135deg, #2878eb 0%, #1e5bb6 100%);
    --gradient-secondary: linear-gradient(135deg, #ff723a 0%, #ff5722 100%);
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f5f7fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo img {
    height: 40px;
    margin-right: 10px;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
}

.nav-item {
    margin-left: 25px;
    position: relative;
}

.nav-link {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    padding: 5px 0;
}

.nav-link:hover {
    color: var(--primary-color);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: #fff;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    padding: 10px 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: var(--transition);
    z-index: 100;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: 8px 20px;
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-menu a:hover {
    background-color: #f5f7fa;
    color: var(--primary-color);
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
    border: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: #fff;
}

.btn-primary:hover {
    box-shadow: 0 5px 15px rgba(40, 120, 235, 0.3);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: #fff;
}

.btn-secondary:hover {
    box-shadow: 0 5px 15px rgba(255, 114, 58, 0.3);
    transform: translateY(-2px);
}

/* Hero Section */
.hero {
    background: var(--gradient-primary);
    color: #fff;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/pattern.svg');
    background-size: cover;
    opacity: 0.1;
}

.hero-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-text {
    flex: 1;
    max-width: 600px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.network-logos {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.network-logo {
    max-width: 120px;
    filter: drop-shadow(0 5px 10px rgba(0, 0, 0, 0.1));
    transition: var(--transition);
}

.network-logo:hover {
    transform: scale(1.05);
}

/* Search Form - Removed conflicting styles */

.search-form {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 20px;
    align-items: end;
}

.form-group {
    margin-bottom: 0;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group select,
.form-group input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(40, 120, 235, 0.15), 0 4px 20px rgba(40, 120, 235, 0.1);
    transform: translateY(-2px);
}

.form-group select:hover,
.form-group input:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Enhanced Select Styling */
.form-group select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 45px;
    appearance: none;
    cursor: pointer;
}

/* Input Icons */
.form-group.has-icon {
    position: relative;
}

.form-group.has-icon::before {
    content: '\f3cd';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 1.1rem;
    z-index: 1;
}

.form-group.has-icon input {
    padding-left: 45px;
}

.btn-large {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 25px rgba(255, 114, 58, 0.3);
    transition: all 0.3s ease;
}

.btn-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 114, 58, 0.4);
}

.btn-large::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-large:hover::before {
    left: 100%;
}

/* Search Button Animation */
.btn-large i {
    margin-right: 8px;
    transition: transform 0.3s ease;
}

.btn-large:hover i {
    transform: scale(1.1);
}

/* Hero Stats */
.hero-stats {
    display: flex;
    margin-top: 30px;
    gap: 30px;
}

.stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 25px;
    border-radius: var(--border-radius);
    backdrop-filter: blur(5px);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Search Results */
.search-results {
    padding: 50px 0;
    background-color: #f5f7fa;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.results-header h2 {
    color: var(--dark-color);
    font-size: 1.8rem;
}

.results-content {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
}

/* Loading Animation */
.loading {
    text-align: center;
    padding: 50px 0;
}

.loading i {
    font-size: 3rem;
    color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    margin-top: 20px;
    font-size: 1.2rem;
    color: var(--dark-color);
}

.loading small {
    display: block;
    margin-top: 10px;
    color: #777;
}

/* Result Cards */
.result-card {
    background: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    margin-bottom: 20px;
    border-left: 5px solid var(--primary-color);
}

.result-card h3 {
    color: var(--dark-color);
    margin-bottom: 20px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
}

.result-card h3 i {
    color: var(--primary-color);
    margin-right: 10px;
}

.result-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #eee;
}

.result-label {
    font-weight: 600;
    color: #555;
}

.result-value {
    font-weight: 500;
    color: var(--dark-color);
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-content {
        flex-direction: column;
    }
    
    .hero-text {
        max-width: 100%;
        margin-bottom: 40px;
    }
    
    .search-form {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-stats {
        flex-wrap: wrap;
    }
    
    .stat {
        flex: 1 0 calc(50% - 15px);
    }
}

@media (max-width: 576px) {
    .hero {
        padding: 60px 0;
    }

    .hero-title {
        font-size: 2rem;
    }



    .result-item {
        flex-direction: column;
    }

    .result-label {
        margin-bottom: 5px;
    }
}

/* Enhanced Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes rocket {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-3px) rotate(-5deg); }
    75% { transform: translateY(-1px) rotate(5deg); }
}

@keyframes slide {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

@keyframes twinkle {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(40, 120, 235, 0.5); }
    50% { box-shadow: 0 0 20px rgba(40, 120, 235, 0.8), 0 0 30px rgba(40, 120, 235, 0.6); }
}

/* Enhanced Form Styling */
.form-group select:focus,
.form-group input:focus {
    animation: glow 2s ease-in-out infinite;
}

.example-btn {
    position: relative;
    overflow: hidden;
}

.example-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.example-btn:hover::before {
    left: 100%;
}

/* Loading Animation Enhancement */
.loading i {
    animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
}

/* Button Hover Effects */
.btn-large:active {
    transform: translateY(-1px) scale(0.98);
}



/* 🔥 EPIC ANIMATIONS FOR SUPER ATTRACTIVE SEARCH BOX */

/* Floating Particles */
@keyframes float1 {
    0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0.7; }
    25% { transform: translateY(-20px) translateX(10px) rotate(90deg); opacity: 1; }
    50% { transform: translateY(-10px) translateX(-15px) rotate(180deg); opacity: 0.8; }
    75% { transform: translateY(-30px) translateX(5px) rotate(270deg); opacity: 0.9; }
}

@keyframes float2 {
    0%, 100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.6; }
    33% { transform: translateY(-25px) translateX(-10px) scale(1.2); opacity: 1; }
    66% { transform: translateY(-5px) translateX(20px) scale(0.8); opacity: 0.7; }
}

@keyframes float3 {
    0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg) scale(1); }
    50% { transform: translateY(-35px) translateX(-20px) rotate(180deg) scale(1.5); }
}

/* Glowing Border */
@keyframes borderGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Neon Pulse Effect */
@keyframes neonPulse {
    0%, 100% {
        text-shadow: 0 0 10px rgba(255,255,255,0.8), 0 0 20px rgba(255,255,255,0.6), 0 0 30px rgba(255,255,255,0.4);
        transform: scale(1);
    }
    50% {
        text-shadow: 0 0 20px rgba(255,255,255,1), 0 0 30px rgba(255,255,255,0.8), 0 0 40px rgba(255,255,255,0.6);
        transform: scale(1.02);
    }
}

/* Rocket Spin */
@keyframes rocketSpin {
    0% { transform: rotate(0deg) translateY(0px); }
    25% { transform: rotate(90deg) translateY(-5px); }
    50% { transform: rotate(180deg) translateY(0px); }
    75% { transform: rotate(270deg) translateY(-3px); }
    100% { transform: rotate(360deg) translateY(0px); }
}

/* Star Twinkle */
@keyframes starTwinkle {
    0%, 100% { opacity: 1; transform: scale(1) rotate(0deg); }
    25% { opacity: 0.7; transform: scale(1.3) rotate(45deg); }
    50% { opacity: 0.4; transform: scale(0.8) rotate(90deg); }
    75% { opacity: 0.8; transform: scale(1.1) rotate(135deg); }
}

/* Step Pulse */
@keyframes stepPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 4px 15px rgba(255,107,107,0.4); }
    50% { transform: scale(1.1); box-shadow: 0 6px 25px rgba(255,107,107,0.6); }
}

/* Wiggle Effect */
@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

/* Badge Animation */
@keyframes badge {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

/* Phone Ring */
@keyframes phoneRing {
    0%, 100% { transform: translateY(-50%) rotate(0deg); }
    25% { transform: translateY(-50%) rotate(-10deg); }
    75% { transform: translateY(-50%) rotate(10deg); }
}

/* Rainbow Shift */
@keyframes rainbowShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Rocket Launch */
@keyframes rocketLaunch {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-8px) rotate(-15deg); }
    50% { transform: translateY(-4px) rotate(0deg); }
    75% { transform: translateY(-12px) rotate(15deg); }
}

/* Free Badge */
@keyframes freeBadge {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.9; }
}

/* Sparkle Effects */
@keyframes sparkle1 {
    0%, 100% { opacity: 0; transform: scale(0); }
    50% { opacity: 1; transform: scale(1); }
}

@keyframes sparkle2 {
    0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
    50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
}

@keyframes sparkle3 {
    0%, 100% { opacity: 0; transform: scale(0); }
    33% { opacity: 0.7; transform: scale(0.8); }
    66% { opacity: 1; transform: scale(1.1); }
}

/* Wave Slide */
@keyframes waveSlide {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

/* Enhanced Input Focus */
.form-group input:focus,
.form-group select:focus {
    outline: none !important;
    transform: translateY(-2px) !important;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.3), 0 0 0 3px rgba(255,255,255,0.3), 0 12px 25px rgba(240,147,251,0.4) !important;
}

/* Network Cards Styling */
.network-logos {
    animation: fadeInUp 1s ease-out 0.5s both;
}

.network-card {
    position: relative;
    overflow: hidden;
}

.network-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.network-card:hover::before {
    left: 100%;
}

/* Responsive Network Cards */
@media (max-width: 768px) {
    .network-logos {
        grid-template-columns: 1fr 1fr !important;
        gap: 15px !important;
    }

    .network-card {
        padding: 15px !important;
    }

    .network-card div {
        font-size: 2rem !important;
    }

    .network-card h4 {
        font-size: 1rem !important;
    }

    .network-card p {
        font-size: 0.7rem !important;
    }
}

@media (max-width: 480px) {
    .network-logos {
        grid-template-columns: 1fr !important;
        max-width: 250px !important;
    }
}

/* Fade In Up Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Glass Morphism Float Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* Button Shine Effect */
.btn:hover .shine {
    left: 100%;
}


