<?php
/**
 * Database Setup Script
 * OLX Marketplace
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'olx_marketplace';

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>OLX Marketplace Database Setup</h2>";
    echo "<p>Starting database setup...</p>";
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✓ Database '$database' created successfully</p>";
    
    // Connect to the created database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute schema
    $schema = file_get_contents('backend/database/schema.sql');
    
    // Split the schema into individual statements
    $statements = explode(';', $schema);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
            } catch (PDOException $e) {
                // Skip if table already exists
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "<p>Error executing statement: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    echo "<p>✓ Database schema created successfully</p>";
    
    // Create uploads directory
    $uploadDirs = [
        'uploads',
        'uploads/products',
        'uploads/avatars'
    ];
    
    foreach ($uploadDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
            echo "<p>✓ Directory '$dir' created</p>";
        } else {
            echo "<p>✓ Directory '$dir' already exists</p>";
        }
    }
    
    // Set permissions
    chmod('uploads', 0777);
    chmod('uploads/products', 0777);
    chmod('uploads/avatars', 0777);
    
    echo "<p>✓ Upload directories configured</p>";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE is_admin = 1");
    $stmt->execute();
    $adminCount = $stmt->fetchColumn();
    
    if ($adminCount == 0) {
        // Create default admin user
        $adminPassword = password_hash('admin123', PASSWORD_BCRYPT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, full_name, is_admin, is_verified, is_active) 
            VALUES ('admin', '<EMAIL>', ?, 'Administrator', 1, 1, 1)
        ");
        $stmt->execute([$adminPassword]);
        echo "<p>✓ Default admin user created (<EMAIL> / admin123)</p>";
    } else {
        echo "<p>✓ Admin user already exists</p>";
    }
    
    // Insert sample data if tables are empty
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM products");
    $stmt->execute();
    $productCount = $stmt->fetchColumn();
    
    if ($productCount == 0) {
        // Insert sample products
        $sampleProducts = [
            [
                'user_id' => 1,
                'category_id' => 1,
                'title' => 'iPhone 14 Pro Max 256GB',
                'slug' => 'iphone-14-pro-max-256gb',
                'description' => 'Brand new iPhone 14 Pro Max with 256GB storage. All accessories included. Perfect condition.',
                'price' => 350000,
                'condition_type' => 'new',
                'location' => 'Karachi',
                'contact_phone' => '03001234567',
                'contact_email' => '<EMAIL>'
            ],
            [
                'user_id' => 1,
                'category_id' => 2,
                'title' => 'Honda Civic 2020 Model',
                'slug' => 'honda-civic-2020-model',
                'description' => 'Well maintained Honda Civic 2020 model. Single owner, all documents clear. Excellent condition.',
                'price' => 4500000,
                'condition_type' => 'used',
                'location' => 'Lahore',
                'contact_phone' => '03009876543',
                'contact_email' => '<EMAIL>'
            ],
            [
                'user_id' => 1,
                'category_id' => 3,
                'title' => 'Yamaha YBR 125 2022',
                'slug' => 'yamaha-ybr-125-2022',
                'description' => 'Excellent condition Yamaha YBR 125. Low mileage, well maintained. First owner.',
                'price' => 285000,
                'condition_type' => 'used',
                'location' => 'Islamabad',
                'contact_phone' => '03001111222',
                'contact_email' => '<EMAIL>'
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO products (user_id, category_id, title, slug, description, price, condition_type, location, contact_phone, contact_email, expires_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 30 DAY))
        ");
        
        foreach ($sampleProducts as $product) {
            $stmt->execute([
                $product['user_id'],
                $product['category_id'],
                $product['title'],
                $product['slug'],
                $product['description'],
                $product['price'],
                $product['condition_type'],
                $product['location'],
                $product['contact_phone'],
                $product['contact_email']
            ]);
        }
        
        echo "<p>✓ Sample products inserted</p>";
    } else {
        echo "<p>✓ Products already exist</p>";
    }
    
    echo "<h3>Setup Complete!</h3>";
    echo "<p><strong>Your OLX Marketplace is ready to use!</strong></p>";
    echo "<ul>";
    echo "<li><a href='index.html'>Visit Homepage</a></li>";
    echo "<li><a href='admin/index.html'>Admin Panel</a> (<EMAIL> / admin123)</li>";
    echo "</ul>";
    
    echo "<h4>Next Steps:</h4>";
    echo "<ul>";
    echo "<li>Change the default admin password</li>";
    echo "<li>Configure email settings in backend/config/database.php</li>";
    echo "<li>Update site settings through admin panel</li>";
    echo "<li>Add your own categories and products</li>";
    echo "</ul>";
    
    echo "<h4>Features Available:</h4>";
    echo "<ul>";
    echo "<li>✓ User Registration & Login</li>";
    echo "<li>✓ Product Management (Add, Edit, Delete)</li>";
    echo "<li>✓ Advanced Search & Filters</li>";
    echo "<li>✓ Category Management</li>";
    echo "<li>✓ Image Upload</li>";
    echo "<li>✓ Admin Dashboard</li>";
    echo "<li>✓ Responsive Design</li>";
    echo "<li>✓ SEO Friendly URLs</li>";
    echo "</ul>";
    
    echo "<p><em>For support, check the README.md file or documentation.</em></p>";
    
} catch (PDOException $e) {
    echo "<h3>Setup Failed!</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OLX Marketplace Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        h2, h3, h4 {
            color: #002f34;
        }
        
        p {
            line-height: 1.6;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        li {
            margin-bottom: 8px;
        }
        
        a {
            color: #002f34;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            color: #23e5db;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <!-- Content is generated by PHP above -->
</body>
</html>
