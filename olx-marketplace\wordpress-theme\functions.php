<?php
/**
 * OLX Marketplace Theme Functions
 * 
 * @package OLX_Marketplace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme setup
 */
function olx_marketplace_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));

    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'olx-marketplace'),
        'footer' => __('Footer Menu', 'olx-marketplace'),
    ));

    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'olx_marketplace_setup');

/**
 * Enqueue scripts and styles
 */
function olx_marketplace_scripts() {
    // Enqueue styles
    wp_enqueue_style('olx-marketplace-style', get_stylesheet_uri());
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');
    
    // Enqueue scripts
    wp_enqueue_script('olx-marketplace-main', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('olx-marketplace-main', 'olx_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('olx_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'olx_marketplace_scripts');

/**
 * Register widget areas
 */
function olx_marketplace_widgets_init() {
    register_sidebar(array(
        'name' => __('Sidebar', 'olx-marketplace'),
        'id' => 'sidebar-1',
        'description' => __('Add widgets here.', 'olx-marketplace'),
        'before_widget' => '<div id="%1$s" class="wp-widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h3 class="wp-widget-title">',
        'after_title' => '</h3>',
    ));

    register_sidebar(array(
        'name' => __('Footer Widget Area', 'olx-marketplace'),
        'id' => 'footer-widgets',
        'description' => __('Add widgets here for the footer.', 'olx-marketplace'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget' => '</div>',
        'before_title' => '<h4 class="footer-widget-title">',
        'after_title' => '</h4>',
    ));
}
add_action('widgets_init', 'olx_marketplace_widgets_init');

/**
 * Custom post type for Products
 */
function olx_marketplace_create_product_post_type() {
    $labels = array(
        'name' => 'Products',
        'singular_name' => 'Product',
        'menu_name' => 'Products',
        'add_new' => 'Add New Product',
        'add_new_item' => 'Add New Product',
        'edit_item' => 'Edit Product',
        'new_item' => 'New Product',
        'view_item' => 'View Product',
        'search_items' => 'Search Products',
        'not_found' => 'No products found',
        'not_found_in_trash' => 'No products found in trash',
    );

    $args = array(
        'labels' => $labels,
        'public' => true,
        'has_archive' => true,
        'publicly_queryable' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'products'),
        'capability_type' => 'post',
        'hierarchical' => false,
        'supports' => array('title', 'editor', 'thumbnail', 'author', 'custom-fields'),
        'menu_icon' => 'dashicons-cart',
        'show_in_rest' => true,
    );

    register_post_type('product', $args);
}
add_action('init', 'olx_marketplace_create_product_post_type');

/**
 * Custom taxonomy for Product Categories
 */
function olx_marketplace_create_product_taxonomy() {
    $labels = array(
        'name' => 'Product Categories',
        'singular_name' => 'Product Category',
        'search_items' => 'Search Categories',
        'all_items' => 'All Categories',
        'parent_item' => 'Parent Category',
        'parent_item_colon' => 'Parent Category:',
        'edit_item' => 'Edit Category',
        'update_item' => 'Update Category',
        'add_new_item' => 'Add New Category',
        'new_item_name' => 'New Category Name',
        'menu_name' => 'Categories',
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'product-category'),
        'show_in_rest' => true,
    );

    register_taxonomy('product_category', 'product', $args);
}
add_action('init', 'olx_marketplace_create_product_taxonomy');

/**
 * Add custom meta boxes for products
 */
function olx_marketplace_add_product_meta_boxes() {
    add_meta_box(
        'product_details',
        'Product Details',
        'olx_marketplace_product_details_callback',
        'product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'olx_marketplace_add_product_meta_boxes');

/**
 * Product details meta box callback
 */
function olx_marketplace_product_details_callback($post) {
    wp_nonce_field('olx_marketplace_save_product_details', 'olx_marketplace_product_details_nonce');
    
    $price = get_post_meta($post->ID, '_product_price', true);
    $location = get_post_meta($post->ID, '_product_location', true);
    $contact_phone = get_post_meta($post->ID, '_contact_phone', true);
    $contact_email = get_post_meta($post->ID, '_contact_email', true);
    $condition = get_post_meta($post->ID, '_product_condition', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="product_price">Price (Rs)</label></th>';
    echo '<td><input type="number" id="product_price" name="product_price" value="' . esc_attr($price) . '" /></td></tr>';
    
    echo '<tr><th><label for="product_location">Location</label></th>';
    echo '<td><input type="text" id="product_location" name="product_location" value="' . esc_attr($location) . '" /></td></tr>';
    
    echo '<tr><th><label for="contact_phone">Contact Phone</label></th>';
    echo '<td><input type="tel" id="contact_phone" name="contact_phone" value="' . esc_attr($contact_phone) . '" /></td></tr>';
    
    echo '<tr><th><label for="contact_email">Contact Email</label></th>';
    echo '<td><input type="email" id="contact_email" name="contact_email" value="' . esc_attr($contact_email) . '" /></td></tr>';
    
    echo '<tr><th><label for="product_condition">Condition</label></th>';
    echo '<td><select id="product_condition" name="product_condition">';
    echo '<option value="new"' . selected($condition, 'new', false) . '>New</option>';
    echo '<option value="used"' . selected($condition, 'used', false) . '>Used</option>';
    echo '<option value="refurbished"' . selected($condition, 'refurbished', false) . '>Refurbished</option>';
    echo '</select></td></tr>';
    echo '</table>';
}

/**
 * Save product details
 */
function olx_marketplace_save_product_details($post_id) {
    if (!isset($_POST['olx_marketplace_product_details_nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['olx_marketplace_product_details_nonce'], 'olx_marketplace_save_product_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $fields = array('product_price', 'product_location', 'contact_phone', 'contact_email', 'product_condition');
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'olx_marketplace_save_product_details');

/**
 * AJAX handler for product search
 */
function olx_marketplace_ajax_search_products() {
    check_ajax_referer('olx_nonce', 'nonce');
    
    $search_term = sanitize_text_field($_POST['search_term']);
    $location = sanitize_text_field($_POST['location']);
    $category = sanitize_text_field($_POST['category']);
    
    $args = array(
        'post_type' => 'product',
        'post_status' => 'publish',
        'posts_per_page' => 12,
        's' => $search_term,
    );
    
    if (!empty($location)) {
        $args['meta_query'][] = array(
            'key' => '_product_location',
            'value' => $location,
            'compare' => 'LIKE'
        );
    }
    
    if (!empty($category)) {
        $args['tax_query'][] = array(
            'taxonomy' => 'product_category',
            'field' => 'slug',
            'terms' => $category
        );
    }
    
    $products = new WP_Query($args);
    $results = array();
    
    if ($products->have_posts()) {
        while ($products->have_posts()) {
            $products->the_post();
            $results[] = array(
                'id' => get_the_ID(),
                'title' => get_the_title(),
                'price' => 'Rs ' . number_format(get_post_meta(get_the_ID(), '_product_price', true)),
                'location' => get_post_meta(get_the_ID(), '_product_location', true),
                'date' => human_time_diff(get_the_time('U'), current_time('timestamp')) . ' ago',
                'image' => get_the_post_thumbnail_url(get_the_ID(), 'medium'),
                'url' => get_permalink()
            );
        }
    }
    
    wp_reset_postdata();
    wp_send_json_success($results);
}
add_action('wp_ajax_search_products', 'olx_marketplace_ajax_search_products');
add_action('wp_ajax_nopriv_search_products', 'olx_marketplace_ajax_search_products');

/**
 * Custom excerpt length
 */
function olx_marketplace_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'olx_marketplace_excerpt_length');

/**
 * Custom excerpt more
 */
function olx_marketplace_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'olx_marketplace_excerpt_more');
?>
