<?php
/*
Plugin Name: WhatsApp Chat Widget Pro
Plugin URI: https://codecanyon.net/
Description: Professional WhatsApp chat widget with multiple agents, working hours, custom themes, and advanced features
Version: 2.0.0
Author: Your Name
Author URI: https://yourwebsite.com
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: whatsapp-widget-pro
Domain Path: /languages
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Network: false
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access denied.');
}

// Define plugin constants
define('WHATSAPP_WIDGET_VERSION', '2.0.0');
define('WHATSAPP_WIDGET_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WHATSAPP_WIDGET_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('WHATSAPP_WIDGET_PLUGIN_BASENAME', plugin_basename(__FILE__));

class WhatsAppChatWidget {

    private $default_settings;

    public function __construct() {
        // Initialize default settings
        $this->default_settings = array(
            'enabled' => true,
            'phone' => '+923001234567',
            'message' => 'Hello! I need help.',
            'position' => 'bottom-right',
            'size' => '60',
            'theme' => 'default',
            'show_on_mobile' => true,
            'show_on_desktop' => true,
            'working_hours_enabled' => false,
            'working_hours' => array(
                'monday' => array('start' => '09:00', 'end' => '17:00', 'enabled' => true),
                'tuesday' => array('start' => '09:00', 'end' => '17:00', 'enabled' => true),
                'wednesday' => array('start' => '09:00', 'end' => '17:00', 'enabled' => true),
                'thursday' => array('start' => '09:00', 'end' => '17:00', 'enabled' => true),
                'friday' => array('start' => '09:00', 'end' => '17:00', 'enabled' => true),
                'saturday' => array('start' => '09:00', 'end' => '17:00', 'enabled' => false),
                'sunday' => array('start' => '09:00', 'end' => '17:00', 'enabled' => false)
            ),
            'timezone' => 'Asia/Karachi',
            'offline_message' => 'We are currently offline. Please leave a message.',
            'agents' => array(
                array(
                    'name' => 'Support Agent',
                    'phone' => '+923001234567',
                    'title' => 'Customer Support',
                    'avatar' => '',
                    'enabled' => true
                )
            ),
            'bubble_enabled' => false,
            'bubble_title' => 'Need Help?',
            'bubble_subtitle' => 'Click here to chat with us',
            'custom_css' => '',
            'analytics_enabled' => true
        );

        // WordPress hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'display_widget'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('wp_ajax_whatsapp_widget_save_settings', array($this, 'save_settings_ajax'));
        add_action('wp_ajax_whatsapp_widget_get_agent_status', array($this, 'get_agent_status_ajax'));
        add_action('wp_ajax_whatsapp_widget_track_click', array($this, 'track_click_ajax'));
        add_action('wp_ajax_nopriv_whatsapp_widget_track_click', array($this, 'track_click_ajax'));

        // Plugin activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    public function activate() {
        // Set default options on activation
        foreach ($this->default_settings as $key => $value) {
            add_option('whatsapp_' . $key, $value);
        }

        // Create database table for analytics (if needed)
        $this->create_analytics_table();
    }

    public function deactivate() {
        // Clean up if needed
    }

    private function create_analytics_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'whatsapp_widget_analytics';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            click_time datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            agent_phone varchar(20) NOT NULL,
            user_ip varchar(45) NOT NULL,
            user_agent text NOT NULL,
            page_url varchar(255) NOT NULL,
            PRIMARY KEY (id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    public function enqueue_scripts() {
        wp_enqueue_style('whatsapp-widget-css', plugin_dir_url(__FILE__) . 'style.css', array(), WHATSAPP_WIDGET_VERSION);
        wp_enqueue_script('whatsapp-widget-js', plugin_dir_url(__FILE__) . 'script.js', array('jquery'), WHATSAPP_WIDGET_VERSION, true);

        // Localize script for AJAX
        wp_localize_script('whatsapp-widget-js', 'whatsapp_widget_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('whatsapp_widget_nonce')
        ));

        // Add custom CSS if any
        $custom_css = get_option('whatsapp_custom_css', '');
        if (!empty($custom_css)) {
            wp_add_inline_style('whatsapp-widget-css', $custom_css);
        }
    }
    
    public function display_widget() {
        // Check if widget is enabled
        if (!get_option('whatsapp_enabled', true)) {
            return;
        }

        // Check device visibility settings
        $show_on_mobile = get_option('whatsapp_show_on_mobile', true);
        $show_on_desktop = get_option('whatsapp_show_on_desktop', true);

        $is_mobile = wp_is_mobile();
        if (($is_mobile && !$show_on_mobile) || (!$is_mobile && !$show_on_desktop)) {
            return;
        }

        // Get settings
        $position = get_option('whatsapp_position', 'bottom-right');
        $size = get_option('whatsapp_size', '60');
        $theme = get_option('whatsapp_theme', 'default');
        $bubble_enabled = get_option('whatsapp_bubble_enabled', false);
        $agents = get_option('whatsapp_agents', $this->default_settings['agents']);

        // Check working hours
        $is_online = $this->is_within_working_hours();
        $available_agents = $this->get_available_agents($agents, $is_online);

        if (empty($available_agents)) {
            if (!$is_online) {
                $this->display_offline_widget($position, $size, $theme);
                return;
            }
        }

        // Display widget based on type
        if ($bubble_enabled) {
            $this->display_bubble_widget($available_agents, $position, $size, $theme, $is_online);
        } else {
            $this->display_simple_widget($available_agents, $position, $size, $theme, $is_online);
        }
    }

    private function is_within_working_hours() {
        if (!get_option('whatsapp_working_hours_enabled', false)) {
            return true;
        }

        $timezone = get_option('whatsapp_timezone', 'Asia/Karachi');
        $working_hours = get_option('whatsapp_working_hours', $this->default_settings['working_hours']);

        try {
            $tz = new DateTimeZone($timezone);
            $now = new DateTime('now', $tz);
            $current_day = strtolower($now->format('l'));

            if (!isset($working_hours[$current_day]) || !$working_hours[$current_day]['enabled']) {
                return false;
            }

            $start_time = DateTime::createFromFormat('H:i', $working_hours[$current_day]['start'], $tz);
            $end_time = DateTime::createFromFormat('H:i', $working_hours[$current_day]['end'], $tz);

            return ($now >= $start_time && $now <= $end_time);
        } catch (Exception $e) {
            return true; // Default to online if timezone error
        }
    }

    private function get_available_agents($agents, $is_online) {
        if (!$is_online) {
            return array();
        }

        $available = array();
        foreach ($agents as $agent) {
            if (isset($agent['enabled']) && $agent['enabled']) {
                $available[] = $agent;
            }
        }

        return $available;
    }

    private function display_simple_widget($agents, $position, $size, $theme, $is_online) {
        if (empty($agents)) {
            return;
        }

        // Use first available agent for simple widget
        $agent = $agents[0];
        $phone = $agent['phone'];
        $message = get_option('whatsapp_message', 'Hello! I need help.');

        echo '<div id="whatsapp-widget" class="whatsapp-widget whatsapp-theme-' . esc_attr($theme) . ' ' . esc_attr($position) . '" style="width: ' . esc_attr($size) . 'px; height: ' . esc_attr($size) . 'px;" data-agent-phone="' . esc_attr($phone) . '">';
        echo '<a href="https://wa.me/' . esc_attr($phone) . '?text=' . urlencode($message) . '" target="_blank" class="whatsapp-link">';
        echo '<i class="fab fa-whatsapp"></i>';
        if ($is_online) {
            echo '<span class="online-indicator"></span>';
        }
        echo '</a>';
        echo '</div>';
    }

    private function display_bubble_widget($agents, $position, $size, $theme, $is_online) {
        $bubble_title = get_option('whatsapp_bubble_title', 'Need Help?');
        $bubble_subtitle = get_option('whatsapp_bubble_subtitle', 'Click here to chat with us');

        echo '<div id="whatsapp-bubble-widget" class="whatsapp-bubble-widget whatsapp-theme-' . esc_attr($theme) . ' ' . esc_attr($position) . '">';
        echo '<div class="bubble-trigger" style="width: ' . esc_attr($size) . 'px; height: ' . esc_attr($size) . 'px;">';
        echo '<i class="fab fa-whatsapp"></i>';
        if ($is_online) {
            echo '<span class="online-indicator"></span>';
        }
        echo '</div>';

        echo '<div class="bubble-content">';
        echo '<div class="bubble-header">';
        echo '<h4>' . esc_html($bubble_title) . '</h4>';
        echo '<p>' . esc_html($bubble_subtitle) . '</p>';
        echo '<button class="bubble-close">&times;</button>';
        echo '</div>';

        echo '<div class="bubble-agents">';
        foreach ($agents as $index => $agent) {
            $message = get_option('whatsapp_message', 'Hello! I need help.');
            echo '<div class="agent-item" data-agent-phone="' . esc_attr($agent['phone']) . '">';
            echo '<div class="agent-avatar">';
            if (!empty($agent['avatar'])) {
                echo '<img src="' . esc_url($agent['avatar']) . '" alt="' . esc_attr($agent['name']) . '">';
            } else {
                echo '<i class="fas fa-user"></i>';
            }
            echo '</div>';
            echo '<div class="agent-info">';
            echo '<h5>' . esc_html($agent['name']) . '</h5>';
            echo '<p>' . esc_html($agent['title']) . '</p>';
            echo '</div>';
            echo '<a href="https://wa.me/' . esc_attr($agent['phone']) . '?text=' . urlencode($message) . '" target="_blank" class="agent-chat-btn">Chat</a>';
            echo '</div>';
        }
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    private function display_offline_widget($position, $size, $theme) {
        $offline_message = get_option('whatsapp_offline_message', 'We are currently offline. Please leave a message.');

        echo '<div id="whatsapp-widget-offline" class="whatsapp-widget whatsapp-offline whatsapp-theme-' . esc_attr($theme) . ' ' . esc_attr($position) . '" style="width: ' . esc_attr($size) . 'px; height: ' . esc_attr($size) . 'px;">';
        echo '<div class="offline-content">';
        echo '<i class="fab fa-whatsapp"></i>';
        echo '<span class="offline-indicator"></span>';
        echo '<div class="offline-tooltip">' . esc_html($offline_message) . '</div>';
        echo '</div>';
        echo '</div>';
    }
    
    public function admin_menu() {
        add_options_page('WhatsApp Widget', 'WhatsApp Widget', 'manage_options', 'whatsapp-widget', array($this, 'admin_page'));
        add_submenu_page('options-general.php', 'WhatsApp Analytics', 'WhatsApp Analytics', 'manage_options', 'whatsapp-analytics', array($this, 'analytics_page'));
    }
    
    public function admin_init() {
        // Register all settings
        $settings = array(
            'whatsapp_enabled', 'whatsapp_phone', 'whatsapp_message', 'whatsapp_position',
            'whatsapp_size', 'whatsapp_theme', 'whatsapp_show_on_mobile', 'whatsapp_show_on_desktop',
            'whatsapp_working_hours_enabled', 'whatsapp_working_hours', 'whatsapp_timezone',
            'whatsapp_offline_message', 'whatsapp_agents', 'whatsapp_bubble_enabled',
            'whatsapp_bubble_title', 'whatsapp_bubble_subtitle', 'whatsapp_custom_css',
            'whatsapp_analytics_enabled'
        );

        foreach ($settings as $setting) {
            register_setting('whatsapp_settings', $setting);
        }

        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
    }

    public function admin_enqueue_scripts($hook) {
        if ($hook !== 'settings_page_whatsapp-widget') {
            return;
        }

        wp_enqueue_script('whatsapp-admin-js', plugin_dir_url(__FILE__) . 'admin-script.js', array('jquery'), WHATSAPP_WIDGET_VERSION, true);
        wp_localize_script('whatsapp-admin-js', 'whatsapp_admin_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('whatsapp_admin_nonce')
        ));
    }
    
    public function admin_page() {
        // Handle form submission
        if (isset($_POST['whatsapp_save_settings']) && wp_verify_nonce($_POST['whatsapp_nonce'], 'whatsapp_save_settings')) {
            $this->save_admin_settings();
            echo '<div class="whatsapp-success-message" style="display: block;">Settings saved successfully!</div>';
        }

        // Get current settings
        $settings = $this->get_admin_settings();
        ?>

        <div class="whatsapp-admin-container">
            <div class="whatsapp-admin-header">
                <h1><i class="fab fa-whatsapp"></i> WhatsApp Widget Pro</h1>
                <p>Professional WhatsApp chat widget with advanced features</p>
            </div>

            <div class="whatsapp-admin-tabs">
                <button class="whatsapp-admin-tab active" data-tab="general">
                    <i class="fas fa-cog"></i> General Settings
                </button>
                <button class="whatsapp-admin-tab" data-tab="agents">
                    <i class="fas fa-users"></i> Agents
                </button>
                <button class="whatsapp-admin-tab" data-tab="appearance">
                    <i class="fas fa-palette"></i> Appearance
                </button>
                <button class="whatsapp-admin-tab" data-tab="working-hours">
                    <i class="fas fa-clock"></i> Working Hours
                </button>
                <button class="whatsapp-admin-tab" data-tab="advanced">
                    <i class="fas fa-code"></i> Advanced
                </button>
                <button class="whatsapp-admin-tab" data-tab="preview">
                    <i class="fas fa-eye"></i> Preview
                </button>
            </div>

            <div class="whatsapp-admin-content">
                <form method="post" id="whatsapp-admin-form">
                    <?php wp_nonce_field('whatsapp_save_settings', 'whatsapp_nonce'); ?>

                    <!-- General Settings Tab -->
                    <div class="whatsapp-tab-content active" id="tab-general">
                        <?php $this->render_general_tab($settings); ?>
                    </div>

                    <!-- Agents Tab -->
                    <div class="whatsapp-tab-content" id="tab-agents">
                        <?php $this->render_agents_tab($settings); ?>
                    </div>

                    <!-- Appearance Tab -->
                    <div class="whatsapp-tab-content" id="tab-appearance">
                        <?php $this->render_appearance_tab($settings); ?>
                    </div>

                    <!-- Working Hours Tab -->
                    <div class="whatsapp-tab-content" id="tab-working-hours">
                        <?php $this->render_working_hours_tab($settings); ?>
                    </div>

                    <!-- Advanced Tab -->
                    <div class="whatsapp-tab-content" id="tab-advanced">
                        <?php $this->render_advanced_tab($settings); ?>
                    </div>

                    <!-- Preview Tab -->
                    <div class="whatsapp-tab-content" id="tab-preview">
                        <?php $this->render_preview_tab($settings); ?>
                    </div>

                    <button type="submit" name="whatsapp_save_settings" class="whatsapp-save-button">
                        <i class="fas fa-save"></i> Save All Settings
                    </button>
                </form>
            </div>
        </div>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <link rel="stylesheet" href="<?php echo plugin_dir_url(__FILE__); ?>admin-style.css">
        <?php
    }

    private function get_admin_settings() {
        $settings = array();
        foreach ($this->default_settings as $key => $default) {
            $settings[$key] = get_option('whatsapp_' . $key, $default);
        }
        return $settings;
    }

    private function save_admin_settings() {
        $settings_to_save = array(
            'enabled' => isset($_POST['whatsapp_enabled']),
            'phone' => sanitize_text_field($_POST['whatsapp_phone']),
            'message' => sanitize_textarea_field($_POST['whatsapp_message']),
            'position' => sanitize_text_field($_POST['whatsapp_position']),
            'size' => sanitize_text_field($_POST['whatsapp_size']),
            'theme' => sanitize_text_field($_POST['whatsapp_theme']),
            'show_on_mobile' => isset($_POST['whatsapp_show_on_mobile']),
            'show_on_desktop' => isset($_POST['whatsapp_show_on_desktop']),
            'working_hours_enabled' => isset($_POST['whatsapp_working_hours_enabled']),
            'timezone' => sanitize_text_field($_POST['whatsapp_timezone']),
            'offline_message' => sanitize_textarea_field($_POST['whatsapp_offline_message']),
            'bubble_enabled' => isset($_POST['whatsapp_bubble_enabled']),
            'bubble_title' => sanitize_text_field($_POST['whatsapp_bubble_title']),
            'bubble_subtitle' => sanitize_text_field($_POST['whatsapp_bubble_subtitle']),
            'custom_css' => wp_strip_all_tags($_POST['whatsapp_custom_css']),
            'analytics_enabled' => isset($_POST['whatsapp_analytics_enabled'])
        );

        // Handle working hours
        if (isset($_POST['working_hours'])) {
            $working_hours = array();
            foreach ($_POST['working_hours'] as $day => $hours) {
                $working_hours[$day] = array(
                    'start' => sanitize_text_field($hours['start']),
                    'end' => sanitize_text_field($hours['end']),
                    'enabled' => isset($hours['enabled'])
                );
            }
            $settings_to_save['working_hours'] = $working_hours;
        }

        // Handle agents
        if (isset($_POST['agents'])) {
            $agents = array();
            foreach ($_POST['agents'] as $agent_data) {
                if (!empty($agent_data['phone'])) {
                    $agents[] = array(
                        'name' => sanitize_text_field($agent_data['name']),
                        'phone' => sanitize_text_field($agent_data['phone']),
                        'title' => sanitize_text_field($agent_data['title']),
                        'avatar' => esc_url_raw($agent_data['avatar']),
                        'enabled' => isset($agent_data['enabled'])
                    );
                }
            }
            $settings_to_save['agents'] = $agents;
        }

        // Save all settings
        foreach ($settings_to_save as $key => $value) {
            update_option('whatsapp_' . $key, $value);
        }
    }

    private function render_general_tab($settings) {
        ?>
        <div class="whatsapp-form-grid">
            <div class="whatsapp-form-section">
                <h3><i class="fas fa-toggle-on"></i> Widget Status</h3>

                <div class="whatsapp-form-group">
                    <label>
                        <span class="whatsapp-toggle">
                            <input type="checkbox" name="whatsapp_enabled" <?php checked($settings['enabled']); ?>>
                            <span class="whatsapp-toggle-slider"></span>
                        </span>
                        Enable WhatsApp Widget
                    </label>
                </div>

                <div class="whatsapp-form-group">
                    <label>
                        <span class="whatsapp-toggle">
                            <input type="checkbox" name="whatsapp_show_on_desktop" <?php checked($settings['show_on_desktop']); ?>>
                            <span class="whatsapp-toggle-slider"></span>
                        </span>
                        Show on Desktop
                    </label>
                </div>

                <div class="whatsapp-form-group">
                    <label>
                        <span class="whatsapp-toggle">
                            <input type="checkbox" name="whatsapp_show_on_mobile" <?php checked($settings['show_on_mobile']); ?>>
                            <span class="whatsapp-toggle-slider"></span>
                        </span>
                        Show on Mobile
                    </label>
                </div>
            </div>

            <div class="whatsapp-form-section">
                <h3><i class="fas fa-phone"></i> Contact Information</h3>

                <div class="whatsapp-form-group">
                    <label for="whatsapp_phone">WhatsApp Phone Number</label>
                    <input type="text" id="whatsapp_phone" name="whatsapp_phone"
                           value="<?php echo esc_attr($settings['phone']); ?>"
                           placeholder="+923001234567" required>
                    <small>Include country code (e.g., +92 for Pakistan)</small>
                </div>

                <div class="whatsapp-form-group">
                    <label for="whatsapp_message">Default Message</label>
                    <textarea id="whatsapp_message" name="whatsapp_message"
                              placeholder="Hello! I need help."><?php echo esc_textarea($settings['message']); ?></textarea>
                    <small>This message will be pre-filled when users click the widget</small>
                </div>
            </div>
        </div>

        <div class="whatsapp-form-grid">
            <div class="whatsapp-form-section">
                <h3><i class="fas fa-map-marker-alt"></i> Position & Size</h3>

                <div class="whatsapp-form-group">
                    <label for="whatsapp_position">Widget Position</label>
                    <select id="whatsapp_position" name="whatsapp_position">
                        <option value="bottom-right" <?php selected($settings['position'], 'bottom-right'); ?>>Bottom Right</option>
                        <option value="bottom-left" <?php selected($settings['position'], 'bottom-left'); ?>>Bottom Left</option>
                        <option value="top-right" <?php selected($settings['position'], 'top-right'); ?>>Top Right</option>
                        <option value="top-left" <?php selected($settings['position'], 'top-left'); ?>>Top Left</option>
                    </select>
                </div>

                <div class="whatsapp-form-group">
                    <label for="whatsapp_size">Widget Size</label>
                    <select id="whatsapp_size" name="whatsapp_size">
                        <option value="50" <?php selected($settings['size'], '50'); ?>>Small (50px)</option>
                        <option value="60" <?php selected($settings['size'], '60'); ?>>Medium (60px)</option>
                        <option value="70" <?php selected($settings['size'], '70'); ?>>Large (70px)</option>
                        <option value="80" <?php selected($settings['size'], '80'); ?>>Extra Large (80px)</option>
                    </select>
                </div>
            </div>

            <div class="whatsapp-form-section">
                <h3><i class="fas fa-chart-line"></i> Analytics</h3>

                <div class="whatsapp-form-group">
                    <label>
                        <span class="whatsapp-toggle">
                            <input type="checkbox" name="whatsapp_analytics_enabled" <?php checked($settings['analytics_enabled']); ?>>
                            <span class="whatsapp-toggle-slider"></span>
                        </span>
                        Enable Click Analytics
                    </label>
                    <small>Track widget clicks for analytics dashboard</small>
                </div>
            </div>
        </div>
        <?php
    }

    private function render_agents_tab($settings) {
        $agents = $settings['agents'];
        ?>
        <div class="whatsapp-form-section">
            <h3><i class="fas fa-users"></i> Manage Agents</h3>
            <p>Add multiple WhatsApp agents to handle customer inquiries. Customers can choose which agent to contact.</p>

            <div class="whatsapp-agents-list" id="whatsapp-agents-list">
                <?php foreach ($agents as $index => $agent): ?>
                <div class="whatsapp-agent-item">
                    <div class="whatsapp-agent-header">
                        <div class="whatsapp-agent-toggle">
                            <span class="whatsapp-toggle">
                                <input type="checkbox" name="agents[<?php echo $index; ?>][enabled]" <?php checked($agent['enabled']); ?>>
                                <span class="whatsapp-toggle-slider"></span>
                            </span>
                            <strong>Agent <?php echo $index + 1; ?></strong>
                        </div>
                        <button type="button" class="whatsapp-agent-remove" onclick="removeAgent(this)">Remove</button>
                    </div>

                    <div class="whatsapp-form-grid">
                        <div class="whatsapp-form-group">
                            <label>Agent Name</label>
                            <input type="text" name="agents[<?php echo $index; ?>][name]"
                                   value="<?php echo esc_attr($agent['name']); ?>"
                                   placeholder="John Doe" required>
                        </div>

                        <div class="whatsapp-form-group">
                            <label>WhatsApp Number</label>
                            <input type="text" name="agents[<?php echo $index; ?>][phone]"
                                   value="<?php echo esc_attr($agent['phone']); ?>"
                                   placeholder="+923001234567" required>
                        </div>

                        <div class="whatsapp-form-group">
                            <label>Job Title</label>
                            <input type="text" name="agents[<?php echo $index; ?>][title]"
                                   value="<?php echo esc_attr($agent['title']); ?>"
                                   placeholder="Customer Support">
                        </div>

                        <div class="whatsapp-form-group">
                            <label>Avatar URL (Optional)</label>
                            <input type="url" name="agents[<?php echo $index; ?>][avatar]"
                                   value="<?php echo esc_url($agent['avatar']); ?>"
                                   placeholder="https://example.com/avatar.jpg">
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <button type="button" class="whatsapp-add-agent" onclick="addAgent()">
                <i class="fas fa-plus"></i> Add New Agent
            </button>
        </div>
        <?php
    }

    private function render_appearance_tab($settings) {
        ?>
        <div class="whatsapp-form-grid">
            <div class="whatsapp-form-section">
                <h3><i class="fas fa-palette"></i> Theme Selection</h3>

                <div class="whatsapp-form-group">
                    <label for="whatsapp_theme">Widget Theme</label>
                    <select id="whatsapp_theme" name="whatsapp_theme">
                        <option value="default" <?php selected($settings['theme'], 'default'); ?>>Default (Green)</option>
                        <option value="minimal" <?php selected($settings['theme'], 'minimal'); ?>>Minimal (White)</option>
                        <option value="dark" <?php selected($settings['theme'], 'dark'); ?>>Dark Theme</option>
                        <option value="gradient" <?php selected($settings['theme'], 'gradient'); ?>>Gradient</option>
                        <option value="square" <?php selected($settings['theme'], 'square'); ?>>Rounded Square</option>
                        <option value="pulse" <?php selected($settings['theme'], 'pulse'); ?>>Pulsing Effect</option>
                    </select>
                </div>
            </div>

            <div class="whatsapp-form-section">
                <h3><i class="fas fa-comments"></i> Chat Bubble</h3>

                <div class="whatsapp-form-group">
                    <label>
                        <span class="whatsapp-toggle">
                            <input type="checkbox" name="whatsapp_bubble_enabled" <?php checked($settings['bubble_enabled']); ?>>
                            <span class="whatsapp-toggle-slider"></span>
                        </span>
                        Enable Chat Bubble Interface
                    </label>
                    <small>Show a chat bubble with agent list instead of direct WhatsApp link</small>
                </div>

                <div class="whatsapp-form-group">
                    <label for="whatsapp_bubble_title">Bubble Title</label>
                    <input type="text" id="whatsapp_bubble_title" name="whatsapp_bubble_title"
                           value="<?php echo esc_attr($settings['bubble_title']); ?>"
                           placeholder="Need Help?">
                </div>

                <div class="whatsapp-form-group">
                    <label for="whatsapp_bubble_subtitle">Bubble Subtitle</label>
                    <input type="text" id="whatsapp_bubble_subtitle" name="whatsapp_bubble_subtitle"
                           value="<?php echo esc_attr($settings['bubble_subtitle']); ?>"
                           placeholder="Click here to chat with us">
                </div>
            </div>
        </div>

        <div class="whatsapp-form-section">
            <h3><i class="fas fa-code"></i> Custom CSS</h3>
            <p>Add custom CSS to further customize the widget appearance.</p>

            <div class="whatsapp-form-group">
                <label for="whatsapp_custom_css">Custom CSS Code</label>
                <textarea id="whatsapp_custom_css" name="whatsapp_custom_css"
                          style="height: 200px; font-family: monospace;"
                          placeholder="/* Add your custom CSS here */
.whatsapp-widget a {
    background: #your-color !important;
}"><?php echo esc_textarea($settings['custom_css']); ?></textarea>
                <small>Use !important to override default styles if needed</small>
            </div>
        </div>
        <?php
    }

    private function render_working_hours_tab($settings) {
        $working_hours = $settings['working_hours'];
        $timezone = $settings['timezone'];
        ?>
        <div class="whatsapp-form-grid">
            <div class="whatsapp-form-section">
                <h3><i class="fas fa-clock"></i> Working Hours Settings</h3>

                <div class="whatsapp-form-group">
                    <label>
                        <span class="whatsapp-toggle">
                            <input type="checkbox" name="whatsapp_working_hours_enabled" <?php checked($settings['working_hours_enabled']); ?>>
                            <span class="whatsapp-toggle-slider"></span>
                        </span>
                        Enable Working Hours
                    </label>
                    <small>Show/hide widget based on business hours</small>
                </div>

                <div class="whatsapp-form-group">
                    <label for="whatsapp_timezone">Timezone</label>
                    <select id="whatsapp_timezone" name="whatsapp_timezone">
                        <option value="Asia/Karachi" <?php selected($timezone, 'Asia/Karachi'); ?>>Pakistan (Asia/Karachi)</option>
                        <option value="Asia/Dubai" <?php selected($timezone, 'Asia/Dubai'); ?>>UAE (Asia/Dubai)</option>
                        <option value="Asia/Kolkata" <?php selected($timezone, 'Asia/Kolkata'); ?>>India (Asia/Kolkata)</option>
                        <option value="Europe/London" <?php selected($timezone, 'Europe/London'); ?>>UK (Europe/London)</option>
                        <option value="America/New_York" <?php selected($timezone, 'America/New_York'); ?>>US East (America/New_York)</option>
                        <option value="America/Los_Angeles" <?php selected($timezone, 'America/Los_Angeles'); ?>>US West (America/Los_Angeles)</option>
                    </select>
                </div>

                <div class="whatsapp-form-group">
                    <label for="whatsapp_offline_message">Offline Message</label>
                    <textarea id="whatsapp_offline_message" name="whatsapp_offline_message"
                              placeholder="We are currently offline. Please leave a message."><?php echo esc_textarea($settings['offline_message']); ?></textarea>
                    <small>Message shown when outside working hours</small>
                </div>
            </div>

            <div class="whatsapp-form-section">
                <h3><i class="fas fa-calendar-week"></i> Weekly Schedule</h3>

                <div class="whatsapp-working-hours">
                    <?php
                    $days = array('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday');
                    $day_names = array('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday');

                    foreach ($days as $index => $day):
                        $day_schedule = $working_hours[$day];
                    ?>
                    <div class="whatsapp-day-schedule">
                        <div class="whatsapp-day-name"><?php echo $day_names[$index]; ?></div>
                        <div>
                            <span class="whatsapp-toggle">
                                <input type="checkbox" name="working_hours[<?php echo $day; ?>][enabled]" <?php checked($day_schedule['enabled']); ?>>
                                <span class="whatsapp-toggle-slider"></span>
                            </span>
                        </div>
                        <input type="time" name="working_hours[<?php echo $day; ?>][start]"
                               value="<?php echo esc_attr($day_schedule['start']); ?>">
                        <input type="time" name="working_hours[<?php echo $day; ?>][end]"
                               value="<?php echo esc_attr($day_schedule['end']); ?>">
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php
    }

    private function render_advanced_tab($settings) {
        ?>
        <div class="whatsapp-form-section">
            <h3><i class="fas fa-code"></i> Advanced Features</h3>
            <p>Advanced settings for developers and power users.</p>

            <div class="whatsapp-form-group">
                <label><strong>Shortcode Usage</strong></label>
                <p>Use this shortcode to display the widget in specific locations:</p>
                <code>[whatsapp_widget]</code>
                <br><br>
                <p>With custom parameters:</p>
                <code>[whatsapp_widget phone="+923001234567" message="Custom message" size="70"]</code>
            </div>

            <div class="whatsapp-form-group">
                <label><strong>PHP Integration</strong></label>
                <p>Use this PHP code to display the widget programmatically:</p>
                <code>&lt;?php do_shortcode('[whatsapp_widget]'); ?&gt;</code>
            </div>

            <div class="whatsapp-form-group">
                <label><strong>JavaScript Events</strong></label>
                <p>Listen to widget events in your custom JavaScript:</p>
                <code>
                // Widget click event<br>
                window.WhatsAppWidget.trackEvent('Custom Event', 'action', 'label');<br><br>
                // Custom tracking function<br>
                window.whatsappWidgetCustomTrack = function(category, action, label) {<br>
                &nbsp;&nbsp;// Your custom tracking code<br>
                };
                </code>
            </div>

            <div class="whatsapp-form-group">
                <label><strong>CSS Classes</strong></label>
                <p>Available CSS classes for customization:</p>
                <ul>
                    <li><code>.whatsapp-widget</code> - Main widget container</li>
                    <li><code>.whatsapp-theme-{theme}</code> - Theme-specific styling</li>
                    <li><code>.whatsapp-bubble-widget</code> - Bubble widget container</li>
                    <li><code>.online-indicator</code> - Online status indicator</li>
                    <li><code>.offline-indicator</code> - Offline status indicator</li>
                </ul>
            </div>
        </div>
        <?php
    }

    private function render_preview_tab($settings) {
        ?>
        <div class="whatsapp-preview-section">
            <h3><i class="fas fa-eye"></i> Live Preview</h3>
            <p>See how your widget will look on your website. Changes are reflected in real-time.</p>

            <div class="whatsapp-preview-container" id="whatsapp-preview-container">
                <!-- Preview will be generated by JavaScript -->
            </div>

            <div class="whatsapp-form-grid">
                <div>
                    <h4>Current Settings Summary:</h4>
                    <ul>
                        <li><strong>Status:</strong> <?php echo $settings['enabled'] ? 'Enabled' : 'Disabled'; ?></li>
                        <li><strong>Position:</strong> <?php echo ucwords(str_replace('-', ' ', $settings['position'])); ?></li>
                        <li><strong>Size:</strong> <?php echo $settings['size']; ?>px</li>
                        <li><strong>Theme:</strong> <?php echo ucfirst($settings['theme']); ?></li>
                        <li><strong>Agents:</strong> <?php echo count($settings['agents']); ?> configured</li>
                        <li><strong>Working Hours:</strong> <?php echo $settings['working_hours_enabled'] ? 'Enabled' : 'Disabled'; ?></li>
                        <li><strong>Bubble Mode:</strong> <?php echo $settings['bubble_enabled'] ? 'Enabled' : 'Disabled'; ?></li>
                    </ul>
                </div>

                <div>
                    <h4>Quick Actions:</h4>
                    <button type="button" class="whatsapp-save-button" onclick="testWidget()">
                        <i class="fas fa-play"></i> Test Widget
                    </button>
                    <br><br>
                    <button type="button" class="whatsapp-save-button" onclick="exportSettings()">
                        <i class="fas fa-download"></i> Export Settings
                    </button>
                </div>
            </div>
        </div>
        <?php
    }

    // AJAX handlers
    public function save_settings_ajax() {
        check_ajax_referer('whatsapp_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $this->save_admin_settings();
        wp_send_json_success('Settings saved successfully');
    }

    public function get_agent_status_ajax() {
        check_ajax_referer('whatsapp_admin_nonce', 'nonce');

        $agents = get_option('whatsapp_agents', $this->default_settings['agents']);
        $is_online = $this->is_within_working_hours();

        wp_send_json_success(array(
            'agents' => $agents,
            'is_online' => $is_online
        ));
    }

    public function track_click_ajax() {
        check_ajax_referer('whatsapp_widget_nonce', 'nonce');

        if (!get_option('whatsapp_analytics_enabled', true)) {
            wp_send_json_success('Analytics disabled');
            return;
        }

        global $wpdb;

        $table_name = $wpdb->prefix . 'whatsapp_widget_analytics';

        $data = array(
            'agent_phone' => sanitize_text_field($_POST['agent_phone']),
            'agent_name' => sanitize_text_field($_POST['agent_name']),
            'page_url' => esc_url_raw($_POST['page_url']),
            'user_ip' => $this->get_user_ip(),
            'user_agent' => sanitize_text_field($_POST['user_agent']),
            'referrer' => esc_url_raw($_POST['referrer']),
            'click_time' => current_time('mysql')
        );

        $result = $wpdb->insert($table_name, $data);

        if ($result !== false) {
            wp_send_json_success('Click tracked successfully');
        } else {
            wp_send_json_error('Failed to track click');
        }
    }

    private function get_user_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }

    public function analytics_page() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'whatsapp_widget_analytics';

        // Get analytics data
        $total_clicks = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        $today_clicks = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE DATE(click_time) = %s", current_time('Y-m-d')));
        $this_week_clicks = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE WEEK(click_time) = WEEK(%s)", current_time('Y-m-d')));
        $this_month_clicks = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE MONTH(click_time) = MONTH(%s) AND YEAR(click_time) = YEAR(%s)", current_time('Y-m-d'), current_time('Y-m-d')));

        // Get top agents
        $top_agents = $wpdb->get_results("SELECT agent_phone, agent_name, COUNT(*) as clicks FROM $table_name GROUP BY agent_phone ORDER BY clicks DESC LIMIT 5");

        // Get recent clicks
        $recent_clicks = $wpdb->get_results("SELECT * FROM $table_name ORDER BY click_time DESC LIMIT 10");

        // Get daily clicks for chart (last 30 days)
        $daily_clicks = $wpdb->get_results($wpdb->prepare("
            SELECT DATE(click_time) as date, COUNT(*) as clicks
            FROM $table_name
            WHERE click_time >= DATE_SUB(%s, INTERVAL 30 DAY)
            GROUP BY DATE(click_time)
            ORDER BY date ASC
        ", current_time('Y-m-d')));

        ?>
        <div class="wrap">
            <h1><i class="fas fa-chart-line"></i> WhatsApp Widget Analytics</h1>

            <div class="whatsapp-analytics-dashboard">
                <!-- Stats Cards -->
                <div class="whatsapp-stats-grid">
                    <div class="whatsapp-stat-card">
                        <div class="stat-icon"><i class="fas fa-mouse-pointer"></i></div>
                        <div class="stat-content">
                            <h3><?php echo number_format($total_clicks); ?></h3>
                            <p>Total Clicks</p>
                        </div>
                    </div>

                    <div class="whatsapp-stat-card">
                        <div class="stat-icon"><i class="fas fa-calendar-day"></i></div>
                        <div class="stat-content">
                            <h3><?php echo number_format($today_clicks); ?></h3>
                            <p>Today's Clicks</p>
                        </div>
                    </div>

                    <div class="whatsapp-stat-card">
                        <div class="stat-icon"><i class="fas fa-calendar-week"></i></div>
                        <div class="stat-content">
                            <h3><?php echo number_format($this_week_clicks); ?></h3>
                            <p>This Week</p>
                        </div>
                    </div>

                    <div class="whatsapp-stat-card">
                        <div class="stat-icon"><i class="fas fa-calendar-alt"></i></div>
                        <div class="stat-content">
                            <h3><?php echo number_format($this_month_clicks); ?></h3>
                            <p>This Month</p>
                        </div>
                    </div>
                </div>

                <!-- Charts and Tables -->
                <div class="whatsapp-analytics-grid">
                    <div class="whatsapp-analytics-section">
                        <h3><i class="fas fa-chart-area"></i> Daily Clicks (Last 30 Days)</h3>
                        <canvas id="clicksChart" width="400" height="200"></canvas>
                    </div>

                    <div class="whatsapp-analytics-section">
                        <h3><i class="fas fa-trophy"></i> Top Performing Agents</h3>
                        <div class="whatsapp-agents-stats">
                            <?php foreach ($top_agents as $agent): ?>
                            <div class="agent-stat-item">
                                <div class="agent-info">
                                    <strong><?php echo esc_html($agent->agent_name ?: 'Unknown'); ?></strong>
                                    <small><?php echo esc_html($agent->agent_phone); ?></small>
                                </div>
                                <div class="agent-clicks">
                                    <span class="click-count"><?php echo number_format($agent->clicks); ?></span>
                                    <small>clicks</small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="whatsapp-analytics-section">
                    <h3><i class="fas fa-history"></i> Recent Activity</h3>
                    <div class="whatsapp-recent-activity">
                        <table class="wp-list-table widefat fixed striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Agent</th>
                                    <th>Page</th>
                                    <th>IP Address</th>
                                    <th>User Agent</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_clicks as $click): ?>
                                <tr>
                                    <td><?php echo esc_html(date('M j, Y H:i', strtotime($click->click_time))); ?></td>
                                    <td>
                                        <strong><?php echo esc_html($click->agent_name ?: 'Unknown'); ?></strong><br>
                                        <small><?php echo esc_html($click->agent_phone); ?></small>
                                    </td>
                                    <td>
                                        <a href="<?php echo esc_url($click->page_url); ?>" target="_blank">
                                            <?php echo esc_html(parse_url($click->page_url, PHP_URL_PATH) ?: '/'); ?>
                                        </a>
                                    </td>
                                    <td><?php echo esc_html($click->user_ip); ?></td>
                                    <td><?php echo esc_html(substr($click->user_agent, 0, 50)) . (strlen($click->user_agent) > 50 ? '...' : ''); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <style>
        .whatsapp-analytics-dashboard {
            max-width: 1200px;
        }

        .whatsapp-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .whatsapp-stat-card {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #25D366, #128C7E);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .stat-content h3 {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            color: #333;
        }

        .stat-content p {
            margin: 4px 0 0 0;
            color: #666;
            font-size: 14px;
        }

        .whatsapp-analytics-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .whatsapp-analytics-section {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .whatsapp-analytics-section h3 {
            margin: 0 0 20px 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .agent-stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .agent-stat-item:last-child {
            border-bottom: none;
        }

        .click-count {
            font-size: 18px;
            font-weight: 600;
            color: #25D366;
        }

        @media (max-width: 768px) {
            .whatsapp-analytics-grid {
                grid-template-columns: 1fr;
            }
        }
        </style>

        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
        // Create chart
        const ctx = document.getElementById('clicksChart').getContext('2d');
        const chartData = {
            labels: [<?php
                $labels = array();
                foreach ($daily_clicks as $day) {
                    $labels[] = "'" . date('M j', strtotime($day->date)) . "'";
                }
                echo implode(',', $labels);
            ?>],
            datasets: [{
                label: 'Clicks',
                data: [<?php
                    $data = array();
                    foreach ($daily_clicks as $day) {
                        $data[] = $day->clicks;
                    }
                    echo implode(',', $data);
                ?>],
                borderColor: '#25D366',
                backgroundColor: 'rgba(37, 211, 102, 0.1)',
                tension: 0.4,
                fill: true
            }]
        };

        new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        </script>
        <?php
    }
}

// Initialize the plugin
new WhatsAppChatWidget();

// Add shortcode support
add_shortcode('whatsapp_widget', function($atts) {
    $atts = shortcode_atts(array(
        'phone' => get_option('whatsapp_phone', '+923001234567'),
        'message' => get_option('whatsapp_message', 'Hello! I need help.'),
        'size' => get_option('whatsapp_size', '60'),
        'position' => 'static'
    ), $atts);

    ob_start();
    echo '<div class="whatsapp-widget whatsapp-shortcode" style="position: relative; width: ' . esc_attr($atts['size']) . 'px; height: ' . esc_attr($atts['size']) . 'px; display: inline-block;">';
    echo '<a href="https://wa.me/' . esc_attr($atts['phone']) . '?text=' . urlencode($atts['message']) . '" target="_blank">';
    echo '<i class="fab fa-whatsapp"></i>';
    echo '</a>';
    echo '</div>';
    return ob_get_clean();
});
?>