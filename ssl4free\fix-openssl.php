<?php
// Fix OpenSSL configuration for Windows XAMPP

echo "<h1>OpenSSL Configuration Fix</h1>";

// Check if OpenSSL is available
echo "<h2>1. OpenSSL Availability Check</h2>";
if (function_exists('openssl_pkey_new')) {
    echo "✅ OpenSSL PHP extension is available<br>";
} else {
    echo "❌ OpenSSL PHP extension is NOT available<br>";
}

// Check OpenSSL version
echo "<h2>2. OpenSSL Version</h2>";
if (defined('OPENSSL_VERSION_TEXT')) {
    echo "OpenSSL Version: " . OPENSSL_VERSION_TEXT . "<br>";
} else {
    echo "OpenSSL version not available<br>";
}

// Try to find OpenSSL config file
echo "<h2>3. OpenSSL Configuration File</h2>";
$possiblePaths = [
    'C:\xampp\apache\conf\openssl.cnf',
    'C:\xampp\apache\bin\openssl.cnf',
    'C:\xampp\php\extras\openssl\openssl.cnf',
    'C:\Program Files\OpenSSL\bin\openssl.cfg',
    'C:\OpenSSL\bin\openssl.cfg'
];

$configFound = false;
foreach ($possiblePaths as $path) {
    if (file_exists($path)) {
        echo "✅ Found OpenSSL config: $path<br>";
        $configFound = true;
        break;
    } else {
        echo "❌ Not found: $path<br>";
    }
}

if (!$configFound) {
    echo "<br><strong>Creating OpenSSL config file...</strong><br>";
    
    $configContent = '[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = CA
L = SF
O = SSL4Free
CN = example.com

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = example.com
DNS.2 = www.example.com
';
    
    $configPath = 'C:\xampp\apache\conf\openssl.cnf';
    if (file_put_contents($configPath, $configContent)) {
        echo "✅ Created OpenSSL config at: $configPath<br>";
    } else {
        echo "❌ Failed to create OpenSSL config<br>";
    }
}

// Test CSR generation
echo "<h2>4. Test CSR Generation</h2>";

try {
    // Set OpenSSL config environment variable
    putenv('OPENSSL_CONF=C:\xampp\apache\conf\openssl.cnf');
    
    $config = [
        'private_key_bits' => 2048,
        'private_key_type' => OPENSSL_KEYTYPE_RSA,
        'config' => 'C:\xampp\apache\conf\openssl.cnf'
    ];
    
    echo "Attempting to generate private key...<br>";
    $privateKey = openssl_pkey_new($config);
    
    if ($privateKey) {
        echo "✅ Private key generated successfully<br>";
        
        $dn = [
            'countryName' => 'US',
            'stateOrProvinceName' => 'CA',
            'localityName' => 'SF',
            'organizationName' => 'SSL4Free',
            'commonName' => 'test.example.com',
            'emailAddress' => '<EMAIL>'
        ];
        
        echo "Attempting to generate CSR...<br>";
        $csr = openssl_csr_new($dn, $privateKey, $config);
        
        if ($csr) {
            echo "✅ CSR generated successfully<br>";
            
            if (openssl_csr_export($csr, $csrString)) {
                echo "✅ CSR exported successfully<br>";
                echo "<h3>Generated CSR:</h3>";
                echo "<textarea rows='10' cols='80'>" . htmlspecialchars($csrString) . "</textarea><br>";
                
                // Test with ZeroSSL API
                echo "<h2>5. Test with ZeroSSL API</h2>";
                
                require_once 'api/config.php';
                
                $requestData = [
                    'certificate_domains' => 'test.example.com',
                    'certificate_validity_days' => 90,
                    'certificate_csr' => $csrString
                ];
                
                echo "Testing CSR with ZeroSSL...<br>";
                $response = zeroSSLRequest('/certificates', 'POST', $requestData);
                
                if ($response && isset($response['id'])) {
                    echo "✅ ZeroSSL accepted the CSR! Certificate ID: " . $response['id'] . "<br>";
                } else {
                    echo "❌ ZeroSSL rejected the CSR<br>";
                    echo "Response: " . json_encode($response, JSON_PRETTY_PRINT) . "<br>";
                }
                
            } else {
                echo "❌ Failed to export CSR: " . openssl_error_string() . "<br>";
            }
        } else {
            echo "❌ Failed to generate CSR: " . openssl_error_string() . "<br>";
        }
    } else {
        echo "❌ Failed to generate private key: " . openssl_error_string() . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "<br>";
}

echo "<h2>6. Alternative Solution</h2>";
echo "If OpenSSL still doesn't work, we can:<br>";
echo "1. Use an online CSR generator<br>";
echo "2. Install OpenSSL separately<br>";
echo "3. Use a pre-generated valid CSR template<br>";
echo "4. Use a different SSL provider<br>";

echo "<br><a href='debug-certificate.php'>← Back to Debug Page</a>";
?>
