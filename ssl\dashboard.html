<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL For Free - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-shield-alt text-2xl"></i>
                    <h1 class="text-2xl font-bold">SSL For Free</h1>
                </div>
                <nav class="hidden md:flex space-x-6">
                    <a href="#" class="hover:text-blue-200">Dashboard</a>
                    <a href="#" class="hover:text-blue-200">New Certificate</a>
                    <a href="#" class="hover:text-blue-200">Help</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Welcome Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-2">Welcome to SSL For Free</h2>
            <p class="text-gray-600">Generate and manage your free SSL certificates with ease. Secure your websites with trusted encryption.</p>
        </div>

        <!-- Quick Actions -->
        <div class="grid md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 card-hover cursor-pointer" onclick="showNewCertificate()">
                <div class="text-center">
                    <i class="fas fa-plus-circle text-4xl text-green-500 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">New Certificate</h3>
                    <p class="text-gray-600">Generate a new SSL certificate for your domain</p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 card-hover cursor-pointer" onclick="showCertificateList()">
                <div class="text-center">
                    <i class="fas fa-list text-4xl text-blue-500 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">My Certificates</h3>
                    <p class="text-gray-600">View and manage your existing certificates</p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 card-hover cursor-pointer" onclick="showHelp()">
                <div class="text-center">
                    <i class="fas fa-question-circle text-4xl text-purple-500 mb-4"></i>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Help & Support</h3>
                    <p class="text-gray-600">Get help with SSL installation and setup</p>
                </div>
            </div>
        </div>

        <!-- New Certificate Section -->
        <div id="newCertificateSection" class="bg-white rounded-lg shadow-md p-6 mb-8" style="display: none;">
            <h3 class="text-xl font-semibold text-gray-800 mb-6">Generate New SSL Certificate</h3>
            
            <!-- Step 1: Domain Entry -->
            <div id="step1" class="mb-6">
                <h4 class="text-lg font-medium text-gray-700 mb-4">Step 1: Enter Your Domain</h4>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Domain Name</label>
                        <input type="text" id="domainInput" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="example.com">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="emailInput" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>">
                    </div>
                </div>
                <button onclick="validateDomain()" class="mt-4 bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                    <i class="fas fa-check mr-2"></i>Validate Domain
                </button>
            </div>

            <!-- Step 2: Domain Verification -->
            <div id="step2" class="mb-6" style="display: none;">
                <h4 class="text-lg font-medium text-gray-700 mb-4">Step 2: Domain Verification</h4>
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-triangle text-yellow-500 mt-1 mr-3"></i>
                        <div>
                            <h5 class="font-medium text-yellow-800">Domain Verification Required</h5>
                            <p class="text-yellow-700 mt-1">Please verify domain ownership by one of the following methods:</p>
                        </div>
                    </div>
                </div>
                
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="border border-gray-200 rounded-md p-4">
                        <h5 class="font-medium text-gray-800 mb-2">
                            <i class="fas fa-file-alt text-blue-500 mr-2"></i>HTTP File Upload
                        </h5>
                        <p class="text-sm text-gray-600 mb-3">Upload a verification file to your website</p>
                        <button onclick="selectVerificationMethod('http')" class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">
                            Select Method
                        </button>
                    </div>
                    
                    <div class="border border-gray-200 rounded-md p-4">
                        <h5 class="font-medium text-gray-800 mb-2">
                            <i class="fas fa-globe text-green-500 mr-2"></i>DNS Record
                        </h5>
                        <p class="text-sm text-gray-600 mb-3">Add a DNS TXT record to your domain</p>
                        <button onclick="selectVerificationMethod('dns')" class="bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700">
                            Select Method
                        </button>
                    </div>
                </div>
            </div>

            <!-- Step 3: Certificate Generation -->
            <div id="step3" class="mb-6" style="display: none;">
                <h4 class="text-lg font-medium text-gray-700 mb-4">Step 3: Generate Certificate</h4>
                <div id="verificationDetails" class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                    <!-- Verification details will be populated here -->
                </div>
                <button onclick="generateCertificate()" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700">
                    <i class="fas fa-certificate mr-2"></i>Generate SSL Certificate
                </button>
            </div>

            <!-- Step 4: Download Certificate -->
            <div id="step4" class="mb-6" style="display: none;">
                <h4 class="text-lg font-medium text-gray-700 mb-4">Step 4: Download Your Certificate</h4>
                <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                        <div>
                            <h5 class="font-medium text-green-800">Certificate Generated Successfully!</h5>
                            <p class="text-green-700 mt-1">Your SSL certificate has been generated and is ready for download.</p>
                        </div>
                    </div>
                </div>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <button onclick="downloadFile('certificate')" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        <i class="fas fa-download mr-2"></i>Certificate (.crt)
                    </button>
                    <button onclick="downloadFile('privatekey')" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                        <i class="fas fa-download mr-2"></i>Private Key (.key)
                    </button>
                    <button onclick="downloadFile('cabundle')" class="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700">
                        <i class="fas fa-download mr-2"></i>CA Bundle (.ca)
                    </button>
                </div>
            </div>
        </div>

        <!-- Certificate List Section -->
        <div id="certificateListSection" class="bg-white rounded-lg shadow-md p-6" style="display: none;">
            <h3 class="text-xl font-semibold text-gray-800 mb-6">My SSL Certificates</h3>
            
            <div class="overflow-x-auto">
                <table class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">Domain</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">Status</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">Expires</th>
                            <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="certificateTableBody">
                        <!-- Certificate rows will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Help Section -->
        <div id="helpSection" class="bg-white rounded-lg shadow-md p-6" style="display: none;">
            <h3 class="text-xl font-semibold text-gray-800 mb-6">Help & Support</h3>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-medium text-gray-700 mb-3">Installation Guides</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-blue-600 hover:text-blue-800">Apache Installation</a></li>
                        <li><a href="#" class="text-blue-600 hover:text-blue-800">Nginx Installation</a></li>
                        <li><a href="#" class="text-blue-600 hover:text-blue-800">cPanel Installation</a></li>
                        <li><a href="#" class="text-blue-600 hover:text-blue-800">Cloudflare Setup</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-medium text-gray-700 mb-3">Common Issues</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-blue-600 hover:text-blue-800">Domain Verification Failed</a></li>
                        <li><a href="#" class="text-blue-600 hover:text-blue-800">Certificate Not Working</a></li>
                        <li><a href="#" class="text-blue-600 hover:text-blue-800">Mixed Content Errors</a></li>
                        <li><a href="#" class="text-blue-600 hover:text-blue-800">Renewal Issues</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 SSL For Free. All rights reserved. | Powered by Let's Encrypt</p>
        </div>
    </footer>

    <script src="js/dashboard.js"></script>
</body>
</html>
