-- OLX Marketplace Database Schema
-- Create database and tables

CREATE DATABASE IF NOT EXISTS olx_marketplace CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE olx_marketplace;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    avatar VARCHAR(255) DEFAULT NULL,
    location VARCHAR(100),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255) DEFAULT NULL,
    reset_token VARCHAR(255) DEFAULT NULL,
    reset_token_expires DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL DEFAULT NULL
);

-- Categories table
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    icon VARCHAR(50) DEFAULT NULL,
    description TEXT,
    parent_id INT DEFAULT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Products table
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    category_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    price DECIMAL(12,2) NOT NULL,
    condition_type ENUM('new', 'used', 'refurbished') DEFAULT 'used',
    location VARCHAR(100) NOT NULL,
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_sold BOOLEAN DEFAULT FALSE,
    views_count INT DEFAULT 0,
    favorites_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    INDEX idx_category (category_id),
    INDEX idx_location (location),
    INDEX idx_price (price),
    INDEX idx_created (created_at),
    INDEX idx_active (is_active),
    FULLTEXT(title, description)
);

-- Product images table
CREATE TABLE product_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    alt_text VARCHAR(255),
    sort_order INT DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product (product_id)
);

-- Favorites table
CREATE TABLE favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (user_id, product_id),
    INDEX idx_user (user_id),
    INDEX idx_product (product_id)
);

-- Messages table (for buyer-seller communication)
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_product (product_id),
    INDEX idx_sender (sender_id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_created (created_at)
);

-- Conversations table (to group messages)
CREATE TABLE conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    buyer_id INT NOT NULL,
    seller_id INT NOT NULL,
    last_message_id INT DEFAULT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (last_message_id) REFERENCES messages(id) ON DELETE SET NULL,
    UNIQUE KEY unique_conversation (product_id, buyer_id, seller_id),
    INDEX idx_buyer (buyer_id),
    INDEX idx_seller (seller_id)
);

-- Reports table (for reporting inappropriate content)
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reporter_id INT NOT NULL,
    product_id INT DEFAULT NULL,
    user_id INT DEFAULT NULL,
    reason ENUM('spam', 'inappropriate', 'fraud', 'duplicate', 'other') NOT NULL,
    description TEXT,
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (reporter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_created (created_at)
);

-- Search logs table (for analytics)
CREATE TABLE search_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    search_term VARCHAR(255),
    category_id INT DEFAULT NULL,
    location VARCHAR(100),
    results_count INT DEFAULT 0,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_search_term (search_term),
    INDEX idx_created (created_at)
);

-- Site settings table
CREATE TABLE site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Email templates table
CREATE TABLE email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_key VARCHAR(100) UNIQUE NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    variables TEXT COMMENT 'JSON array of available variables',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default categories
INSERT INTO categories (name, slug, icon, description) VALUES
('Mobiles', 'mobiles', 'fas fa-mobile-alt', 'Mobile phones and accessories'),
('Cars', 'cars', 'fas fa-car', 'Cars for sale'),
('Bikes', 'bikes', 'fas fa-motorcycle', 'Motorcycles and bikes'),
('Houses', 'houses', 'fas fa-home', 'Houses and real estate'),
('Electronics', 'electronics', 'fas fa-laptop', 'Electronics and gadgets'),
('Fashion', 'fashion', 'fas fa-tshirt', 'Clothing and fashion'),
('Furniture', 'furniture', 'fas fa-couch', 'Furniture and home decor'),
('Jobs', 'jobs', 'fas fa-briefcase', 'Job opportunities');

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, full_name, is_admin, is_verified, is_active) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', TRUE, TRUE, TRUE);

-- Insert default site settings
INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'OLX Marketplace', 'string', 'Website name'),
('site_description', 'Pakistan\'s largest marketplace for buying and selling', 'string', 'Website description'),
('contact_email', '<EMAIL>', 'string', 'Contact email'),
('products_per_page', '12', 'number', 'Products per page'),
('max_images_per_product', '5', 'number', 'Maximum images per product'),
('require_email_verification', 'true', 'boolean', 'Require email verification for new users'),
('allow_guest_contact', 'false', 'boolean', 'Allow guests to contact sellers'),
('auto_approve_products', 'true', 'boolean', 'Auto approve new products');

-- Insert default email templates
INSERT INTO email_templates (template_key, subject, body, variables) VALUES
('welcome', 'Welcome to OLX Marketplace', 
'<h2>Welcome {{full_name}}!</h2>
<p>Thank you for joining OLX Marketplace. Your account has been created successfully.</p>
<p>You can now start buying and selling products on our platform.</p>
<p><a href="{{site_url}}">Visit our website</a></p>', 
'["full_name", "email", "site_url"]'),

('verification', 'Verify Your Email Address', 
'<h2>Email Verification</h2>
<p>Hi {{full_name}},</p>
<p>Please click the link below to verify your email address:</p>
<p><a href="{{verification_link}}">Verify Email</a></p>
<p>If you did not create this account, please ignore this email.</p>', 
'["full_name", "verification_link"]'),

('password_reset', 'Reset Your Password', 
'<h2>Password Reset Request</h2>
<p>Hi {{full_name}},</p>
<p>You requested to reset your password. Click the link below to reset it:</p>
<p><a href="{{reset_link}}">Reset Password</a></p>
<p>This link will expire in 1 hour.</p>
<p>If you did not request this, please ignore this email.</p>', 
'["full_name", "reset_link"]');

-- Create indexes for better performance
CREATE INDEX idx_products_search ON products(title, location, price, is_active);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_messages_conversation ON messages(product_id, sender_id, receiver_id);

-- Create triggers for updating counters
DELIMITER //

CREATE TRIGGER update_favorites_count_insert 
AFTER INSERT ON favorites 
FOR EACH ROW 
BEGIN 
    UPDATE products SET favorites_count = favorites_count + 1 WHERE id = NEW.product_id;
END//

CREATE TRIGGER update_favorites_count_delete 
AFTER DELETE ON favorites 
FOR EACH ROW 
BEGIN 
    UPDATE products SET favorites_count = favorites_count - 1 WHERE id = OLD.product_id;
END//

DELIMITER ;

-- Sample data for testing
INSERT INTO products (user_id, category_id, title, slug, description, price, condition_type, location, contact_phone, contact_email) VALUES
(1, 1, 'iPhone 14 Pro Max 256GB', 'iphone-14-pro-max-256gb', 'Brand new iPhone 14 Pro Max with 256GB storage. All accessories included.', 350000, 'new', 'Karachi', '03001234567', '<EMAIL>'),
(1, 2, 'Honda Civic 2020 Model', 'honda-civic-2020-model', 'Well maintained Honda Civic 2020 model. Single owner, all documents clear.', 4500000, 'used', 'Lahore', '03009876543', '<EMAIL>'),
(1, 3, 'Yamaha YBR 125 2022', 'yamaha-ybr-125-2022', 'Excellent condition Yamaha YBR 125. Low mileage, well maintained.', 285000, 'used', 'Islamabad', '03001111222', '<EMAIL>');
