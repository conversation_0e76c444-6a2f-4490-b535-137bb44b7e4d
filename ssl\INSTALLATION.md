# SSL Generator Installation Guide

## Quick Setup Instructions

### 1. Server Requirements

- **Web Server**: Apache or Nginx
- **PHP**: Version 7.4 or higher
- **Extensions**: openssl, curl, json, mail
- **HTTPS**: Recommended (but not required for testing)

### 2. Installation Steps

#### Option A: Local Development (XAMPP/WAMP)

1. **Download XAMPP** from https://www.apachefriends.org/
2. **Install XAMPP** and start Apache and MySQL
3. **Copy files** to `C:\xampp\htdocs\ssl\` (Windows) or `/opt/lampp/htdocs/ssl/` (Linux)
4. **Access website** at `http://localhost/ssl/`

#### Option B: Web Hosting

1. **Upload files** to your web hosting account via FTP/cPanel
2. **Extract files** to your domain's public_html directory
3. **Set permissions** (755 for directories, 644 for files)
4. **Access website** at `https://yourdomain.com/`

#### Option C: VPS/Dedicated Server

1. **Install web server** (Apache/Nginx)
2. **Install PHP** with required extensions
3. **Upload files** to web root directory
4. **Configure virtual host** (see configuration examples below)
5. **Set up SSL certificate** for the website itself

### 3. Configuration

#### Apache Virtual Host Example

```apache
<VirtualHost *:80>
    ServerName sslgenerator.yourdomain.com
    DocumentRoot /var/www/ssl
    
    # Redirect to HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</VirtualHost>

<VirtualHost *:443>
    ServerName sslgenerator.yourdomain.com
    DocumentRoot /var/www/ssl
    
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    
    <Directory /var/www/ssl>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

#### Nginx Configuration Example

```nginx
server {
    listen 80;
    server_name sslgenerator.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name sslgenerator.yourdomain.com;
    root /var/www/ssl;
    index index.html;
    
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    location / {
        try_files $uri $uri/ =404;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location ~ /\.ht {
        deny all;
    }
}
```

### 4. PHP Configuration

#### Required Extensions

Ensure these PHP extensions are enabled in `php.ini`:

```ini
extension=openssl
extension=curl
extension=json
extension=mbstring
```

#### Email Configuration

For email functionality, configure SMTP in `php.ini`:

```ini
[mail function]
SMTP = your-smtp-server.com
smtp_port = 587
sendmail_from = <EMAIL>
```

### 5. Testing

1. **Access the website** in your browser
2. **Test SSL generation** with a domain you own
3. **Verify email delivery** (check spam folder)
4. **Test on mobile devices** for responsiveness
5. **Check browser console** for any JavaScript errors

### 6. Security Hardening

#### File Permissions

```bash
# Set directory permissions
find /var/www/ssl -type d -exec chmod 755 {} \;

# Set file permissions
find /var/www/ssl -type f -exec chmod 644 {} \;

# Make PHP files executable
chmod 755 /var/www/ssl/api/*.php
```

#### Additional Security

1. **Enable firewall** and close unnecessary ports
2. **Keep software updated** (PHP, web server, OS)
3. **Use strong passwords** for server access
4. **Enable fail2ban** to prevent brute force attacks
5. **Regular backups** of website files and data

### 7. Customization

#### Branding

1. **Replace logo**: Update the shield icon in navigation
2. **Change colors**: Modify CSS color variables
3. **Update content**: Edit text in HTML files
4. **Add analytics**: Insert Google Analytics code

#### Domain Configuration

1. **Update contact email**: Change email addresses in PHP files
2. **Set domain restrictions**: Add domain validation rules
3. **Configure rate limiting**: Implement API rate limits

### 8. Troubleshooting

#### Common Issues

**"Permission denied" errors**
```bash
sudo chown -R www-data:www-data /var/www/ssl
sudo chmod -R 755 /var/www/ssl
```

**PHP errors not showing**
```ini
; In php.ini
display_errors = On
error_reporting = E_ALL
log_errors = On
error_log = /var/log/php_errors.log
```

**Email not working**
- Check SMTP configuration
- Verify firewall allows SMTP ports
- Test with a simple PHP mail script

**SSL generation fails**
- Ensure domain is accessible from internet
- Check DNS settings
- Verify domain ownership

#### Log Files

Monitor these log files for issues:
- Apache: `/var/log/apache2/error.log`
- Nginx: `/var/log/nginx/error.log`
- PHP: `/var/log/php_errors.log`
- SSL Generator: `ssl_generation.log` (created by the app)

### 9. Production Deployment

#### Performance Optimization

1. **Enable caching**: Use Redis or Memcached
2. **CDN setup**: Use CloudFlare or similar
3. **Image optimization**: Compress images
4. **Minify assets**: Minify CSS and JavaScript

#### Monitoring

1. **Uptime monitoring**: Use Pingdom or UptimeRobot
2. **Error tracking**: Implement error logging
3. **Performance monitoring**: Use New Relic or similar
4. **Security scanning**: Regular vulnerability scans

### 10. Support

If you encounter issues:

1. **Check logs** for error messages
2. **Verify configuration** against this guide
3. **Test with minimal setup** to isolate issues
4. **Search documentation** for similar problems
5. **Contact support** with detailed error information

---

**Note**: This installation guide covers basic setup. For production environments, additional security measures and optimizations may be required.
