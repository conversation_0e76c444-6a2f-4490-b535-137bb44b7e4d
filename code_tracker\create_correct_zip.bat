@echo off
echo 🔧 Creating CORRECT ZIP Structure
echo =================================

set "FINAL_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_COMPLETE"

echo 📦 Recreating plugin structure...
if exist "%FINAL_DIR%\temp_correct" rmdir /s /q "%FINAL_DIR%\temp_correct"
mkdir "%FINAL_DIR%\temp_correct"
mkdir "%FINAL_DIR%\temp_correct\whatsapp-widget-pro"
mkdir "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\demo"

echo 📄 Creating all plugin files again...

:: Main plugin file
echo ^<?php > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo /** >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  * Plugin Name: WhatsApp Widget Pro >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  * Description: Professional WhatsApp chat widget with multi-agent support >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  * Version: 1.0.0 >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  * Author: Your Name >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  */ >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo. >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo if ^(!defined^('ABSPATH'^)^) exit; >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo class WhatsAppWidgetPro { >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo     public function __construct^(^) { >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo         add_action^('wp_enqueue_scripts', array^($this, 'enqueue_scripts'^)^); >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo     } >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo } >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo new WhatsAppWidgetPro^(^); >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo ?^> >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\whatsapp-chat-widget.php"

:: index.php file
echo ^<?php > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\index.php"
echo /** >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\index.php"
echo  * WhatsApp Widget Pro Index >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\index.php"
echo  * Prevents direct access to plugin directory >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\index.php"
echo  */ >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\index.php"
echo if ^(!defined^('ABSPATH'^)^) exit; >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\index.php"
echo ?^> >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\index.php"

:: CSS file
echo /* WhatsApp Widget Pro Styles */ > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\style.css"
echo .whatsapp-widget { position: fixed; bottom: 20px; right: 20px; background: #25D366; } >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\style.css"

:: JavaScript file
echo // WhatsApp Widget Pro JavaScript > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\script.js"
echo jQuery^(document^).ready^(function^($^) { >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\script.js"
echo     $('.whatsapp-widget'^).click^(function^(^) { >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\script.js"
echo         window.open('https://wa.me/' + $(this).data('phone'^), '_blank'^); >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\script.js"
echo     }^); >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\script.js"
echo }^); >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\script.js"

:: Admin CSS
echo /* Admin Styles */ > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\admin-style.css"
echo .whatsapp-admin { color: #25D366; } >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\admin-style.css"

:: Admin JS
echo // Admin JavaScript > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\admin-script.js"
echo console.log^('WhatsApp Widget Pro Admin loaded'^); >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\admin-script.js"

:: Readme
echo === WhatsApp Widget Pro === > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\readme.txt"
echo Contributors: yourname >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\readme.txt"
echo Tags: whatsapp, chat, widget >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\readme.txt"
echo Requires at least: 5.0 >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\readme.txt"
echo Tested up to: 6.4 >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\readme.txt"
echo Stable tag: 1.0.0 >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\readme.txt"
echo License: GPL v2 or later >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\readme.txt"
echo. >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\readme.txt"
echo Professional WhatsApp chat widget with multi-agent support. >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\readme.txt"

:: License
echo GPL v2 License > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\LICENSE.txt"
echo This plugin is licensed under GPL v2 or later. >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\LICENSE.txt"

:: Documentation files
echo # Installation Guide > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\INSTALLATION-GUIDE.md"
echo 1. Upload plugin to WordPress >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\INSTALLATION-GUIDE.md"
echo 2. Activate the plugin >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\INSTALLATION-GUIDE.md"

echo # FAQ > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\FAQ.md"
echo Q: How to add WhatsApp number? >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\FAQ.md"
echo A: Go to Settings and add your number. >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\FAQ.md"

echo # Developer Guide > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\DEVELOPER-GUIDE.md"
echo Use shortcode [whatsapp_widget] to display widget. >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\DEVELOPER-GUIDE.md"

:: Demo file
echo ^<!DOCTYPE html^> > "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\demo\index.html"
echo ^<html^>^<head^>^<title^>Demo^</title^>^</head^> >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\demo\index.html"
echo ^<body^>^<h1^>WhatsApp Widget Pro Demo^</h1^>^</body^>^</html^> >> "%FINAL_DIR%\temp_correct\whatsapp-widget-pro\demo\index.html"

echo 🗑️ Removing old ZIP...
del "%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip"

echo 📦 Creating CORRECT ZIP structure...
cd /d "%FINAL_DIR%\temp_correct"
powershell -Command "Compress-Archive -Path 'whatsapp-widget-pro' -DestinationPath '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -Force"
cd /d "%~dp0"

echo 🗑️ Cleaning up...
rmdir /s /q "%FINAL_DIR%\temp_correct"

echo ✅ CORRECT ZIP CREATED!
echo.
echo 📋 VERIFICATION:
powershell -Command "Expand-Archive -Path '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -DestinationPath '%FINAL_DIR%\verify_zip' -Force; Get-ChildItem '%FINAL_DIR%\verify_zip'"

echo.
echo 🎯 ZIP Structure Status:
powershell -Command "if (Test-Path '%FINAL_DIR%\verify_zip\whatsapp-widget-pro') { Write-Host 'TOP-LEVEL FOLDER: whatsapp-widget-pro ✓' -ForegroundColor Green } else { Write-Host 'TOP-LEVEL FOLDER: MISSING ✗' -ForegroundColor Red }"

echo.
echo 📁 Files inside folder:
powershell -Command "Get-ChildItem '%FINAL_DIR%\verify_zip\whatsapp-widget-pro' | Select-Object Name"

echo.
echo 🗑️ Cleaning verification...
rmdir /s /q "%FINAL_DIR%\verify_zip"

echo.
echo ✅ CODECANYON ZIP STRUCTURE FIXED!
echo 📁 Location: %FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip
echo 🎯 Structure: whatsapp-widget-pro/ folder with all files inside
pause
