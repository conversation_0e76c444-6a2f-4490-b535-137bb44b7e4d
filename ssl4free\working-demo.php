<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL4Free - Working Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-certificate text-green-600 mr-2"></i>
                SSL4Free - Working Demo with Valid CSR
            </h1>
            
            <?php
            require_once 'api/config.php';
            
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $domain = trim($_POST['domain']);
                $email = trim($_POST['email']);
                
                echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>";
                echo "<h2 class='text-xl font-semibold mb-4'>Creating SSL Certificate for: $domain</h2>";
                
                // Use a real, valid CSR that ZeroSSL will accept
                $validCSR = getValidCSR($domain);
                
                if ($validCSR) {
                    echo "<div class='bg-green-50 border border-green-200 rounded p-4 mb-4'>";
                    echo "<h3 class='font-semibold text-green-800'>✅ Step 1: Valid CSR Ready</h3>";
                    echo "<p class='text-green-700'>Using a properly formatted CSR for domain: $domain</p>";
                    echo "</div>";
                    
                    // Create certificate with ZeroSSL
                    $requestData = [
                        'certificate_domains' => $domain,
                        'certificate_validity_days' => 90,
                        'certificate_csr' => $validCSR
                    ];
                    
                    echo "<div class='bg-blue-50 border border-blue-200 rounded p-4 mb-4'>";
                    echo "<h3 class='font-semibold text-blue-800'>🔄 Step 2: Creating Certificate with ZeroSSL...</h3>";
                    echo "</div>";
                    
                    $response = zeroSSLRequest('/certificates', 'POST', $requestData);
                    
                    if ($response && isset($response['id'])) {
                        echo "<div class='bg-green-50 border border-green-200 rounded p-4 mb-4'>";
                        echo "<h3 class='font-semibold text-green-800'>🎉 Step 3: Certificate Created Successfully!</h3>";
                        echo "<p class='text-green-700'>Certificate ID: <code class='bg-gray-100 px-2 py-1 rounded'>" . $response['id'] . "</code></p>";
                        echo "<p class='text-green-700'>Status: " . ($response['status'] ?? 'draft') . "</p>";
                        echo "<p class='text-green-700'>Common Name: " . ($response['common_name'] ?? $domain) . "</p>";
                        echo "</div>";
                        
                        // Show verification instructions
                        if (isset($response['validation']['other_methods'][$domain])) {
                            $validation = $response['validation']['other_methods'][$domain];
                            
                            echo "<div class='bg-yellow-50 border border-yellow-200 rounded p-4 mb-4'>";
                            echo "<h3 class='font-semibold text-yellow-800'>📋 Step 4: Domain Verification Required</h3>";
                            
                            echo "<div class='mt-4'>";
                            echo "<h4 class='font-medium mb-2'>Choose Verification Method:</h4>";
                            
                            // HTTP File Verification
                            if (isset($validation['file_validation_url_http'])) {
                                echo "<div class='border border-gray-200 rounded p-3 mb-3'>";
                                echo "<h5 class='font-medium text-blue-800 mb-2'>Option 1: HTTP File Verification</h5>";
                                echo "<p class='text-sm mb-2'>1. Create this file on your website:</p>";
                                echo "<code class='bg-gray-100 px-2 py-1 rounded block mb-2 text-xs'>" . $validation['file_validation_url_http'] . "</code>";
                                echo "<p class='text-sm mb-2'>2. File content (copy exactly):</p>";
                                echo "<textarea class='w-full p-2 border rounded text-xs font-mono' rows='3' readonly>" . implode("\n", $validation['file_validation_content']) . "</textarea>";
                                echo "<button onclick='copyFileContent(this)' class='mt-2 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700'>";
                                echo "<i class='fas fa-copy mr-1'></i>Copy Content";
                                echo "</button>";
                                echo "</div>";
                            }
                            
                            // DNS CNAME Verification
                            if (isset($validation['cname_validation_p1'])) {
                                echo "<div class='border border-gray-200 rounded p-3 mb-3'>";
                                echo "<h5 class='font-medium text-green-800 mb-2'>Option 2: DNS CNAME Verification</h5>";
                                echo "<p class='text-sm mb-2'>Add this CNAME record to your DNS:</p>";
                                echo "<div class='bg-gray-100 p-2 rounded text-sm'>";
                                echo "<p><strong>Name:</strong> <code>" . $validation['cname_validation_p1'] . "</code></p>";
                                echo "<p><strong>Value:</strong> <code>" . $validation['cname_validation_p2'] . "</code></p>";
                                echo "</div>";
                                echo "<button onclick='copyCNAME(this)' class='mt-2 bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700'>";
                                echo "<i class='fas fa-copy mr-1'></i>Copy CNAME";
                                echo "</button>";
                                echo "</div>";
                            }
                            
                            echo "</div>";
                            
                            echo "<div class='bg-blue-50 border border-blue-200 rounded p-4 mt-4'>";
                            echo "<h3 class='font-semibold text-blue-800'>🚀 Next Steps:</h3>";
                            echo "<ol class='list-decimal list-inside text-blue-700 space-y-1 text-sm'>";
                            echo "<li>Complete domain verification (choose one method above)</li>";
                            echo "<li>Wait 5-10 minutes for ZeroSSL to verify</li>";
                            echo "<li>Certificate will be automatically issued</li>";
                            echo "<li>Check certificate status and download</li>";
                            echo "</ol>";
                            
                            echo "<div class='mt-4 space-x-2'>";
                            echo "<button onclick='checkStatus(\"" . $response['id'] . "\")' class='bg-purple-600 text-white px-4 py-2 rounded text-sm hover:bg-purple-700'>";
                            echo "<i class='fas fa-sync mr-2'></i>Check Status";
                            echo "</button>";
                            
                            echo "<button onclick='downloadCert(\"" . $response['id'] . "\")' class='bg-green-600 text-white px-4 py-2 rounded text-sm hover:bg-green-700'>";
                            echo "<i class='fas fa-download mr-2'></i>Download Certificate";
                            echo "</button>";
                            echo "</div>";
                            
                            echo "</div>";
                        }
                        
                    } else {
                        echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
                        echo "<h3 class='font-semibold text-red-800'>❌ Certificate Creation Failed</h3>";
                        if (isset($response['error'])) {
                            echo "<p class='text-red-700'>Error: " . $response['error']['type'] . "</p>";
                            echo "<p class='text-red-700'>Code: " . $response['error']['code'] . "</p>";
                        }
                        echo "<pre class='text-xs mt-2 bg-gray-100 p-2 rounded overflow-x-auto'>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
                        echo "</div>";
                    }
                    
                } else {
                    echo "<div class='bg-red-50 border border-red-200 rounded p-4'>";
                    echo "<h3 class='font-semibold text-red-800'>❌ CSR Generation Failed</h3>";
                    echo "<p class='text-red-700'>Unable to generate a valid CSR. Please use the manual CSR generator.</p>";
                    echo "</div>";
                }
                
                echo "</div>";
            }
            
            // Function to get a valid CSR (using online generator or manual input)
            function getValidCSR($domain) {
                // For demo purposes, we'll show how to use a manual CSR
                // In production, you would integrate with a proper CSR generator
                
                if (isset($_POST['manual_csr']) && !empty($_POST['manual_csr'])) {
                    return trim($_POST['manual_csr']);
                }
                
                // Try to generate a simple CSR
                try {
                    $config = [
                        'private_key_bits' => 2048,
                        'private_key_type' => OPENSSL_KEYTYPE_RSA,
                    ];
                    
                    $privateKey = @openssl_pkey_new($config);
                    
                    if ($privateKey) {
                        $dn = [
                            'C' => 'US',
                            'ST' => 'CA',
                            'L' => 'SF',
                            'O' => 'SSL4Free',
                            'CN' => $domain
                        ];
                        
                        $csr = @openssl_csr_new($dn, $privateKey, $config);
                        
                        if ($csr && @openssl_csr_export($csr, $csrString)) {
                            return $csrString;
                        }
                    }
                } catch (Exception $e) {
                    // Fall through to return false
                }
                
                return false;
            }
            ?>
            
            <!-- Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Generate SSL Certificate</h2>
                
                <div class="bg-blue-50 border border-blue-200 rounded p-4 mb-6">
                    <h3 class="font-semibold text-blue-800 mb-2">💡 Working Demo</h3>
                    <p class="text-blue-700 text-sm">This demo uses a valid CSR generation method that works with ZeroSSL API.</p>
                </div>
                
                <form method="POST" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Domain Name
                        </label>
                        <input type="text" name="domain" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="example.com"
                               value="<?php echo isset($_POST['domain']) ? htmlspecialchars($_POST['domain']) : ''; ?>">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" name="email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="<EMAIL>"
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Manual CSR (Optional)
                        </label>
                        <textarea name="manual_csr" rows="8"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-xs"
                                  placeholder="-----BEGIN CERTIFICATE REQUEST-----
Paste your CSR here if you have one...
-----END CERTIFICATE REQUEST-----"><?php echo isset($_POST['manual_csr']) ? htmlspecialchars($_POST['manual_csr']) : ''; ?></textarea>
                        <p class="text-sm text-gray-600 mt-1">
                            Generate CSR at: <a href="https://www.ssl.com/online-csr-generator/" target="_blank" class="text-blue-600 underline">SSL.com CSR Generator</a>
                        </p>
                    </div>
                    
                    <button type="submit"
                            class="w-full bg-green-600 text-white py-3 px-6 rounded-lg text-lg font-semibold hover:bg-green-700 transition-colors">
                        <i class="fas fa-certificate mr-2"></i>
                        Generate SSL Certificate
                    </button>
                </form>
                
                <div class="mt-6 pt-4 border-t border-gray-200 space-y-2">
                    <a href="generate-csr.php" class="block text-blue-600 hover:text-blue-800">
                        <i class="fas fa-key mr-2"></i>
                        CSR Generator Tool
                    </a>
                    <a href="index.html" class="block text-gray-600 hover:text-gray-800">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to SSL4Free
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function copyFileContent(button) {
            const textarea = button.parentElement.querySelector('textarea');
            textarea.select();
            document.execCommand('copy');
            button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-copy mr-1"></i>Copy Content';
            }, 2000);
        }
        
        function copyCNAME(button) {
            const div = button.parentElement.querySelector('.bg-gray-100');
            const text = div.innerText;
            navigator.clipboard.writeText(text);
            button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-copy mr-1"></i>Copy CNAME';
            }, 2000);
        }
        
        function checkStatus(certificateId) {
            alert('Certificate ID: ' + certificateId + '\nUse the certificate status API to check progress.');
        }
        
        function downloadCert(certificateId) {
            alert('Certificate ID: ' + certificateId + '\nUse the download API when certificate status is "issued".');
        }
    </script>
</body>
</html>
