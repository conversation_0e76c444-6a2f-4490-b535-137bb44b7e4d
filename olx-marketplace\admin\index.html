<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - OLX Marketplace</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #002f34;
            color: white;
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid #444;
            margin-bottom: 20px;
        }

        .sidebar-header h2 {
            color: #23e5db;
            font-size: 18px;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: #ccc;
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #23e5db;
            color: #002f34;
        }

        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #002f34;
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logout-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }

        .logout-btn:hover {
            background: #d32f2f;
        }

        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .card-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .card-icon.users { color: #4CAF50; }
        .card-icon.products { color: #2196F3; }
        .card-icon.orders { color: #FF9800; }
        .card-icon.revenue { color: #9C27B0; }

        .card-number {
            font-size: 32px;
            font-weight: bold;
            color: #002f34;
            margin-bottom: 10px;
        }

        .card-label {
            color: #666;
            font-size: 16px;
        }

        .content-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .section-title {
            color: #002f34;
            font-size: 20px;
        }

        .btn {
            background: #002f34;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
        }

        .btn:hover {
            background: #001a1d;
        }

        .btn-danger {
            background: #f44336;
        }

        .btn-danger:hover {
            background: #d32f2f;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #002f34;
        }

        .table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-active {
            background: #4CAF50;
            color: white;
        }

        .status-inactive {
            background: #f44336;
            color: white;
        }

        .status-pending {
            background: #FF9800;
            color: white;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #002f34;
        }

        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            position: relative;
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }

        .close:hover {
            color: #333;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading i {
            font-size: 24px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .dashboard-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> OLX Admin</h2>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#" class="menu-link active" data-section="dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="#" class="menu-link" data-section="users"><i class="fas fa-users"></i> Users</a></li>
                <li><a href="#" class="menu-link" data-section="products"><i class="fas fa-box"></i> Products</a></li>
                <li><a href="#" class="menu-link" data-section="categories"><i class="fas fa-tags"></i> Categories</a></li>
                <li><a href="#" class="menu-link" data-section="reports"><i class="fas fa-flag"></i> Reports</a></li>
                <li><a href="#" class="menu-link" data-section="settings"><i class="fas fa-cog"></i> Settings</a></li>
                <li><a href="#" class="menu-link" data-section="analytics"><i class="fas fa-chart-bar"></i> Analytics</a></li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1 id="pageTitle">Dashboard</h1>
                <div class="user-info">
                    <span id="adminName">Admin User</span>
                    <a href="#" class="logout-btn" onclick="logout()"><i class="fas fa-sign-out-alt"></i> Logout</a>
                </div>
            </div>

            <!-- Dashboard Section -->
            <div id="dashboard" class="content-section active">
                <div class="dashboard-cards">
                    <div class="card">
                        <div class="card-icon users"><i class="fas fa-users"></i></div>
                        <div class="card-number" id="totalUsers">0</div>
                        <div class="card-label">Total Users</div>
                    </div>
                    <div class="card">
                        <div class="card-icon products"><i class="fas fa-box"></i></div>
                        <div class="card-number" id="totalProducts">0</div>
                        <div class="card-label">Total Products</div>
                    </div>
                    <div class="card">
                        <div class="card-icon orders"><i class="fas fa-shopping-cart"></i></div>
                        <div class="card-number" id="totalOrders">0</div>
                        <div class="card-label">Total Orders</div>
                    </div>
                    <div class="card">
                        <div class="card-icon revenue"><i class="fas fa-dollar-sign"></i></div>
                        <div class="card-number" id="totalRevenue">Rs 0</div>
                        <div class="card-label">Total Revenue</div>
                    </div>
                </div>

                <div class="content-section active">
                    <div class="section-header">
                        <h2 class="section-title">Recent Activities</h2>
                    </div>
                    <div id="recentActivities">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Loading recent activities...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Section -->
            <div id="users" class="content-section">
                <div class="section-header">
                    <h2 class="section-title">Users Management</h2>
                    <button class="btn" onclick="showAddUserModal()">Add New User</button>
                </div>
                <div id="usersTable">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading users...</p>
                    </div>
                </div>
            </div>

            <!-- Products Section -->
            <div id="products" class="content-section">
                <div class="section-header">
                    <h2 class="section-title">Products Management</h2>
                    <button class="btn" onclick="showAddProductModal()">Add New Product</button>
                </div>
                <div id="productsTable">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading products...</p>
                    </div>
                </div>
            </div>

            <!-- Categories Section -->
            <div id="categories" class="content-section">
                <div class="section-header">
                    <h2 class="section-title">Categories Management</h2>
                    <button class="btn" onclick="showAddCategoryModal()">Add New Category</button>
                </div>
                <div id="categoriesTable">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading categories...</p>
                    </div>
                </div>
            </div>

            <!-- Reports Section -->
            <div id="reports" class="content-section">
                <div class="section-header">
                    <h2 class="section-title">Reports & Complaints</h2>
                </div>
                <div id="reportsTable">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading reports...</p>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings" class="content-section">
                <div class="section-header">
                    <h2 class="section-title">Site Settings</h2>
                    <button class="btn" onclick="saveSettings()">Save Settings</button>
                </div>
                <div id="settingsForm">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading settings...</p>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics" class="content-section">
                <div class="section-header">
                    <h2 class="section-title">Analytics & Statistics</h2>
                </div>
                <div id="analyticsContent">
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Loading analytics...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <script src="../js/main.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
