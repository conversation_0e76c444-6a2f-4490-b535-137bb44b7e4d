@echo off
echo 🔧 Adding index.php to WordPress ZIP
echo ==================================

set "FINAL_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_COMPLETE"

echo 📦 Extracting current ZIP...
powershell -Command "Expand-Archive -Path '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -DestinationPath '%FINAL_DIR%\temp_extract' -Force"

echo 📄 Creating index.php file...
echo ^<?php > "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo /** >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * WhatsApp Widget Pro >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * This file prevents direct access to the plugin directory >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * and serves as the main entry point for WordPress. >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * @package WhatsApp_Widget_Pro >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * @version 1.0.0 >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  */ >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo. >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo // Prevent direct access >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo if ^(!defined^('ABSPATH'^)^) { >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     exit; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo } >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo. >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo /** >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * WhatsApp Widget Pro Main Index >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * This file serves as the main entry point for the plugin >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  * and handles initialization when accessed directly. >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo  */ >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo. >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo // Plugin information >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo $plugin_info = array^( >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     'name' =^> 'WhatsApp Widget Pro', >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     'version' =^> '1.0.0', >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     'description' =^> 'Professional WhatsApp chat widget with multi-agent support', >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     'author' =^> 'Your Name', >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     'url' =^> 'https://yourwebsite.com' >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo ^); >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo. >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo // Include main plugin file if WordPress is loaded >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo if ^(defined^('WPINC'^)^) { >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     require_once plugin_dir_path^(__FILE__^) . 'whatsapp-chat-widget.php'; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo } >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo. >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo // Display plugin info if accessed directly ^(for debugging^) >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo if ^(!defined^('WPINC'^) ^&^& !headers_sent^(^)^) { >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     header^('Content-Type: text/html; charset=utf-8'^); >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     echo '^<!DOCTYPE html^>'; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     echo '^<html^>^<head^>^<title^>' . $plugin_info['name'] . '^</title^>^</head^>'; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     echo '^<body style="font-family: Arial, sans-serif; padding: 20px;"^>'; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     echo '^<h1^>' . $plugin_info['name'] . '^</h1^>'; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     echo '^<p^>Version: ' . $plugin_info['version'] . '^</p^>'; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     echo '^<p^>' . $plugin_info['description'] . '^</p^>'; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     echo '^<p^>This is a WordPress plugin. Please install it through WordPress admin.^</p^>'; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     echo '^</body^>^</html^>'; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo     exit; >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo } >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo. >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"
echo ?^> >> "%FINAL_DIR%\temp_extract\whatsapp-widget-pro\index.php"

echo 📦 Creating new ZIP with index.php...
del "%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip"
powershell -Command "Compress-Archive -Path '%FINAL_DIR%\temp_extract\whatsapp-widget-pro' -DestinationPath '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -Force"

echo 🗑️ Cleaning up...
rmdir /s /q "%FINAL_DIR%\temp_extract"

echo ✅ index.php ADDED TO ZIP!
echo.
echo 📋 WHAT'S BEEN FIXED:
echo    ✅ index.php file added to whatsapp-widget-pro folder
echo    ✅ WordPress ZIP structure now complete
echo    ✅ CodeCanyon requirement satisfied
echo.
echo 📦 ZIP now contains:
echo    📁 whatsapp-widget-pro/
echo    ├── index.php ✅ ^(NEW - REQUIRED^)
echo    ├── whatsapp-chat-widget.php
echo    ├── style.css
echo    ├── script.js
echo    ├── admin-style.css
echo    ├── admin-script.js
echo    ├── readme.txt
echo    ├── LICENSE.txt
echo    ├── INSTALLATION-GUIDE.md
echo    ├── FAQ.md
echo    ├── DEVELOPER-GUIDE.md
echo    └── demo/
echo        └── index.html
echo.
echo 🎯 WordPress ZIP issue COMPLETELY SOLVED!
echo 📁 Location: %FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip
pause
