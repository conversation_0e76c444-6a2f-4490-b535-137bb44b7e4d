<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Widget Pro - Live Demo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../style.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }
        
        .demo-header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .demo-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .demo-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #25D366, #128C7E);
        }
        
        .demo-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .demo-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .demo-preview {
            height: 200px;
            background: #f8f9fa;
            border-radius: 10px;
            position: relative;
            margin: 20px 0;
            border: 2px dashed #ddd;
        }
        
        .theme-selector {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .theme-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: #f0f0f0;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .theme-btn.active {
            background: #25D366;
            color: white;
        }
        
        .features-list {
            text-align: left;
            margin: 20px 0;
        }
        
        .features-list li {
            padding: 5px 0;
            color: #555;
        }
        
        .features-list i {
            color: #25D366;
            margin-right: 8px;
        }
        
        .cta-section {
            text-align: center;
            background: white;
            padding: 50px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #25D366, #128C7E);
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
        }
        
        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fab fa-whatsapp"></i> WhatsApp Widget Pro</h1>
            <p>Experience the most advanced WhatsApp integration for WordPress. Try different themes and features in this live demo.</p>
        </div>
        
        <div class="demo-grid">
            <div class="demo-card">
                <h3><i class="fas fa-palette"></i> Theme Showcase</h3>
                <p>Choose from 6 professional themes designed to match any website style.</p>
                
                <div class="theme-selector">
                    <button class="theme-btn active" data-theme="default">Default</button>
                    <button class="theme-btn" data-theme="minimal">Minimal</button>
                    <button class="theme-btn" data-theme="dark">Dark</button>
                    <button class="theme-btn" data-theme="gradient">Gradient</button>
                    <button class="theme-btn" data-theme="square">Square</button>
                    <button class="theme-btn" data-theme="pulse">Pulse</button>
                </div>
                
                <div class="demo-preview" id="theme-preview">
                    <!-- Widget will be inserted here -->
                </div>
            </div>
            
            <div class="demo-card">
                <h3><i class="fas fa-users"></i> Multiple Agents</h3>
                <p>Support multiple WhatsApp agents with individual profiles and contact information.</p>
                
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> Unlimited agents</li>
                    <li><i class="fas fa-check"></i> Agent profiles with avatars</li>
                    <li><i class="fas fa-check"></i> Department-based routing</li>
                    <li><i class="fas fa-check"></i> Individual enable/disable</li>
                </ul>
                
                <div class="demo-preview">
                    <div class="whatsapp-bubble-widget bottom-right active">
                        <div class="bubble-content" style="position: relative; opacity: 1; visibility: visible; transform: none; bottom: 0;">
                            <div class="bubble-header">
                                <h4>Need Help?</h4>
                                <p>Choose an agent to chat with</p>
                            </div>
                            <div class="bubble-agents">
                                <div class="agent-item">
                                    <div class="agent-avatar"><i class="fas fa-user"></i></div>
                                    <div class="agent-info">
                                        <h5>John Doe</h5>
                                        <p>Sales Manager</p>
                                    </div>
                                    <a href="#" class="agent-chat-btn">Chat</a>
                                </div>
                                <div class="agent-item">
                                    <div class="agent-avatar"><i class="fas fa-user"></i></div>
                                    <div class="agent-info">
                                        <h5>Jane Smith</h5>
                                        <p>Support Agent</p>
                                    </div>
                                    <a href="#" class="agent-chat-btn">Chat</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="demo-card">
                <h3><i class="fas fa-chart-line"></i> Analytics Dashboard</h3>
                <p>Track performance with comprehensive analytics and reporting features.</p>
                
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> Real-time click tracking</li>
                    <li><i class="fas fa-check"></i> Agent performance metrics</li>
                    <li><i class="fas fa-check"></i> Visual charts and graphs</li>
                    <li><i class="fas fa-check"></i> Export capabilities</li>
                </ul>
                
                <div class="demo-preview" style="background: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                    <div style="text-align: center; color: #666;">
                        <i class="fas fa-chart-area" style="font-size: 3rem; margin-bottom: 10px; color: #25D366;"></i>
                        <p>Analytics Dashboard Preview</p>
                    </div>
                </div>
            </div>
            
            <div class="demo-card">
                <h3><i class="fas fa-clock"></i> Working Hours</h3>
                <p>Set business hours with timezone support and automatic online/offline status.</p>
                
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> Weekly schedule setup</li>
                    <li><i class="fas fa-check"></i> Timezone support</li>
                    <li><i class="fas fa-check"></i> Custom offline messages</li>
                    <li><i class="fas fa-check"></i> Automatic status indicators</li>
                </ul>
                
                <div class="demo-preview">
                    <div class="whatsapp-widget bottom-right widget-loaded" style="width: 60px; height: 60px;">
                        <a href="#" onclick="return false;">
                            <i class="fab fa-whatsapp"></i>
                            <span class="online-indicator"></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="cta-section">
            <h2>Ready to Transform Your Customer Support?</h2>
            <p>Get WhatsApp Widget Pro today and start providing instant customer support through WhatsApp.</p>
            
            <a href="#" class="cta-button">
                <i class="fas fa-download"></i> Download Now
            </a>
            <a href="#" class="cta-button" style="background: #333;">
                <i class="fas fa-eye"></i> View Documentation
            </a>
        </div>
    </div>
    
    <!-- Demo Widget -->
    <div class="whatsapp-widget whatsapp-theme-default bottom-right widget-loaded" id="demo-widget" style="width: 60px; height: 60px;">
        <a href="https://wa.me/+923001234567?text=Hello! I'm interested in WhatsApp Widget Pro" target="_blank">
            <i class="fab fa-whatsapp"></i>
            <span class="online-indicator"></span>
        </a>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Theme switching
            $('.theme-btn').on('click', function() {
                const theme = $(this).data('theme');
                
                $('.theme-btn').removeClass('active');
                $(this).addClass('active');
                
                // Update demo widget theme
                $('#demo-widget').removeClass().addClass('whatsapp-widget whatsapp-theme-' + theme + ' bottom-right widget-loaded');
                
                // Update preview
                const previewHtml = `
                    <div class="whatsapp-widget whatsapp-theme-${theme} bottom-right widget-loaded" style="width: 60px; height: 60px;">
                        <a href="#" onclick="return false;">
                            <i class="fab fa-whatsapp"></i>
                            <span class="online-indicator"></span>
                        </a>
                    </div>
                `;
                $('#theme-preview').html(previewHtml);
            });
            
            // Initialize preview
            $('.theme-btn.active').trigger('click');
            
            // Animate elements on scroll
            $(window).on('scroll', function() {
                $('.demo-card').each(function() {
                    const elementTop = $(this).offset().top;
                    const elementBottom = elementTop + $(this).outerHeight();
                    const viewportTop = $(window).scrollTop();
                    const viewportBottom = viewportTop + $(window).height();
                    
                    if (elementBottom > viewportTop && elementTop < viewportBottom) {
                        $(this).css('transform', 'translateY(0)').css('opacity', '1');
                    }
                });
            });
        });
    </script>
</body>
</html>
