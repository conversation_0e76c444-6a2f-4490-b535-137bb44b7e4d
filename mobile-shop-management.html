<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Shop Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            flex-wrap: wrap;
            gap: 10px;
        }

        .nav-tab {
            flex: 1;
            min-width: 150px;
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
        }

        .nav-tab:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .content-section {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            min-height: 500px;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            color: #333;
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .stat-card h3 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stat-card p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }
            
            .nav-tab {
                min-width: auto;
            }
            
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📱 Mobile Shop Management System</h1>
            <p>Complete Offline Mobile Shop Management Solution</p>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showSection('dashboard')">📊 Dashboard</button>
            <button class="nav-tab" onclick="showSection('inventory')">📱 Mobile Inventory</button>
            <button class="nav-tab" onclick="showSection('customers')">👥 Customers</button>
            <button class="nav-tab" onclick="showSection('orders')">📊 Sales & Stock</button>
            <button class="nav-tab" onclick="showSection('reports')">📈 Reports</button>
            <button class="nav-tab" onclick="showSection('settings')">⚙️ Settings</button>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard" class="content-section active">
            <h2 class="section-title">📊 Dashboard</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalMobiles">0</h3>
                    <p>Total Mobiles</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalCustomers">0</h3>
                    <p>Total Customers</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalOrders">0</h3>
                    <p>Total Sales</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalRevenue">₹0</h3>
                    <p>Total Revenue</p>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 30px;">
                <div>
                    <h3>📱 Recent Mobile Additions</h3>
                    <div id="recentMobiles" style="background: #f8f9fa; padding: 20px; border-radius: 10px; min-height: 200px;">
                        <p style="text-align: center; color: #666;">No mobiles added yet</p>
                    </div>
                </div>
                <div>
                    <h3>💰 Recent Sales</h3>
                    <div id="recentOrders" style="background: #f8f9fa; padding: 20px; border-radius: 10px; min-height: 200px;">
                        <p style="text-align: center; color: #666;">No sales yet</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Inventory Section -->
        <div id="inventory" class="content-section">
            <h2 class="section-title">📱 Mobile Inventory Management</h2>

            <!-- Add Mobile Form -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #333;">➕ Add New Mobile</h3>
                <form id="addMobileForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="form-group">
                        <label for="mobileBrand">Brand *</label>
                        <select id="mobileBrand" required>
                            <option value="">Select Brand</option>
                            <option value="Samsung">Samsung</option>
                            <option value="Apple">Apple</option>
                            <option value="Xiaomi">Xiaomi</option>
                            <option value="OnePlus">OnePlus</option>
                            <option value="Oppo">Oppo</option>
                            <option value="Vivo">Vivo</option>
                            <option value="Realme">Realme</option>
                            <option value="Huawei">Huawei</option>
                            <option value="Nokia">Nokia</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="mobileModel">Model *</label>
                        <input type="text" id="mobileModel" placeholder="e.g., Galaxy S24, iPhone 15" required>
                    </div>

                    <div class="form-group">
                        <label for="mobilePrice">Price (₹) *</label>
                        <input type="number" id="mobilePrice" placeholder="e.g., 50000" required min="0">
                    </div>

                    <div class="form-group">
                        <label for="mobileStock">Stock Quantity *</label>
                        <input type="number" id="mobileStock" placeholder="e.g., 10" required min="0">
                    </div>

                    <div class="form-group">
                        <label for="mobileRAM">RAM (GB)</label>
                        <select id="mobileRAM">
                            <option value="">Select RAM</option>
                            <option value="2">2 GB</option>
                            <option value="3">3 GB</option>
                            <option value="4">4 GB</option>
                            <option value="6">6 GB</option>
                            <option value="8">8 GB</option>
                            <option value="12">12 GB</option>
                            <option value="16">16 GB</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="mobileStorage">Storage (GB)</label>
                        <select id="mobileStorage">
                            <option value="">Select Storage</option>
                            <option value="32">32 GB</option>
                            <option value="64">64 GB</option>
                            <option value="128">128 GB</option>
                            <option value="256">256 GB</option>
                            <option value="512">512 GB</option>
                            <option value="1024">1 TB</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="mobileColor">Color</label>
                        <input type="text" id="mobileColor" placeholder="e.g., Black, White, Blue">
                    </div>

                    <div class="form-group">
                        <label for="mobileCondition">Condition</label>
                        <select id="mobileCondition">
                            <option value="New">New</option>
                            <option value="Refurbished">Refurbished</option>
                            <option value="Used">Used</option>
                        </select>
                    </div>

                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label for="mobileDescription">Description</label>
                        <textarea id="mobileDescription" rows="3" placeholder="Additional details about the mobile..."></textarea>
                    </div>

                    <div style="grid-column: 1 / -1; text-align: center;">
                        <button type="submit" class="btn btn-success">➕ Add Mobile</button>
                        <button type="button" class="btn" onclick="clearMobileForm()">🔄 Clear Form</button>
                    </div>
                </form>
            </div>

            <!-- Search and Filter -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                <h3 style="margin-bottom: 15px; color: #333;">🔍 Search & Filter</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <input type="text" id="searchMobile" placeholder="Search by brand, model..." onkeyup="filterMobiles()">
                    <select id="filterBrand" onchange="filterMobiles()">
                        <option value="">All Brands</option>
                    </select>
                    <select id="filterCondition" onchange="filterMobiles()">
                        <option value="">All Conditions</option>
                        <option value="New">New</option>
                        <option value="Refurbished">Refurbished</option>
                        <option value="Used">Used</option>
                    </select>
                    <select id="filterStock" onchange="filterMobiles()">
                        <option value="">All Stock</option>
                        <option value="instock">In Stock</option>
                        <option value="lowstock">Low Stock (≤5)</option>
                        <option value="outofstock">Out of Stock</option>
                    </select>
                </div>
            </div>

            <!-- Mobile List -->
            <div class="table-container">
                <table id="mobileTable">
                    <thead>
                        <tr>
                            <th>Brand</th>
                            <th>Model</th>
                            <th>Price</th>
                            <th>Stock</th>
                            <th>RAM/Storage</th>
                            <th>Color</th>
                            <th>Condition</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="mobileTableBody">
                        <tr>
                            <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                                No mobiles in inventory. Add your first mobile above! 📱
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Customers Section -->
        <div id="customers" class="content-section">
            <h2 class="section-title">👥 Customer Management</h2>

            <!-- Add Customer Form -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #333;">➕ Add New Customer</h3>
                <form id="addCustomerForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="form-group">
                        <label for="customerName">Full Name *</label>
                        <input type="text" id="customerName" placeholder="Enter customer name" required>
                    </div>

                    <div class="form-group">
                        <label for="customerPhone">Phone Number *</label>
                        <input type="tel" id="customerPhone" placeholder="e.g., +92 300 1234567" required>
                    </div>

                    <div class="form-group">
                        <label for="customerEmail">Email</label>
                        <input type="email" id="customerEmail" placeholder="<EMAIL>">
                    </div>

                    <div class="form-group">
                        <label for="customerCNIC">CNIC</label>
                        <input type="text" id="customerCNIC" placeholder="12345-1234567-1">
                    </div>

                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label for="customerAddress">Address</label>
                        <textarea id="customerAddress" rows="3" placeholder="Customer address..."></textarea>
                    </div>

                    <div style="grid-column: 1 / -1; text-align: center;">
                        <button type="submit" class="btn btn-success">➕ Add Customer</button>
                        <button type="button" class="btn" onclick="clearCustomerForm()">🔄 Clear Form</button>
                    </div>
                </form>
            </div>

            <!-- Search Customers -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                <h3 style="margin-bottom: 15px; color: #333;">🔍 Search Customers</h3>
                <input type="text" id="searchCustomer" placeholder="Search by name, phone, or email..." onkeyup="filterCustomers()" style="width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px;">
            </div>

            <!-- Customer List -->
            <div class="table-container">
                <table id="customerTable">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>CNIC</th>
                            <th>Total Orders</th>
                            <th>Total Spent</th>
                            <th>Joined Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="customerTableBody">
                        <tr>
                            <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                                No customers registered yet. Add your first customer above! 👥
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Sales & Stock Section -->
        <div id="orders" class="content-section">
            <h2 class="section-title">📊 Sales & Stock Management</h2>

            <!-- Quick Sale Form -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #333;">💰 Record Sale</h3>
                <form id="recordSaleForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div class="form-group">
                        <label for="saleMobile">Select Mobile *</label>
                        <select id="saleMobile" required onchange="updateSalePrice()">
                            <option value="">Select Mobile to Sell</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="saleQuantity">Quantity *</label>
                        <input type="number" id="saleQuantity" value="1" min="1" required onchange="updateSalePrice()">
                    </div>

                    <div class="form-group">
                        <label for="salePrice">Sale Price (₹) *</label>
                        <input type="number" id="salePrice" required onchange="updateSaleTotal()">
                    </div>

                    <div class="form-group">
                        <label for="saleTotal">Total Amount (₹)</label>
                        <input type="number" id="saleTotal" readonly style="background: #f0f0f0; font-weight: bold;">
                    </div>

                    <div class="form-group">
                        <label for="customerName">Customer Name</label>
                        <input type="text" id="customerName" placeholder="Optional customer name">
                    </div>

                    <div class="form-group">
                        <label for="customerPhone">Customer Phone</label>
                        <input type="tel" id="customerPhone" placeholder="Optional phone number">
                    </div>

                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label for="saleNotes">Sale Notes</label>
                        <textarea id="saleNotes" rows="2" placeholder="Any notes about this sale..."></textarea>
                    </div>

                    <div style="grid-column: 1 / -1; text-align: center;">
                        <button type="submit" class="btn btn-success">💰 Record Sale</button>
                        <button type="button" class="btn" onclick="clearSaleForm()">🔄 Clear Form</button>
                    </div>
                </form>
            </div>

            <!-- Stock Update Form -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #333;">📦 Update Stock</h3>
                <form id="updateStockForm" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div class="form-group">
                        <label for="stockMobile">Select Mobile *</label>
                        <select id="stockMobile" required>
                            <option value="">Select Mobile</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="stockAction">Action *</label>
                        <select id="stockAction" required>
                            <option value="">Select Action</option>
                            <option value="add">Add Stock (Purchase)</option>
                            <option value="remove">Remove Stock (Damage/Return)</option>
                            <option value="set">Set Stock (Inventory Count)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="stockQuantity">Quantity *</label>
                        <input type="number" id="stockQuantity" min="1" required>
                    </div>

                    <div class="form-group">
                        <label for="stockReason">Reason</label>
                        <input type="text" id="stockReason" placeholder="e.g., New purchase, Damaged, etc.">
                    </div>

                    <div style="grid-column: 1 / -1; text-align: center;">
                        <button type="submit" class="btn btn-success">📦 Update Stock</button>
                        <button type="button" class="btn" onclick="clearStockForm()">🔄 Clear Form</button>
                    </div>
                </form>
            </div>

            <!-- Sales History -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                <h3 style="margin-bottom: 15px; color: #333;">🔍 Filter Sales</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <input type="text" id="searchSale" placeholder="Search by mobile, customer..." onkeyup="filterSales()">
                    <select id="filterSaleDate" onchange="filterSales()">
                        <option value="">All Dates</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                    </select>
                    <select id="filterSaleBrand" onchange="filterSales()">
                        <option value="">All Brands</option>
                    </select>
                </div>
            </div>

            <!-- Sales Table -->
            <div class="table-container">
                <table id="salesTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Mobile</th>
                            <th>Quantity</th>
                            <th>Sale Price</th>
                            <th>Total</th>
                            <th>Customer</th>
                            <th>Profit</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="salesTableBody">
                        <tr>
                            <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                                No sales recorded yet. Record your first sale above! 💰
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Reports Section -->
        <div id="reports" class="content-section">
            <h2 class="section-title">📈 Reports & Analytics</h2>

            <!-- Summary Cards -->
            <div class="stats-grid" style="margin-bottom: 30px;">
                <div class="stat-card" style="background: linear-gradient(135deg, #00b894 0%, #00a085 100%);">
                    <h3 id="todaySales">₹0</h3>
                    <p>Today's Sales</p>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);">
                    <h3 id="weekSales">₹0</h3>
                    <p>This Week's Sales</p>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);">
                    <h3 id="monthSales">₹0</h3>
                    <p>This Month's Sales</p>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);">
                    <h3 id="totalProfit">₹0</h3>
                    <p>Total Profit</p>
                </div>
            </div>

            <!-- Top Selling Mobiles -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
                <div style="background: #f8f9fa; padding: 25px; border-radius: 15px;">
                    <h3 style="margin-bottom: 20px; color: #333;">🏆 Top Selling Mobiles</h3>
                    <div id="topSellingMobiles">
                        <p style="text-align: center; color: #666;">No sales data available</p>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 25px; border-radius: 15px;">
                    <h3 style="margin-bottom: 20px; color: #333;">⚠️ Low Stock Alert</h3>
                    <div id="lowStockAlert">
                        <p style="text-align: center; color: #666;">All items are well stocked</p>
                    </div>
                </div>
            </div>

            <!-- Brand Performance -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #333;">📊 Brand Performance</h3>
                <div id="brandPerformance">
                    <p style="text-align: center; color: #666;">No sales data available</p>
                </div>
            </div>

            <!-- Monthly Sales Chart (Text-based) -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #333;">📈 Sales Trend (Last 7 Days)</h3>
                <div id="salesTrend">
                    <p style="text-align: center; color: #666;">No sales data available</p>
                </div>
            </div>

            <!-- Export Options -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px;">
                <h3 style="margin-bottom: 20px; color: #333;">📤 Export Reports</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="btn btn-success" onclick="exportSalesReport()">📊 Export Sales Report</button>
                    <button class="btn btn-success" onclick="exportInventoryReport()">📦 Export Inventory Report</button>
                    <button class="btn btn-success" onclick="exportCustomerReport()">👥 Export Customer Report</button>
                    <button class="btn" onclick="printReport()">🖨️ Print Summary</button>
                </div>
            </div>
        </div>

        <!-- Settings Section -->
        <div id="settings" class="content-section">
            <h2 class="section-title">⚙️ Settings & Data Management</h2>

            <!-- Data Backup & Restore -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #333;">💾 Data Backup & Restore</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div>
                        <h4>📤 Export Data</h4>
                        <p style="margin-bottom: 15px; color: #666;">Download all your data as backup files</p>
                        <button class="btn btn-success" onclick="exportAllData()">📤 Export All Data</button>
                        <button class="btn" onclick="exportMobilesData()">📱 Export Mobiles Only</button>
                        <button class="btn" onclick="exportSalesData()">💰 Export Sales Only</button>
                    </div>

                    <div>
                        <h4>📥 Import Data</h4>
                        <p style="margin-bottom: 15px; color: #666;">Restore data from backup files</p>
                        <input type="file" id="importFile" accept=".json" style="margin-bottom: 10px;">
                        <br>
                        <button class="btn btn-success" onclick="importData()">📥 Import Data</button>
                        <button class="btn btn-danger" onclick="clearAllData()">🗑️ Clear All Data</button>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #333;">ℹ️ System Information</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div>
                        <h4>📊 Data Statistics</h4>
                        <p>Total Mobiles: <strong id="settingsTotalMobiles">0</strong></p>
                        <p>Total Customers: <strong id="settingsTotalCustomers">0</strong></p>
                        <p>Total Sales: <strong id="settingsTotalSales">0</strong></p>
                        <p>Data Size: <strong id="dataSize">0 KB</strong></p>
                    </div>

                    <div>
                        <h4>💾 Storage Information</h4>
                        <p>Storage Type: <strong>Browser LocalStorage</strong></p>
                        <p>Data Location: <strong>Local Browser</strong></p>
                        <p>Auto-Save: <strong>Enabled</strong></p>
                        <p>Last Updated: <strong id="lastUpdated">-</strong></p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; margin-bottom: 30px;">
                <h3 style="margin-bottom: 20px; color: #333;">⚡ Quick Actions</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <button class="btn" onclick="refreshData()">🔄 Refresh Data</button>
                    <button class="btn" onclick="validateData()">✅ Validate Data</button>
                    <button class="btn" onclick="compactData()">🗜️ Compact Data</button>
                    <button class="btn" onclick="showDataSummary()">📋 Data Summary</button>
                </div>
            </div>

            <!-- About -->
            <div style="background: #f8f9fa; padding: 25px; border-radius: 15px;">
                <h3 style="margin-bottom: 20px; color: #333;">ℹ️ About</h3>
                <div style="text-align: center;">
                    <h4>📱 Mobile Shop Management System</h4>
                    <p style="margin: 10px 0; color: #666;">Complete Offline Mobile Shop Management Solution</p>
                    <p style="margin: 10px 0; color: #666;">Version: 1.0.0</p>
                    <p style="margin: 10px 0; color: #666;">Built with: HTML, CSS, JavaScript</p>
                    <p style="margin: 10px 0; color: #666;">Storage: Browser LocalStorage</p>
                    <p style="margin: 10px 0; color: #666;">No internet connection required</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables for data storage
        let mobiles = JSON.parse(localStorage.getItem('mobiles')) || [];
        let customers = JSON.parse(localStorage.getItem('customers')) || [];
        let sales = JSON.parse(localStorage.getItem('sales')) || [];
        let stockHistory = JSON.parse(localStorage.getItem('stockHistory')) || [];

        // Navigation function
        function showSection(sectionId) {
            // Hide all sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => section.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected section
            document.getElementById(sectionId).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
            
            // Update dashboard if dashboard is selected
            if (sectionId === 'dashboard') {
                updateDashboard();
            }

            // Update reports if reports section is selected
            if (sectionId === 'reports') {
                updateReports();
            }

            // Update settings if settings section is selected
            if (sectionId === 'settings') {
                updateSettingsInfo();
            }
        }

        // Update dashboard statistics
        function updateDashboard() {
            document.getElementById('totalMobiles').textContent = mobiles.length;
            document.getElementById('totalCustomers').textContent = customers.length;
            document.getElementById('totalOrders').textContent = sales.length;

            // Calculate total revenue
            const totalRevenue = sales.reduce((sum, sale) => sum + (sale.total || 0), 0);
            document.getElementById('totalRevenue').textContent = `₹${totalRevenue.toLocaleString()}`;

            // Show recent mobiles
            const recentMobilesDiv = document.getElementById('recentMobiles');
            if (mobiles.length > 0) {
                const recentMobiles = mobiles.slice(-5).reverse();
                recentMobilesDiv.innerHTML = recentMobiles.map(mobile =>
                    `<div style="padding: 10px; border-bottom: 1px solid #ddd;">
                        <strong>${mobile.brand} ${mobile.model}</strong> - ₹${mobile.price.toLocaleString()}
                        <br><small>Stock: ${mobile.stock}</small>
                    </div>`
                ).join('');
            }

            // Show recent sales
            const recentOrdersDiv = document.getElementById('recentOrders');
            recentOrdersDiv.querySelector('h3').textContent = '💰 Recent Sales';
            if (sales.length > 0) {
                const recentSales = sales.slice(-5).reverse();
                recentOrdersDiv.innerHTML = '<h3>💰 Recent Sales</h3>' + recentSales.map(sale =>
                    `<div style="padding: 10px; border-bottom: 1px solid #ddd;">
                        <strong>${sale.mobileBrand} ${sale.mobileModel}</strong> - ₹${sale.total.toLocaleString()}
                        <br><small>${sale.customerName || 'Walk-in Customer'} - ${sale.date}</small>
                    </div>`
                ).join('');
            } else {
                recentOrdersDiv.innerHTML = '<h3>💰 Recent Sales</h3><p style="text-align: center; color: #666;">No sales yet</p>';
            }
        }

        // Mobile Inventory Functions
        function addMobile() {
            const form = document.getElementById('addMobileForm');
            const formData = new FormData(form);

            const mobile = {
                id: Date.now().toString(),
                brand: document.getElementById('mobileBrand').value,
                model: document.getElementById('mobileModel').value,
                price: parseFloat(document.getElementById('mobilePrice').value),
                stock: parseInt(document.getElementById('mobileStock').value),
                ram: document.getElementById('mobileRAM').value,
                storage: document.getElementById('mobileStorage').value,
                color: document.getElementById('mobileColor').value,
                condition: document.getElementById('mobileCondition').value || 'New',
                description: document.getElementById('mobileDescription').value,
                dateAdded: new Date().toLocaleDateString()
            };

            mobiles.push(mobile);
            localStorage.setItem('mobiles', JSON.stringify(mobiles));

            displayMobiles();
            updateBrandFilter();
            clearMobileForm();
            updateDashboard();

            alert('✅ Mobile added successfully!');
        }

        function clearMobileForm() {
            document.getElementById('addMobileForm').reset();
        }

        function displayMobiles(mobilesToShow = mobiles) {
            const tbody = document.getElementById('mobileTableBody');

            if (mobilesToShow.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                            No mobiles found. ${mobiles.length === 0 ? 'Add your first mobile above!' : 'Try adjusting your filters.'} 📱
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = mobilesToShow.map(mobile => `
                <tr>
                    <td><strong>${mobile.brand}</strong></td>
                    <td>${mobile.model}</td>
                    <td>₹${mobile.price.toLocaleString()}</td>
                    <td>
                        <span style="color: ${mobile.stock <= 0 ? '#ff6b6b' : mobile.stock <= 5 ? '#ffa726' : '#00b894'}">
                            ${mobile.stock}
                        </span>
                    </td>
                    <td>${mobile.ram ? mobile.ram + 'GB' : '-'} / ${mobile.storage ? mobile.storage + 'GB' : '-'}</td>
                    <td>${mobile.color || '-'}</td>
                    <td>
                        <span style="padding: 4px 8px; border-radius: 4px; font-size: 0.8em;
                                     background: ${mobile.condition === 'New' ? '#e8f5e8' : mobile.condition === 'Refurbished' ? '#fff3e0' : '#fce4ec'};
                                     color: ${mobile.condition === 'New' ? '#2e7d32' : mobile.condition === 'Refurbished' ? '#f57c00' : '#c2185b'};">
                            ${mobile.condition}
                        </span>
                    </td>
                    <td>
                        <button class="btn" onclick="editMobile('${mobile.id}')" style="padding: 5px 10px; font-size: 0.8em;">✏️ Edit</button>
                        <button class="btn btn-danger" onclick="deleteMobile('${mobile.id}')" style="padding: 5px 10px; font-size: 0.8em;">🗑️ Delete</button>
                    </td>
                </tr>
            `).join('');
        }

        function updateBrandFilter() {
            const filterBrand = document.getElementById('filterBrand');
            const brands = [...new Set(mobiles.map(mobile => mobile.brand))].sort();

            filterBrand.innerHTML = '<option value="">All Brands</option>' +
                brands.map(brand => `<option value="${brand}">${brand}</option>`).join('');
        }

        function filterMobiles() {
            const searchTerm = document.getElementById('searchMobile').value.toLowerCase();
            const brandFilter = document.getElementById('filterBrand').value;
            const conditionFilter = document.getElementById('filterCondition').value;
            const stockFilter = document.getElementById('filterStock').value;

            let filteredMobiles = mobiles.filter(mobile => {
                const matchesSearch = mobile.brand.toLowerCase().includes(searchTerm) ||
                                    mobile.model.toLowerCase().includes(searchTerm);
                const matchesBrand = !brandFilter || mobile.brand === brandFilter;
                const matchesCondition = !conditionFilter || mobile.condition === conditionFilter;

                let matchesStock = true;
                if (stockFilter === 'instock') matchesStock = mobile.stock > 0;
                else if (stockFilter === 'lowstock') matchesStock = mobile.stock > 0 && mobile.stock <= 5;
                else if (stockFilter === 'outofstock') matchesStock = mobile.stock <= 0;

                return matchesSearch && matchesBrand && matchesCondition && matchesStock;
            });

            displayMobiles(filteredMobiles);
        }

        function editMobile(id) {
            const mobile = mobiles.find(m => m.id === id);
            if (!mobile) return;

            // Fill form with mobile data
            document.getElementById('mobileBrand').value = mobile.brand;
            document.getElementById('mobileModel').value = mobile.model;
            document.getElementById('mobilePrice').value = mobile.price;
            document.getElementById('mobileStock').value = mobile.stock;
            document.getElementById('mobileRAM').value = mobile.ram;
            document.getElementById('mobileStorage').value = mobile.storage;
            document.getElementById('mobileColor').value = mobile.color;
            document.getElementById('mobileCondition').value = mobile.condition;
            document.getElementById('mobileDescription').value = mobile.description;

            // Change form to edit mode
            const form = document.getElementById('addMobileForm');
            form.onsubmit = function(e) {
                e.preventDefault();
                updateMobile(id);
            };

            // Change button text
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '💾 Update Mobile';
            submitBtn.className = 'btn btn-success';

            // Scroll to form
            form.scrollIntoView({ behavior: 'smooth' });
        }

        function updateMobile(id) {
            const mobileIndex = mobiles.findIndex(m => m.id === id);
            if (mobileIndex === -1) return;

            mobiles[mobileIndex] = {
                ...mobiles[mobileIndex],
                brand: document.getElementById('mobileBrand').value,
                model: document.getElementById('mobileModel').value,
                price: parseFloat(document.getElementById('mobilePrice').value),
                stock: parseInt(document.getElementById('mobileStock').value),
                ram: document.getElementById('mobileRAM').value,
                storage: document.getElementById('mobileStorage').value,
                color: document.getElementById('mobileColor').value,
                condition: document.getElementById('mobileCondition').value,
                description: document.getElementById('mobileDescription').value
            };

            localStorage.setItem('mobiles', JSON.stringify(mobiles));

            displayMobiles();
            updateBrandFilter();
            resetForm();
            updateDashboard();

            alert('✅ Mobile updated successfully!');
        }

        function resetForm() {
            const form = document.getElementById('addMobileForm');
            form.onsubmit = function(e) {
                e.preventDefault();
                addMobile();
            };

            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '➕ Add Mobile';
            submitBtn.className = 'btn btn-success';

            clearMobileForm();
        }

        function deleteMobile(id) {
            if (confirm('Are you sure you want to delete this mobile?')) {
                mobiles = mobiles.filter(m => m.id !== id);
                localStorage.setItem('mobiles', JSON.stringify(mobiles));

                displayMobiles();
                updateBrandFilter();
                updateDashboard();

                alert('✅ Mobile deleted successfully!');
            }
        }

        // Customer Management Functions
        function addCustomer() {
            const customer = {
                id: Date.now().toString(),
                name: document.getElementById('customerName').value,
                phone: document.getElementById('customerPhone').value,
                email: document.getElementById('customerEmail').value,
                cnic: document.getElementById('customerCNIC').value,
                address: document.getElementById('customerAddress').value,
                joinedDate: new Date().toLocaleDateString(),
                totalOrders: 0,
                totalSpent: 0
            };

            // Check if phone number already exists
            if (customers.some(c => c.phone === customer.phone)) {
                alert('❌ Customer with this phone number already exists!');
                return;
            }

            customers.push(customer);
            localStorage.setItem('customers', JSON.stringify(customers));

            displayCustomers();
            clearCustomerForm();
            updateDashboard();

            alert('✅ Customer added successfully!');
        }

        function clearCustomerForm() {
            document.getElementById('addCustomerForm').reset();
        }

        function displayCustomers(customersToShow = customers) {
            const tbody = document.getElementById('customerTableBody');

            if (customersToShow.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                            No customers found. ${customers.length === 0 ? 'Add your first customer above!' : 'Try adjusting your search.'} 👥
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = customersToShow.map(customer => {
                // Calculate customer stats
                const customerSales = sales.filter(sale => sale.customerPhone === customer.phone);
                const totalOrders = customerSales.length;
                const totalSpent = customerSales.reduce((sum, sale) => sum + (sale.total || 0), 0);

                return `
                    <tr>
                        <td><strong>${customer.name}</strong></td>
                        <td>${customer.phone}</td>
                        <td>${customer.email || '-'}</td>
                        <td>${customer.cnic || '-'}</td>
                        <td>${totalOrders}</td>
                        <td>₹${totalSpent.toLocaleString()}</td>
                        <td>${customer.joinedDate}</td>
                        <td>
                            <button class="btn" onclick="viewCustomerDetails('${customer.id}')" style="padding: 5px 10px; font-size: 0.8em;">👁️ View</button>
                            <button class="btn" onclick="editCustomer('${customer.id}')" style="padding: 5px 10px; font-size: 0.8em;">✏️ Edit</button>
                            <button class="btn btn-danger" onclick="deleteCustomer('${customer.id}')" style="padding: 5px 10px; font-size: 0.8em;">🗑️ Delete</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function filterCustomers() {
            const searchTerm = document.getElementById('searchCustomer').value.toLowerCase();

            const filteredCustomers = customers.filter(customer =>
                customer.name.toLowerCase().includes(searchTerm) ||
                customer.phone.toLowerCase().includes(searchTerm) ||
                (customer.email && customer.email.toLowerCase().includes(searchTerm))
            );

            displayCustomers(filteredCustomers);
        }

        function editCustomer(id) {
            const customer = customers.find(c => c.id === id);
            if (!customer) return;

            // Fill form with customer data
            document.getElementById('customerName').value = customer.name;
            document.getElementById('customerPhone').value = customer.phone;
            document.getElementById('customerEmail').value = customer.email;
            document.getElementById('customerCNIC').value = customer.cnic;
            document.getElementById('customerAddress').value = customer.address;

            // Change form to edit mode
            const form = document.getElementById('addCustomerForm');
            form.onsubmit = function(e) {
                e.preventDefault();
                updateCustomer(id);
            };

            // Change button text
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '💾 Update Customer';

            // Scroll to form
            form.scrollIntoView({ behavior: 'smooth' });
        }

        function updateCustomer(id) {
            const customerIndex = customers.findIndex(c => c.id === id);
            if (customerIndex === -1) return;

            const updatedPhone = document.getElementById('customerPhone').value;

            // Check if phone number already exists (excluding current customer)
            if (customers.some(c => c.phone === updatedPhone && c.id !== id)) {
                alert('❌ Customer with this phone number already exists!');
                return;
            }

            customers[customerIndex] = {
                ...customers[customerIndex],
                name: document.getElementById('customerName').value,
                phone: updatedPhone,
                email: document.getElementById('customerEmail').value,
                cnic: document.getElementById('customerCNIC').value,
                address: document.getElementById('customerAddress').value
            };

            localStorage.setItem('customers', JSON.stringify(customers));

            displayCustomers();
            resetCustomerForm();
            updateDashboard();

            alert('✅ Customer updated successfully!');
        }

        function resetCustomerForm() {
            const form = document.getElementById('addCustomerForm');
            form.onsubmit = function(e) {
                e.preventDefault();
                addCustomer();
            };

            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '➕ Add Customer';

            clearCustomerForm();
        }

        function deleteCustomer(id) {
            const customer = customers.find(c => c.id === id);
            if (!customer) return;

            const customerSales = sales.filter(sale => sale.customerPhone === customer.phone);

            if (customerSales.length > 0) {
                if (!confirm(`This customer has ${customerSales.length} purchase(s). Are you sure you want to delete this customer? The purchase history will remain but customer info will be removed.`)) {
                    return;
                }
            } else {
                if (!confirm('Are you sure you want to delete this customer?')) {
                    return;
                }
            }

            customers = customers.filter(c => c.id !== id);
            localStorage.setItem('customers', JSON.stringify(customers));

            displayCustomers();
            updateDashboard();

            alert('✅ Customer deleted successfully!');
        }

        function viewCustomerDetails(id) {
            const customer = customers.find(c => c.id === id);
            if (!customer) return;

            const customerSales = sales.filter(sale => sale.customerPhone === customer.phone);
            const totalSpent = customerSales.reduce((sum, sale) => sum + (sale.total || 0), 0);

            const details = `
                📱 Customer Details:

                Name: ${customer.name}
                Phone: ${customer.phone}
                Email: ${customer.email || 'Not provided'}
                CNIC: ${customer.cnic || 'Not provided'}
                Address: ${customer.address || 'Not provided'}
                Joined Date: ${customer.joinedDate}

                📊 Purchase Statistics:
                Total Purchases: ${customerSales.length}
                Total Spent: ₹${totalSpent.toLocaleString()}

                Recent Purchases:
                ${customerSales.slice(-5).map(sale =>
                    `• ${sale.mobileBrand} ${sale.mobileModel} - ₹${sale.total.toLocaleString()} (${sale.date})`
                ).join('\n') || 'No purchases yet'}
            `;

            alert(details);
        }

        // Sales & Stock Management Functions
        function recordSale() {
            const mobileId = document.getElementById('saleMobile').value;
            const quantity = parseInt(document.getElementById('saleQuantity').value);
            const salePrice = parseFloat(document.getElementById('salePrice').value);
            const total = parseFloat(document.getElementById('saleTotal').value);

            const mobile = mobiles.find(m => m.id === mobileId);
            if (!mobile) {
                alert('❌ Please select a mobile!');
                return;
            }

            if (mobile.stock < quantity) {
                alert(`❌ Insufficient stock! Available: ${mobile.stock}`);
                return;
            }

            // Create sale record
            const sale = {
                id: Date.now().toString(),
                mobileId: mobileId,
                mobileBrand: mobile.brand,
                mobileModel: mobile.model,
                originalPrice: mobile.price,
                salePrice: salePrice,
                quantity: quantity,
                total: total,
                profit: (salePrice - mobile.price) * quantity,
                customerName: document.getElementById('customerName').value || 'Walk-in Customer',
                customerPhone: document.getElementById('customerPhone').value,
                notes: document.getElementById('saleNotes').value,
                date: new Date().toLocaleDateString(),
                time: new Date().toLocaleTimeString()
            };

            // Update mobile stock
            mobile.stock -= quantity;

            // Save data
            sales.push(sale);
            localStorage.setItem('sales', JSON.stringify(sales));
            localStorage.setItem('mobiles', JSON.stringify(mobiles));

            // Update displays
            displaySales();
            displayMobiles();
            updateSaleBrandFilter();
            updateSaleMobileOptions();
            clearSaleForm();
            updateDashboard();

            alert(`✅ Sale recorded successfully! Stock updated: ${mobile.brand} ${mobile.model} (${mobile.stock} remaining)`);
        }

        function updateSalePrice() {
            const mobileId = document.getElementById('saleMobile').value;
            const quantity = parseInt(document.getElementById('saleQuantity').value) || 1;

            if (mobileId) {
                const mobile = mobiles.find(m => m.id === mobileId);
                if (mobile) {
                    document.getElementById('salePrice').value = mobile.price;
                    updateSaleTotal();
                }
            }
        }

        function updateSaleTotal() {
            const quantity = parseInt(document.getElementById('saleQuantity').value) || 1;
            const salePrice = parseFloat(document.getElementById('salePrice').value) || 0;
            const total = quantity * salePrice;
            document.getElementById('saleTotal').value = total;
        }

        function clearSaleForm() {
            document.getElementById('recordSaleForm').reset();
            document.getElementById('saleQuantity').value = 1;
            document.getElementById('saleTotal').value = '';
        }

        function updateStock() {
            const mobileId = document.getElementById('stockMobile').value;
            const action = document.getElementById('stockAction').value;
            const quantity = parseInt(document.getElementById('stockQuantity').value);
            const reason = document.getElementById('stockReason').value;

            const mobile = mobiles.find(m => m.id === mobileId);
            if (!mobile) {
                alert('❌ Please select a mobile!');
                return;
            }

            let newStock = mobile.stock;
            let actionText = '';

            switch(action) {
                case 'add':
                    newStock += quantity;
                    actionText = `Added ${quantity} units`;
                    break;
                case 'remove':
                    if (mobile.stock < quantity) {
                        alert(`❌ Cannot remove ${quantity} units. Available: ${mobile.stock}`);
                        return;
                    }
                    newStock -= quantity;
                    actionText = `Removed ${quantity} units`;
                    break;
                case 'set':
                    newStock = quantity;
                    actionText = `Set stock to ${quantity} units`;
                    break;
                default:
                    alert('❌ Please select an action!');
                    return;
            }

            // Create stock history record
            const stockRecord = {
                id: Date.now().toString(),
                mobileId: mobileId,
                mobileBrand: mobile.brand,
                mobileModel: mobile.model,
                action: action,
                quantity: quantity,
                oldStock: mobile.stock,
                newStock: newStock,
                reason: reason,
                date: new Date().toLocaleDateString(),
                time: new Date().toLocaleTimeString()
            };

            // Update mobile stock
            mobile.stock = newStock;

            // Save data
            stockHistory.push(stockRecord);
            localStorage.setItem('stockHistory', JSON.stringify(stockHistory));
            localStorage.setItem('mobiles', JSON.stringify(mobiles));

            // Update displays
            displayMobiles();
            updateStockMobileOptions();
            clearStockForm();
            updateDashboard();

            alert(`✅ Stock updated successfully! ${mobile.brand} ${mobile.model}: ${actionText} (New stock: ${newStock})`);
        }

        function clearStockForm() {
            document.getElementById('updateStockForm').reset();
        }

        function displaySales(salesToShow = sales) {
            const tbody = document.getElementById('salesTableBody');

            if (salesToShow.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                            No sales found. ${sales.length === 0 ? 'Record your first sale above!' : 'Try adjusting your filters.'} 💰
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = salesToShow.map(sale => `
                <tr>
                    <td>${sale.date}<br><small>${sale.time}</small></td>
                    <td><strong>${sale.mobileBrand}</strong><br>${sale.mobileModel}</td>
                    <td>${sale.quantity}</td>
                    <td>₹${sale.salePrice.toLocaleString()}</td>
                    <td><strong>₹${sale.total.toLocaleString()}</strong></td>
                    <td>${sale.customerName}<br><small>${sale.customerPhone || '-'}</small></td>
                    <td style="color: ${sale.profit >= 0 ? '#00b894' : '#ff6b6b'}">
                        ₹${sale.profit.toLocaleString()}
                    </td>
                    <td>
                        <button class="btn" onclick="viewSaleDetails('${sale.id}')" style="padding: 5px 10px; font-size: 0.8em;">👁️ View</button>
                        <button class="btn btn-danger" onclick="deleteSale('${sale.id}')" style="padding: 5px 10px; font-size: 0.8em;">🗑️ Delete</button>
                    </td>
                </tr>
            `).join('');
        }

        function updateSaleMobileOptions() {
            const saleMobileSelect = document.getElementById('saleMobile');
            const stockMobileSelect = document.getElementById('stockMobile');

            const mobileOptions = mobiles.map(mobile =>
                `<option value="${mobile.id}">${mobile.brand} ${mobile.model} (Stock: ${mobile.stock}) - ₹${mobile.price.toLocaleString()}</option>`
            ).join('');

            saleMobileSelect.innerHTML = '<option value="">Select Mobile to Sell</option>' + mobileOptions;
            stockMobileSelect.innerHTML = '<option value="">Select Mobile</option>' + mobileOptions;
        }

        function updateStockMobileOptions() {
            updateSaleMobileOptions(); // Same function for both
        }

        function updateSaleBrandFilter() {
            const filterBrand = document.getElementById('filterSaleBrand');
            const brands = [...new Set(sales.map(sale => sale.mobileBrand))].sort();

            filterBrand.innerHTML = '<option value="">All Brands</option>' +
                brands.map(brand => `<option value="${brand}">${brand}</option>`).join('');
        }

        function filterSales() {
            const searchTerm = document.getElementById('searchSale').value.toLowerCase();
            const dateFilter = document.getElementById('filterSaleDate').value;
            const brandFilter = document.getElementById('filterSaleBrand').value;

            let filteredSales = sales.filter(sale => {
                const matchesSearch = sale.mobileBrand.toLowerCase().includes(searchTerm) ||
                                    sale.mobileModel.toLowerCase().includes(searchTerm) ||
                                    sale.customerName.toLowerCase().includes(searchTerm);
                const matchesBrand = !brandFilter || sale.mobileBrand === brandFilter;

                let matchesDate = true;
                const saleDate = new Date(sale.date);
                const today = new Date();

                if (dateFilter === 'today') {
                    matchesDate = saleDate.toDateString() === today.toDateString();
                } else if (dateFilter === 'week') {
                    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                    matchesDate = saleDate >= weekAgo;
                } else if (dateFilter === 'month') {
                    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                    matchesDate = saleDate >= monthAgo;
                }

                return matchesSearch && matchesBrand && matchesDate;
            });

            displaySales(filteredSales);
        }

        function viewSaleDetails(id) {
            const sale = sales.find(s => s.id === id);
            if (!sale) return;

            const details = `
                💰 Sale Details:

                Mobile: ${sale.mobileBrand} ${sale.mobileModel}
                Original Price: ₹${sale.originalPrice.toLocaleString()}
                Sale Price: ₹${sale.salePrice.toLocaleString()}
                Quantity: ${sale.quantity}
                Total Amount: ₹${sale.total.toLocaleString()}
                Profit: ₹${sale.profit.toLocaleString()}

                Customer: ${sale.customerName}
                Phone: ${sale.customerPhone || 'Not provided'}

                Date: ${sale.date}
                Time: ${sale.time}

                Notes: ${sale.notes || 'No notes'}
            `;

            alert(details);
        }

        function deleteSale(id) {
            if (confirm('Are you sure you want to delete this sale? This will also restore the stock.')) {
                const sale = sales.find(s => s.id === id);
                if (sale) {
                    // Restore stock
                    const mobile = mobiles.find(m => m.id === sale.mobileId);
                    if (mobile) {
                        mobile.stock += sale.quantity;
                        localStorage.setItem('mobiles', JSON.stringify(mobiles));
                    }

                    // Remove sale
                    sales = sales.filter(s => s.id !== id);
                    localStorage.setItem('sales', JSON.stringify(sales));

                    displaySales();
                    displayMobiles();
                    updateSaleBrandFilter();
                    updateSaleMobileOptions();
                    updateDashboard();

                    alert('✅ Sale deleted and stock restored successfully!');
                }
            }
        }

        // Reports & Analytics Functions
        function updateReports() {
            updateSalesStats();
            updateTopSellingMobiles();
            updateLowStockAlert();
            updateBrandPerformance();
            updateSalesTrend();
        }

        function updateSalesStats() {
            const today = new Date();
            const todayStr = today.toLocaleDateString();

            // Today's sales
            const todaySales = sales.filter(sale => sale.date === todayStr);
            const todayRevenue = todaySales.reduce((sum, sale) => sum + sale.total, 0);
            document.getElementById('todaySales').textContent = `₹${todayRevenue.toLocaleString()}`;

            // This week's sales
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const weekSales = sales.filter(sale => new Date(sale.date) >= weekAgo);
            const weekRevenue = weekSales.reduce((sum, sale) => sum + sale.total, 0);
            document.getElementById('weekSales').textContent = `₹${weekRevenue.toLocaleString()}`;

            // This month's sales
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            const monthSales = sales.filter(sale => new Date(sale.date) >= monthAgo);
            const monthRevenue = monthSales.reduce((sum, sale) => sum + sale.total, 0);
            document.getElementById('monthSales').textContent = `₹${monthRevenue.toLocaleString()}`;

            // Total profit
            const totalProfit = sales.reduce((sum, sale) => sum + sale.profit, 0);
            document.getElementById('totalProfit').textContent = `₹${totalProfit.toLocaleString()}`;
        }

        function updateTopSellingMobiles() {
            const mobileStats = {};

            sales.forEach(sale => {
                const key = `${sale.mobileBrand} ${sale.mobileModel}`;
                if (!mobileStats[key]) {
                    mobileStats[key] = { quantity: 0, revenue: 0 };
                }
                mobileStats[key].quantity += sale.quantity;
                mobileStats[key].revenue += sale.total;
            });

            const sortedMobiles = Object.entries(mobileStats)
                .sort((a, b) => b[1].quantity - a[1].quantity)
                .slice(0, 5);

            const topSellingDiv = document.getElementById('topSellingMobiles');

            if (sortedMobiles.length === 0) {
                topSellingDiv.innerHTML = '<p style="text-align: center; color: #666;">No sales data available</p>';
                return;
            }

            topSellingDiv.innerHTML = sortedMobiles.map((mobile, index) => `
                <div style="padding: 10px; border-bottom: 1px solid #ddd; display: flex; justify-content: space-between;">
                    <div>
                        <strong>${index + 1}. ${mobile[0]}</strong>
                        <br><small>Sold: ${mobile[1].quantity} units</small>
                    </div>
                    <div style="text-align: right;">
                        <strong>₹${mobile[1].revenue.toLocaleString()}</strong>
                    </div>
                </div>
            `).join('');
        }

        function updateLowStockAlert() {
            const lowStockMobiles = mobiles.filter(mobile => mobile.stock <= 5);
            const lowStockDiv = document.getElementById('lowStockAlert');

            if (lowStockMobiles.length === 0) {
                lowStockDiv.innerHTML = '<p style="text-align: center; color: #00b894;">✅ All items are well stocked</p>';
                return;
            }

            lowStockDiv.innerHTML = lowStockMobiles.map(mobile => `
                <div style="padding: 10px; border-bottom: 1px solid #ddd; display: flex; justify-content: space-between;">
                    <div>
                        <strong>${mobile.brand} ${mobile.model}</strong>
                        <br><small style="color: ${mobile.stock === 0 ? '#ff6b6b' : '#ffa726'};">
                            ${mobile.stock === 0 ? 'Out of Stock' : `Only ${mobile.stock} left`}
                        </small>
                    </div>
                    <div style="text-align: right;">
                        <span style="color: ${mobile.stock === 0 ? '#ff6b6b' : '#ffa726'}; font-weight: bold;">
                            ${mobile.stock}
                        </span>
                    </div>
                </div>
            `).join('');
        }

        function updateBrandPerformance() {
            const brandStats = {};

            sales.forEach(sale => {
                if (!brandStats[sale.mobileBrand]) {
                    brandStats[sale.mobileBrand] = { quantity: 0, revenue: 0, profit: 0 };
                }
                brandStats[sale.mobileBrand].quantity += sale.quantity;
                brandStats[sale.mobileBrand].revenue += sale.total;
                brandStats[sale.mobileBrand].profit += sale.profit;
            });

            const sortedBrands = Object.entries(brandStats)
                .sort((a, b) => b[1].revenue - a[1].revenue);

            const brandPerformanceDiv = document.getElementById('brandPerformance');

            if (sortedBrands.length === 0) {
                brandPerformanceDiv.innerHTML = '<p style="text-align: center; color: #666;">No sales data available</p>';
                return;
            }

            brandPerformanceDiv.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    ${sortedBrands.map(brand => `
                        <div style="background: white; padding: 15px; border-radius: 10px; text-align: center;">
                            <h4 style="margin-bottom: 10px;">${brand[0]}</h4>
                            <p><strong>Units Sold:</strong> ${brand[1].quantity}</p>
                            <p><strong>Revenue:</strong> ₹${brand[1].revenue.toLocaleString()}</p>
                            <p><strong>Profit:</strong> ₹${brand[1].profit.toLocaleString()}</p>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function updateSalesTrend() {
            const last7Days = [];
            const today = new Date();

            for (let i = 6; i >= 0; i--) {
                const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
                const dateStr = date.toLocaleDateString();
                const daySales = sales.filter(sale => sale.date === dateStr);
                const dayRevenue = daySales.reduce((sum, sale) => sum + sale.total, 0);

                last7Days.push({
                    date: dateStr,
                    day: date.toLocaleDateString('en', { weekday: 'short' }),
                    revenue: dayRevenue,
                    count: daySales.length
                });
            }

            const maxRevenue = Math.max(...last7Days.map(day => day.revenue));
            const salesTrendDiv = document.getElementById('salesTrend');

            if (maxRevenue === 0) {
                salesTrendDiv.innerHTML = '<p style="text-align: center; color: #666;">No sales data available</p>';
                return;
            }

            salesTrendDiv.innerHTML = last7Days.map(day => {
                const barWidth = maxRevenue > 0 ? (day.revenue / maxRevenue) * 100 : 0;
                return `
                    <div style="margin-bottom: 10px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span><strong>${day.day}</strong> (${day.date})</span>
                            <span>₹${day.revenue.toLocaleString()} (${day.count} sales)</span>
                        </div>
                        <div style="background: #e0e0e0; height: 20px; border-radius: 10px; overflow: hidden;">
                            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); height: 100%; width: ${barWidth}%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Export Functions
        function exportSalesReport() {
            const reportData = {
                generatedDate: new Date().toLocaleDateString(),
                totalSales: sales.length,
                totalRevenue: sales.reduce((sum, sale) => sum + sale.total, 0),
                totalProfit: sales.reduce((sum, sale) => sum + sale.profit, 0),
                sales: sales
            };

            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `sales-report-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);

            alert('✅ Sales report exported successfully!');
        }

        function exportInventoryReport() {
            const reportData = {
                generatedDate: new Date().toLocaleDateString(),
                totalMobiles: mobiles.length,
                totalStock: mobiles.reduce((sum, mobile) => sum + mobile.stock, 0),
                lowStockItems: mobiles.filter(mobile => mobile.stock <= 5).length,
                inventory: mobiles
            };

            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `inventory-report-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);

            alert('✅ Inventory report exported successfully!');
        }

        function exportCustomerReport() {
            const reportData = {
                generatedDate: new Date().toLocaleDateString(),
                totalCustomers: customers.length,
                customers: customers
            };

            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `customer-report-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);

            alert('✅ Customer report exported successfully!');
        }

        function printReport() {
            const printWindow = window.open('', '_blank');
            const reportContent = `
                <html>
                <head>
                    <title>Mobile Shop Summary Report</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        h1, h2 { color: #333; }
                        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
                        .stat-box { border: 1px solid #ddd; padding: 15px; text-align: center; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    <h1>📱 Mobile Shop Summary Report</h1>
                    <p>Generated on: ${new Date().toLocaleDateString()}</p>

                    <div class="stats">
                        <div class="stat-box">
                            <h3>${mobiles.length}</h3>
                            <p>Total Mobiles</p>
                        </div>
                        <div class="stat-box">
                            <h3>${customers.length}</h3>
                            <p>Total Customers</p>
                        </div>
                        <div class="stat-box">
                            <h3>${sales.length}</h3>
                            <p>Total Sales</p>
                        </div>
                        <div class="stat-box">
                            <h3>₹${sales.reduce((sum, sale) => sum + sale.total, 0).toLocaleString()}</h3>
                            <p>Total Revenue</p>
                        </div>
                    </div>

                    <h2>Low Stock Alert</h2>
                    <table>
                        <tr><th>Brand</th><th>Model</th><th>Stock</th></tr>
                        ${mobiles.filter(m => m.stock <= 5).map(mobile =>
                            `<tr><td>${mobile.brand}</td><td>${mobile.model}</td><td>${mobile.stock}</td></tr>`
                        ).join('')}
                    </table>
                </body>
                </html>
            `;

            printWindow.document.write(reportContent);
            printWindow.document.close();
            printWindow.print();
        }

        // Settings & Data Management Functions
        function exportAllData() {
            const allData = {
                exportDate: new Date().toISOString(),
                version: '1.0.0',
                mobiles: mobiles,
                customers: customers,
                sales: sales,
                stockHistory: stockHistory
            };

            const dataStr = JSON.stringify(allData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `mobile-shop-backup-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);

            alert('✅ All data exported successfully!');
        }

        function exportMobilesData() {
            const mobileData = {
                exportDate: new Date().toISOString(),
                type: 'mobiles',
                data: mobiles
            };

            const dataStr = JSON.stringify(mobileData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `mobiles-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);

            alert('✅ Mobiles data exported successfully!');
        }

        function exportSalesData() {
            const salesData = {
                exportDate: new Date().toISOString(),
                type: 'sales',
                data: sales
            };

            const dataStr = JSON.stringify(salesData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `sales-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);

            alert('✅ Sales data exported successfully!');
        }

        function importData() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) {
                alert('❌ Please select a file to import!');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);

                    if (importedData.version || importedData.mobiles) {
                        // Full backup file
                        if (confirm('This will replace all existing data. Are you sure?')) {
                            if (importedData.mobiles) mobiles = importedData.mobiles;
                            if (importedData.customers) customers = importedData.customers;
                            if (importedData.sales) sales = importedData.sales;
                            if (importedData.stockHistory) stockHistory = importedData.stockHistory;

                            // Save to localStorage
                            localStorage.setItem('mobiles', JSON.stringify(mobiles));
                            localStorage.setItem('customers', JSON.stringify(customers));
                            localStorage.setItem('sales', JSON.stringify(sales));
                            localStorage.setItem('stockHistory', JSON.stringify(stockHistory));

                            // Refresh displays
                            displayMobiles();
                            displayCustomers();
                            displaySales();
                            updateDashboard();
                            updateSettingsInfo();

                            alert('✅ Data imported successfully!');
                        }
                    } else if (importedData.type === 'mobiles') {
                        // Mobiles only
                        if (confirm('This will replace all mobile data. Continue?')) {
                            mobiles = importedData.data;
                            localStorage.setItem('mobiles', JSON.stringify(mobiles));
                            displayMobiles();
                            updateDashboard();
                            alert('✅ Mobiles data imported successfully!');
                        }
                    } else if (importedData.type === 'sales') {
                        // Sales only
                        if (confirm('This will replace all sales data. Continue?')) {
                            sales = importedData.data;
                            localStorage.setItem('sales', JSON.stringify(sales));
                            displaySales();
                            updateDashboard();
                            alert('✅ Sales data imported successfully!');
                        }
                    } else {
                        alert('❌ Invalid file format!');
                    }
                } catch (error) {
                    alert('❌ Error reading file: ' + error.message);
                }
            };

            reader.readAsText(file);
        }

        function clearAllData() {
            if (confirm('⚠️ This will delete ALL data permanently. Are you absolutely sure?')) {
                if (confirm('🚨 Last warning! This action cannot be undone. Continue?')) {
                    localStorage.clear();
                    mobiles = [];
                    customers = [];
                    sales = [];
                    stockHistory = [];

                    displayMobiles();
                    displayCustomers();
                    displaySales();
                    updateDashboard();
                    updateSettingsInfo();

                    alert('✅ All data cleared successfully!');
                }
            }
        }

        function refreshData() {
            displayMobiles();
            displayCustomers();
            displaySales();
            updateDashboard();
            updateSettingsInfo();
            alert('✅ Data refreshed successfully!');
        }

        function validateData() {
            let issues = [];

            // Check for duplicate mobile IDs
            const mobileIds = mobiles.map(m => m.id);
            if (mobileIds.length !== new Set(mobileIds).size) {
                issues.push('Duplicate mobile IDs found');
            }

            // Check for negative stock
            const negativeStock = mobiles.filter(m => m.stock < 0);
            if (negativeStock.length > 0) {
                issues.push(`${negativeStock.length} mobiles with negative stock`);
            }

            // Check for invalid prices
            const invalidPrices = mobiles.filter(m => m.price <= 0);
            if (invalidPrices.length > 0) {
                issues.push(`${invalidPrices.length} mobiles with invalid prices`);
            }

            if (issues.length === 0) {
                alert('✅ Data validation passed! No issues found.');
            } else {
                alert('⚠️ Data validation issues found:\n\n' + issues.join('\n'));
            }
        }

        function compactData() {
            // Remove any undefined or null entries
            mobiles = mobiles.filter(m => m && m.id);
            customers = customers.filter(c => c && c.id);
            sales = sales.filter(s => s && s.id);
            stockHistory = stockHistory.filter(sh => sh && sh.id);

            // Save compacted data
            localStorage.setItem('mobiles', JSON.stringify(mobiles));
            localStorage.setItem('customers', JSON.stringify(customers));
            localStorage.setItem('sales', JSON.stringify(sales));
            localStorage.setItem('stockHistory', JSON.stringify(stockHistory));

            updateSettingsInfo();
            alert('✅ Data compacted successfully!');
        }

        function showDataSummary() {
            const totalMobiles = mobiles.length;
            const totalCustomers = customers.length;
            const totalSales = sales.length;
            const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);
            const totalProfit = sales.reduce((sum, sale) => sum + sale.profit, 0);
            const lowStockCount = mobiles.filter(m => m.stock <= 5).length;

            const summary = `
                📊 DATA SUMMARY REPORT
                =====================

                📱 INVENTORY:
                • Total Mobiles: ${totalMobiles}
                • Low Stock Items: ${lowStockCount}
                • Total Stock Value: ₹${mobiles.reduce((sum, m) => sum + (m.price * m.stock), 0).toLocaleString()}

                👥 CUSTOMERS:
                • Total Customers: ${totalCustomers}

                💰 SALES:
                • Total Sales: ${totalSales}
                • Total Revenue: ₹${totalRevenue.toLocaleString()}
                • Total Profit: ₹${totalProfit.toLocaleString()}
                • Average Sale: ₹${totalSales > 0 ? Math.round(totalRevenue / totalSales).toLocaleString() : 0}

                📈 TOP PERFORMERS:
                ${getTopPerformers()}

                Generated: ${new Date().toLocaleString()}
            `;

            alert(summary);
        }

        function getTopPerformers() {
            if (sales.length === 0) return '• No sales data available';

            const mobileStats = {};
            sales.forEach(sale => {
                const key = `${sale.mobileBrand} ${sale.mobileModel}`;
                if (!mobileStats[key]) {
                    mobileStats[key] = { quantity: 0, revenue: 0 };
                }
                mobileStats[key].quantity += sale.quantity;
                mobileStats[key].revenue += sale.total;
            });

            const topMobile = Object.entries(mobileStats)
                .sort((a, b) => b[1].quantity - a[1].quantity)[0];

            return `• Best Seller: ${topMobile[0]} (${topMobile[1].quantity} units)`;
        }

        function updateSettingsInfo() {
            document.getElementById('settingsTotalMobiles').textContent = mobiles.length;
            document.getElementById('settingsTotalCustomers').textContent = customers.length;
            document.getElementById('settingsTotalSales').textContent = sales.length;

            // Calculate data size
            const dataSize = JSON.stringify({mobiles, customers, sales, stockHistory}).length;
            document.getElementById('dataSize').textContent = `${Math.round(dataSize / 1024)} KB`;

            // Last updated
            document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
        }

        // Initialize dashboard on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateDashboard();
            displayMobiles();
            updateBrandFilter();
            displayCustomers();
            displaySales();
            updateSaleMobileOptions();
            updateSaleBrandFilter();

            // Setup form submissions
            document.getElementById('addMobileForm').onsubmit = function(e) {
                e.preventDefault();
                addMobile();
            };

            document.getElementById('addCustomerForm').onsubmit = function(e) {
                e.preventDefault();
                addCustomer();
            };

            document.getElementById('recordSaleForm').onsubmit = function(e) {
                e.preventDefault();
                recordSale();
            };

            document.getElementById('updateStockForm').onsubmit = function(e) {
                e.preventDefault();
                updateStock();
            };
        });
    </script>
</body>
</html>
