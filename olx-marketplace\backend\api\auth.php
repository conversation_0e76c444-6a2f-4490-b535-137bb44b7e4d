<?php
/**
 * Authentication API Endpoints
 * OLX Marketplace Backend
 */

header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';
require_once '../classes/User.php';

$database = new Database();
$db = $database->getConnection();
$user = new User($db);

$action = isset($_GET['action']) ? $_GET['action'] : '';

switch($action) {
    case 'register':
        register();
        break;
    case 'login':
        login();
        break;
    case 'logout':
        logout();
        break;
    case 'verify-email':
        verifyEmail();
        break;
    case 'forgot-password':
        forgotPassword();
        break;
    case 'reset-password':
        resetPassword();
        break;
    case 'check-session':
        checkSession();
        break;
    case 'update-profile':
        updateProfile();
        break;
    case 'change-password':
        changePassword();
        break;
    default:
        http_response_code(400);
        echo json_encode(array("message" => "Invalid action"));
        break;
}

function register() {
    global $user;
    
    $data = json_decode(file_get_contents("php://input"));
    
    if(!$data) {
        http_response_code(400);
        echo json_encode(array("message" => "Invalid JSON data"));
        return;
    }

    // Validate input
    $validation_errors = User::validateRegistration((array)$data);
    if(!empty($validation_errors)) {
        http_response_code(400);
        echo json_encode(array("message" => "Validation failed", "errors" => $validation_errors));
        return;
    }

    // Check if email already exists
    $user->email = $data->email;
    if($user->emailExists()) {
        http_response_code(400);
        echo json_encode(array("message" => "Email already exists"));
        return;
    }

    // Check if username already exists
    $user->username = $data->username;
    if($user->usernameExists()) {
        http_response_code(400);
        echo json_encode(array("message" => "Username already exists"));
        return;
    }

    // Set user properties
    $user->username = $data->username;
    $user->email = $data->email;
    $user->password = $data->password;
    $user->full_name = $data->full_name;
    $user->phone = isset($data->phone) ? $data->phone : '';
    $user->location = isset($data->location) ? $data->location : '';

    // Register user
    if($user->register()) {
        // Send verification email (implement email sending)
        sendVerificationEmail($user->email, $user->full_name, $user->verification_token);
        
        http_response_code(201);
        echo json_encode(array(
            "message" => "User registered successfully. Please check your email for verification.",
            "user_id" => $user->id
        ));
    } else {
        http_response_code(500);
        echo json_encode(array("message" => "Registration failed"));
    }
}

function login() {
    global $user;
    
    $data = json_decode(file_get_contents("php://input"));
    
    if(!$data || empty($data->email) || empty($data->password)) {
        http_response_code(400);
        echo json_encode(array("message" => "Email and password are required"));
        return;
    }

    $user->email = $data->email;
    $user->password = $data->password;

    if($user->login()) {
        // Start session
        session_start();
        $_SESSION['user_id'] = $user->id;
        $_SESSION['username'] = $user->username;
        $_SESSION['email'] = $user->email;
        $_SESSION['full_name'] = $user->full_name;
        $_SESSION['is_admin'] = $user->is_admin;
        $_SESSION['is_verified'] = $user->is_verified;
        $_SESSION['login_time'] = time();

        http_response_code(200);
        echo json_encode(array(
            "message" => "Login successful",
            "user" => array(
                "id" => $user->id,
                "username" => $user->username,
                "email" => $user->email,
                "full_name" => $user->full_name,
                "phone" => $user->phone,
                "avatar" => $user->avatar,
                "location" => $user->location,
                "is_verified" => $user->is_verified,
                "is_admin" => $user->is_admin
            )
        ));
    } else {
        http_response_code(401);
        echo json_encode(array("message" => "Invalid email or password"));
    }
}

function logout() {
    session_start();
    session_destroy();
    
    http_response_code(200);
    echo json_encode(array("message" => "Logout successful"));
}

function verifyEmail() {
    global $user;
    
    $token = isset($_GET['token']) ? $_GET['token'] : '';
    
    if(empty($token)) {
        http_response_code(400);
        echo json_encode(array("message" => "Verification token is required"));
        return;
    }

    if($user->verifyEmail($token)) {
        http_response_code(200);
        echo json_encode(array("message" => "Email verified successfully"));
    } else {
        http_response_code(400);
        echo json_encode(array("message" => "Invalid or expired verification token"));
    }
}

function forgotPassword() {
    global $user;
    
    $data = json_decode(file_get_contents("php://input"));
    
    if(!$data || empty($data->email)) {
        http_response_code(400);
        echo json_encode(array("message" => "Email is required"));
        return;
    }

    $user->email = $data->email;
    
    if(!$user->emailExists()) {
        http_response_code(404);
        echo json_encode(array("message" => "Email not found"));
        return;
    }

    $reset_token = $user->generateResetToken();
    
    if($reset_token) {
        // Send password reset email
        sendPasswordResetEmail($user->email, $reset_token);
        
        http_response_code(200);
        echo json_encode(array("message" => "Password reset email sent"));
    } else {
        http_response_code(500);
        echo json_encode(array("message" => "Failed to generate reset token"));
    }
}

function resetPassword() {
    global $user;
    
    $data = json_decode(file_get_contents("php://input"));
    
    if(!$data || empty($data->token) || empty($data->password)) {
        http_response_code(400);
        echo json_encode(array("message" => "Token and new password are required"));
        return;
    }

    if(strlen($data->password) < 8) {
        http_response_code(400);
        echo json_encode(array("message" => "Password must be at least 8 characters long"));
        return;
    }

    if($user->resetPassword($data->token, $data->password)) {
        http_response_code(200);
        echo json_encode(array("message" => "Password reset successful"));
    } else {
        http_response_code(400);
        echo json_encode(array("message" => "Invalid or expired reset token"));
    }
}

function checkSession() {
    session_start();
    
    if(isset($_SESSION['user_id']) && isset($_SESSION['login_time'])) {
        // Check if session is still valid (1 hour timeout)
        if(time() - $_SESSION['login_time'] < SESSION_TIMEOUT) {
            http_response_code(200);
            echo json_encode(array(
                "authenticated" => true,
                "user" => array(
                    "id" => $_SESSION['user_id'],
                    "username" => $_SESSION['username'],
                    "email" => $_SESSION['email'],
                    "full_name" => $_SESSION['full_name'],
                    "is_admin" => $_SESSION['is_admin'],
                    "is_verified" => $_SESSION['is_verified']
                )
            ));
        } else {
            // Session expired
            session_destroy();
            http_response_code(401);
            echo json_encode(array("authenticated" => false, "message" => "Session expired"));
        }
    } else {
        http_response_code(401);
        echo json_encode(array("authenticated" => false, "message" => "Not authenticated"));
    }
}

function updateProfile() {
    global $user;
    
    session_start();
    if(!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(array("message" => "Authentication required"));
        return;
    }

    $data = json_decode(file_get_contents("php://input"));
    
    if(!$data) {
        http_response_code(400);
        echo json_encode(array("message" => "Invalid JSON data"));
        return;
    }

    $user->id = $_SESSION['user_id'];
    $user->full_name = $data->full_name;
    $user->phone = isset($data->phone) ? $data->phone : '';
    $user->location = isset($data->location) ? $data->location : '';
    $user->avatar = isset($data->avatar) ? $data->avatar : '';

    if($user->updateProfile()) {
        // Update session data
        $_SESSION['full_name'] = $user->full_name;
        
        http_response_code(200);
        echo json_encode(array("message" => "Profile updated successfully"));
    } else {
        http_response_code(500);
        echo json_encode(array("message" => "Profile update failed"));
    }
}

function changePassword() {
    global $user;
    
    session_start();
    if(!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(array("message" => "Authentication required"));
        return;
    }

    $data = json_decode(file_get_contents("php://input"));
    
    if(!$data || empty($data->current_password) || empty($data->new_password)) {
        http_response_code(400);
        echo json_encode(array("message" => "Current password and new password are required"));
        return;
    }

    if(strlen($data->new_password) < 8) {
        http_response_code(400);
        echo json_encode(array("message" => "New password must be at least 8 characters long"));
        return;
    }

    $user->id = $_SESSION['user_id'];

    if($user->changePassword($data->current_password, $data->new_password)) {
        http_response_code(200);
        echo json_encode(array("message" => "Password changed successfully"));
    } else {
        http_response_code(400);
        echo json_encode(array("message" => "Current password is incorrect"));
    }
}

// Email functions (implement with your preferred email service)
function sendVerificationEmail($email, $full_name, $token) {
    $verification_link = SITE_URL . "/verify-email.php?token=" . $token;
    
    // Implement email sending logic here
    // You can use PHPMailer, SendGrid, or any other email service
    
    return true; // Return true if email sent successfully
}

function sendPasswordResetEmail($email, $token) {
    $reset_link = SITE_URL . "/reset-password.php?token=" . $token;
    
    // Implement email sending logic here
    
    return true; // Return true if email sent successfully
}
?>
