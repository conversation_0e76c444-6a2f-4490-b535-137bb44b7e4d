<?php
require_once 'config.php';

setApiHeaders();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo apiResponse(false, 'Method not allowed');
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo apiResponse(false, 'Invalid JSON input');
        exit();
    }
    
    // Validate required fields
    if (!isset($input['domain']) || !isset($input['email']) || !isset($input['certificates'])) {
        echo apiResponse(false, 'Missing required fields');
        exit();
    }
    
    $domain = trim($input['domain']);
    $email = trim($input['email']);
    $certificates = $input['certificates'];
    
    // Validate email
    if (!validateEmail($email)) {
        echo apiResponse(false, 'Invalid email address');
        exit();
    }
    
    // Validate certificates data
    if (!isset($certificates['certificate']) || !isset($certificates['private_key']) || !isset($certificates['ca_bundle'])) {
        echo apiResponse(false, 'Invalid certificate data');
        exit();
    }
    
    logMessage("Sending certificates via email for domain: $domain to $email", 'INFO');
    
    // Create email content
    $emailSubject = "SSL Certificate for $domain - SSL4Free";
    $emailBody = createEmailBody($domain, $certificates);
    
    // Create certificate files as attachments
    $attachments = createCertificateAttachments($domain, $certificates);
    
    // Send email
    $emailSent = sendCertificateEmail($email, $emailSubject, $emailBody, $attachments);
    
    // Clean up temporary attachment files
    foreach ($attachments as $attachment) {
        if (file_exists($attachment['path'])) {
            unlink($attachment['path']);
        }
    }
    
    if ($emailSent) {
        logMessage("Certificates successfully emailed to: $email for domain: $domain", 'INFO');
        echo apiResponse(true, 'Certificates sent to your email successfully!');
    } else {
        logMessage("Failed to send certificates email to: $email for domain: $domain", 'ERROR');
        echo apiResponse(false, 'Failed to send email. Please try downloading the certificates instead.');
    }
    
} catch (Exception $e) {
    logMessage("Error in email-certificates: " . $e->getMessage(), 'ERROR');
    echo apiResponse(false, 'Internal server error: ' . $e->getMessage());
}

function createEmailBody($domain, $certificates) {
    $validFrom = date('Y-m-d H:i:s');
    $validTo = date('Y-m-d H:i:s', strtotime('+90 days'));
    
    $body = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
            .certificate-info { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #10b981; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
            .button { display: inline-block; background: #10b981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
            .urdu { font-family: 'Noto Nastaliq Urdu', serif; direction: rtl; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🔒 SSL4Free</h1>
                <h2>SSL Certificate Generated Successfully!</h2>
                <p class='urdu'>SSL سرٹیفکیٹ کامیابی سے بن گیا!</p>
            </div>
            
            <div class='content'>
                <h3>Dear User,</h3>
                <p>Your SSL certificate for <strong>$domain</strong> has been generated successfully using ZeroSSL API.</p>
                <p class='urdu'>آپ کے ڈومین <strong>$domain</strong> کے لیے SSL سرٹیفکیٹ کامیابی سے بن گیا ہے۔</p>
                
                <div class='certificate-info'>
                    <h4>Certificate Details:</h4>
                    <ul>
                        <li><strong>Domain:</strong> $domain</li>
                        <li><strong>Valid From:</strong> $validFrom</li>
                        <li><strong>Valid To:</strong> $validTo</li>
                        <li><strong>Issuer:</strong> ZeroSSL</li>
                        <li><strong>Type:</strong> Domain Validated (DV)</li>
                    </ul>
                </div>
                
                <h4>Attached Files:</h4>
                <ul>
                    <li><strong>certificate.crt</strong> - Your SSL certificate</li>
                    <li><strong>private.key</strong> - Private key (keep this secure!)</li>
                    <li><strong>ca_bundle.crt</strong> - Certificate Authority bundle</li>
                </ul>
                
                <div style='background: #fef3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>
                    <h4>⚠️ Important Security Notice:</h4>
                    <p>Keep your private key file secure and never share it publicly. This file is required for your SSL certificate to work.</p>
                    <p class='urdu'>اپنی پرائیویٹ کی فائل کو محفوظ رکھیں اور کبھی بھی عوامی طور پر شیئر نہ کریں۔</p>
                </div>
                
                <h4>Installation Help:</h4>
                <p>Need help installing your SSL certificate? Visit our help center or contact support.</p>
                
                <div style='text-align: center; margin: 20px 0;'>
                    <a href='#' class='button'>Visit SSL4Free</a>
                    <a href='#' class='button'>Installation Guide</a>
                </div>
                
                <p>Thank you for using SSL4Free!</p>
                <p class='urdu'>SSL4Free استعمال کرنے کا شکریہ!</p>
            </div>
            
            <div class='footer'>
                <p>&copy; 2024 SSL4Free. All rights reserved.</p>
                <p>Powered by ZeroSSL API | From Waji with ❤️</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    return $body;
}

function createCertificateAttachments($domain, $certificates) {
    $attachments = [];
    $tempDir = TEMP_DIR . 'email_' . uniqid() . '/';
    
    if (!file_exists($tempDir)) {
        mkdir($tempDir, 0755, true);
    }
    
    // Create certificate file
    $certFile = $tempDir . 'certificate.crt';
    file_put_contents($certFile, $certificates['certificate']);
    $attachments[] = [
        'path' => $certFile,
        'name' => 'certificate.crt',
        'type' => 'application/x-x509-ca-cert'
    ];
    
    // Create private key file
    $keyFile = $tempDir . 'private.key';
    file_put_contents($keyFile, $certificates['private_key']);
    $attachments[] = [
        'path' => $keyFile,
        'name' => 'private.key',
        'type' => 'application/x-pem-file'
    ];
    
    // Create CA bundle file
    $caFile = $tempDir . 'ca_bundle.crt';
    file_put_contents($caFile, $certificates['ca_bundle']);
    $attachments[] = [
        'path' => $caFile,
        'name' => 'ca_bundle.crt',
        'type' => 'application/x-x509-ca-cert'
    ];
    
    return $attachments;
}

function sendCertificateEmail($to, $subject, $body, $attachments = []) {
    try {
        // For production, use PHPMailer or similar library
        // This is a simplified version using PHP's mail() function
        
        $boundary = md5(time());
        
        $headers = [
            'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
            'Reply-To: ' . FROM_EMAIL,
            'MIME-Version: 1.0',
            'Content-Type: multipart/mixed; boundary="' . $boundary . '"'
        ];
        
        $message = "--$boundary\r\n";
        $message .= "Content-Type: text/html; charset=UTF-8\r\n";
        $message .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
        $message .= $body . "\r\n\r\n";
        
        // Add attachments
        foreach ($attachments as $attachment) {
            if (file_exists($attachment['path'])) {
                $fileContent = chunk_split(base64_encode(file_get_contents($attachment['path'])));
                
                $message .= "--$boundary\r\n";
                $message .= "Content-Type: " . $attachment['type'] . "; name=\"" . $attachment['name'] . "\"\r\n";
                $message .= "Content-Disposition: attachment; filename=\"" . $attachment['name'] . "\"\r\n";
                $message .= "Content-Transfer-Encoding: base64\r\n\r\n";
                $message .= $fileContent . "\r\n";
            }
        }
        
        $message .= "--$boundary--\r\n";
        
        $success = mail($to, $subject, $message, implode("\r\n", $headers));
        
        if ($success) {
            logMessage("Certificate email sent successfully to: $to", 'INFO');
        } else {
            logMessage("Failed to send certificate email to: $to", 'ERROR');
        }
        
        return $success;
        
    } catch (Exception $e) {
        logMessage("Exception in sendCertificateEmail: " . $e->getMessage(), 'ERROR');
        return false;
    }
}
?>
