@echo off
echo 🎯 CodeCanyon Submission Structure Creator
echo ========================================

set "BASE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_Submission"
set "SOURCE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete"

echo 📁 Creating CodeCanyon submission structure...

:: Create main submission folder
if not exist "%BASE_DIR%" mkdir "%BASE_DIR%"

:: Create Thumbnail folder
echo 📸 Creating Thumbnail folder...
if not exist "%BASE_DIR%\Thumbnail" mkdir "%BASE_DIR%\Thumbnail"

:: Copy and rename first screenshot as main thumbnail (590x300 required)
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\01_General_Settings_Tab.png" "%BASE_DIR%\Thumbnail\01_whatsapp_widget_pro.jpg" >nul

echo 🖼️ Creating Theme Preview folder...
if not exist "%BASE_DIR%\Theme_Preview" mkdir "%BASE_DIR%\Theme_Preview"

:: Copy all screenshots to Theme Preview
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\01_General_Settings_Tab.png" "%BASE_DIR%\Theme_Preview\01_general_settings.jpg" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\02_Agents_Management_Tab.png" "%BASE_DIR%\Theme_Preview\02_agents_management.jpg" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\03_Appearance_Themes_Tab.png" "%BASE_DIR%\Theme_Preview\03_appearance_themes.jpg" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\04_Working_Hours_Tab.png" "%BASE_DIR%\Theme_Preview\04_working_hours.jpg" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\05_Advanced_Settings_Tab.png" "%BASE_DIR%\Theme_Preview\05_advanced_settings.jpg" >nul
copy "%SOURCE_DIR%\WhatsApp_Widget_Screenshots\06_Preview_Tab.png" "%BASE_DIR%\Theme_Preview\06_preview_tab.jpg" >nul

echo 📦 Creating Main File(s) folder...
if not exist "%BASE_DIR%\Main_Files" mkdir "%BASE_DIR%\Main_Files"
if not exist "%BASE_DIR%\Main_Files\whatsapp-widget-pro" mkdir "%BASE_DIR%\Main_Files\whatsapp-widget-pro"

:: Copy all plugin files to Main Files
copy "%SOURCE_DIR%\whatsapp-chat-widget.php" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\style.css" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\script.js" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\admin-style.css" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\admin-script.js" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\readme.txt" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\LICENSE.txt" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul

:: Copy documentation
copy "%SOURCE_DIR%\INSTALLATION-GUIDE.md" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\FAQ.md" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\DEVELOPER-GUIDE.md" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\" >nul

:: Copy demo folder
if not exist "%BASE_DIR%\Main_Files\whatsapp-widget-pro\demo" mkdir "%BASE_DIR%\Main_Files\whatsapp-widget-pro\demo"
copy "%SOURCE_DIR%\demo\index.html" "%BASE_DIR%\Main_Files\whatsapp-widget-pro\demo\" >nul

echo 🌐 Creating WordPress Theme folder...
if not exist "%BASE_DIR%\WordPress_Theme" mkdir "%BASE_DIR%\WordPress_Theme"

:: Create a ZIP file of the plugin for WordPress installation
echo 📦 Creating WordPress plugin ZIP...
powershell -Command "Compress-Archive -Path '%BASE_DIR%\Main_Files\whatsapp-widget-pro\*' -DestinationPath '%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -Force"

echo ✅ CodeCanyon submission structure created!
echo.
echo 📁 Structure created at: %BASE_DIR%
echo.
echo 📋 Folder Contents:
echo    📸 Thumbnail\ - Main preview image (590x300)
echo    🖼️ Theme_Preview\ - All screenshots (6 images)
echo    📦 Main_Files\ - Complete plugin source code
echo    🌐 WordPress_Theme\ - Installable ZIP file
echo.
echo 🎯 Ready for CodeCanyon submission!
pause
