<?php
// Debug version to catch errors
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in output
ini_set('log_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Check for JSON errors
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'success' => false, 
            'message' => 'Invalid JSON: ' . json_last_error_msg()
        ]);
        exit();
    }
    
    // Validate input
    if (!$input || !isset($input['domain']) || !isset($input['email'])) {
        echo json_encode([
            'success' => false, 
            'message' => 'Missing required fields: domain and email'
        ]);
        exit();
    }
    
    $domain = htmlspecialchars(trim($input['domain']));
    $email = filter_var(trim($input['email']), FILTER_VALIDATE_EMAIL);
    
    if (!$email) {
        echo json_encode([
            'success' => false, 
            'message' => 'Invalid email address'
        ]);
        exit();
    }
    
    // Simple domain validation
    $domain = preg_replace('/^https?:\/\//', '', $domain);
    $domain = rtrim($domain, '/');
    
    if (!preg_match('/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/', $domain)) {
        echo json_encode([
            'success' => false, 
            'message' => 'Invalid domain format'
        ]);
        exit();
    }
    
    // Check if files exist
    $acmeExists = file_exists('acme-client.php');
    $sslExists = file_exists('ssl-validator.php');
    
    if (!$acmeExists || !$sslExists) {
        echo json_encode([
            'success' => false,
            'message' => 'Required files missing',
            'debug' => [
                'acme-client.php' => $acmeExists ? 'exists' : 'missing',
                'ssl-validator.php' => $sslExists ? 'exists' : 'missing'
            ]
        ]);
        exit();
    }
    
    // Try to include files
    try {
        require_once 'acme-client.php';
        require_once 'ssl-validator.php';
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error loading required files: ' . $e->getMessage()
        ]);
        exit();
    }
    
    // Check if classes exist
    if (!class_exists('ACMEClient')) {
        echo json_encode([
            'success' => false,
            'message' => 'ACMEClient class not found'
        ]);
        exit();
    }
    
    if (!class_exists('SSLValidator')) {
        echo json_encode([
            'success' => false,
            'message' => 'SSLValidator class not found'
        ]);
        exit();
    }
    
    // Try to create ACME client
    $useStaging = isset($input['staging']) ? $input['staging'] : true;
    
    try {
        $acmeClient = new ACMEClient($useStaging);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error creating ACME client: ' . $e->getMessage()
        ]);
        exit();
    }
    
    // For now, return success with debug info
    echo json_encode([
        'success' => true,
        'message' => 'Debug: All checks passed',
        'debug' => [
            'domain' => $domain,
            'email' => $email,
            'staging' => $useStaging,
            'acme_client_created' => true,
            'files_loaded' => true
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Unexpected error: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'message' => 'PHP Error: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
