#!/usr/bin/env python3
"""
CodeCanyon Complete Auto-Fixer
Fixes all submission issues automatically
"""

import os
import shutil
import zipfile
from PIL import Image, ImageDraw, ImageFont
import sys

def create_directories():
    """Create the required directory structure"""
    base_dir = r"C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_FINAL"
    
    # Remove existing directory if it exists
    if os.path.exists(base_dir):
        shutil.rmtree(base_dir)
    
    # Create new structure
    os.makedirs(f"{base_dir}/Thumbnail", exist_ok=True)
    os.makedirs(f"{base_dir}/Theme_Preview", exist_ok=True) 
    os.makedirs(f"{base_dir}/WordPress_Theme", exist_ok=True)
    
    print(f"✅ Created directory structure: {base_dir}")
    return base_dir

def create_thumbnail(base_dir):
    """Create proper 80x80 thumbnail under 50KB"""
    try:
        # Create 80x80 image with WhatsApp green background
        img = Image.new('RGB', (80, 80), color=(37, 211, 102))
        draw = ImageDraw.Draw(img)
        
        # Add white text
        try:
            font = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()
        
        # Draw text
        draw.text((22, 20), "WA", fill=(255, 255, 255), font=font)
        draw.text((18, 40), "Pro", fill=(255, 255, 255), font=font)
        
        # Save as PNG with optimization
        thumbnail_path = f"{base_dir}/Thumbnail/thumbnail.png"
        img.save(thumbnail_path, "PNG", optimize=True, quality=85)
        
        # Check file size
        file_size = os.path.getsize(thumbnail_path)
        size_kb = file_size / 1024
        
        print(f"✅ Thumbnail created: {thumbnail_path}")
        print(f"   Size: {size_kb:.1f} KB")
        
        if size_kb > 50:
            print("⚠️ Thumbnail over 50KB, optimizing...")
            img.save(thumbnail_path, "PNG", optimize=True, quality=50)
            
        return True
    except Exception as e:
        print(f"❌ Error creating thumbnail: {e}")
        return False

def convert_screenshots(base_dir):
    """Convert PNG screenshots to JPG format"""
    source_dir = r"C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete\WhatsApp_Widget_Screenshots"
    
    screenshot_mappings = {
        "01_General_Settings_Tab.png": "01_general_settings.jpg",
        "02_Agents_Management_Tab.png": "02_agents_management.jpg", 
        "03_Appearance_Themes_Tab.png": "03_appearance_themes.jpg",
        "04_Working_Hours_Tab.png": "04_working_hours.jpg",
        "05_Advanced_Settings_Tab.png": "05_advanced_settings.jpg",
        "06_Preview_Tab.png": "06_preview_tab.jpg"
    }
    
    converted_count = 0
    
    for png_file, jpg_file in screenshot_mappings.items():
        source_path = f"{source_dir}/{png_file}"
        dest_path = f"{base_dir}/Theme_Preview/{jpg_file}"
        
        if os.path.exists(source_path):
            try:
                # Open PNG and convert to JPG
                with Image.open(source_path) as img:
                    # Convert to RGB if necessary
                    if img.mode in ('RGBA', 'LA', 'P'):
                        rgb_img = Image.new('RGB', img.size, (255, 255, 255))
                        rgb_img.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                        img = rgb_img
                    
                    # Save as JPG with good quality
                    img.save(dest_path, "JPEG", quality=85, optimize=True)
                    
                print(f"✅ Converted: {png_file} → {jpg_file}")
                converted_count += 1
                
            except Exception as e:
                print(f"❌ Error converting {png_file}: {e}")
        else:
            print(f"⚠️ Source file not found: {source_path}")
    
    print(f"✅ Converted {converted_count} screenshots")
    return converted_count > 0

def create_wordpress_zip(base_dir):
    """Create proper WordPress plugin ZIP structure"""
    source_dir = r"C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete"
    
    # Create temporary plugin directory
    temp_plugin_dir = f"{base_dir}/temp_plugin/whatsapp-widget-pro"
    os.makedirs(temp_plugin_dir, exist_ok=True)
    
    # Files to copy
    files_to_copy = [
        "whatsapp-chat-widget.php",
        "style.css", 
        "script.js",
        "admin-style.css",
        "admin-script.js",
        "readme.txt",
        "LICENSE.txt",
        "INSTALLATION-GUIDE.md",
        "FAQ.md",
        "DEVELOPER-GUIDE.md"
    ]
    
    copied_files = 0
    
    # Copy individual files
    for file_name in files_to_copy:
        source_path = f"{source_dir}/{file_name}"
        dest_path = f"{temp_plugin_dir}/{file_name}"
        
        if os.path.exists(source_path):
            shutil.copy2(source_path, dest_path)
            copied_files += 1
            print(f"✅ Copied: {file_name}")
        else:
            print(f"⚠️ File not found: {file_name}")
    
    # Copy demo folder
    demo_source = f"{source_dir}/demo"
    demo_dest = f"{temp_plugin_dir}/demo"
    
    if os.path.exists(demo_source):
        shutil.copytree(demo_source, demo_dest)
        print("✅ Copied: demo folder")
    
    # Create ZIP with proper structure
    zip_path = f"{base_dir}/WordPress_Theme/whatsapp-widget-pro.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Add all files maintaining the folder structure
        for root, dirs, files in os.walk(f"{base_dir}/temp_plugin"):
            for file in files:
                file_path = os.path.join(root, file)
                # Create archive path relative to temp_plugin
                archive_path = os.path.relpath(file_path, f"{base_dir}/temp_plugin")
                zipf.write(file_path, archive_path)
    
    # Clean up temp directory
    shutil.rmtree(f"{base_dir}/temp_plugin")
    
    print(f"✅ WordPress ZIP created: {zip_path}")
    print(f"   Files included: {copied_files}")
    
    return True

def main():
    """Main function to fix all CodeCanyon issues"""
    print("🔧 CodeCanyon Complete Auto-Fixer")
    print("=" * 40)
    
    try:
        # Step 1: Create directories
        base_dir = create_directories()
        
        # Step 2: Create thumbnail
        print("\n📸 Creating thumbnail...")
        create_thumbnail(base_dir)
        
        # Step 3: Convert screenshots
        print("\n🎨 Converting screenshots...")
        convert_screenshots(base_dir)
        
        # Step 4: Create WordPress ZIP
        print("\n📦 Creating WordPress ZIP...")
        create_wordpress_zip(base_dir)
        
        print("\n🎉 ALL CODECANYON ISSUES FIXED!")
        print(f"📁 Location: {base_dir}")
        
        print("\n📋 What's been fixed:")
        print("   ✅ Thumbnail: 80x80px PNG under 50KB")
        print("   ✅ Screenshots: All converted to JPG format")
        print("   ✅ WordPress ZIP: Proper folder structure")
        print("   ✅ File naming: All correct extensions")
        
        print("\n⚠️ FORM SETTINGS TO SET:")
        print("   ✅ Compatible Browsers: Select ALL")
        print("   ✅ ThemeForest Files: CSS, JS, PHP")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    main()
    input("\nPress Enter to continue...")
