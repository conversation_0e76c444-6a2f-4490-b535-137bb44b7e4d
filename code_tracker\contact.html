<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - PakSim | Get Support & Premium Services</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Contact PakSim for support, premium services, or business inquiries. Get in touch with Pakistan's leading SIM data service provider.">
    <meta name="keywords" content="contact paksim, sim data support, premium sim services, pakistan sim data, contact us">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/new-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <i class="fas fa-database" style="font-size: 2rem; color: var(--primary-color);"></i>
                    <span class="logo-text">PakSim</span>
                </a>

                <ul class="nav-menu">
                    <li class="nav-item"><a href="index.html" class="nav-link">Homepage</a></li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link">SIM DATA <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="sim-owner-details.html">SIM Owner Details</a></li>
                            <li><a href="sim-data-online.html">SIM DATA ONLINE</a></li>
                            <li><a href="live-tracker.html">Live Tracker</a></li>
                        </ul>
                    </li>
                    <li class="nav-item"><a href="about.html" class="nav-link">About Us</a></li>
                    <li class="nav-item"><a href="faq.html" class="nav-link">FAQ</a></li>
                    <li class="nav-item"><a href="blog.html" class="nav-link">Blog</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-link" style="color: var(--primary-color);">Contact Us</a></li>
                </ul>
                
                <div class="nav-actions">
                    <a href="contact.html" class="btn btn-primary">Get Started</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- AdSense Ad - Top Banner -->
    <section style="padding: 20px 0; background: #f8f9fa; text-align: center;">
        <div class="container">
            <!-- Replace this div with your AdSense code -->
            <div style="background: #e9ecef; border: 2px dashed #6c757d; padding: 60px 20px; border-radius: 10px; color: #6c757d;">
                <i class="fas fa-ad" style="font-size: 2rem; margin-bottom: 10px;"></i>
                <p style="margin: 0; font-size: 1.1rem; font-weight: 500;">AdSense Banner Ad (728x90 or Responsive)</p>
                <small style="opacity: 0.7;">Paste your AdSense code here</small>
            </div>
        </div>
    </section>

    <!-- Page Header -->
    <section class="page-header" style="background: var(--gradient-primary); color: #fff; padding: 60px 0; text-align: center;">
        <div class="container">
            <h1 style="font-size: 3rem; margin-bottom: 20px;">Contact Us</h1>
            <p style="font-size: 1.2rem; opacity: 0.9;">Get in touch with our team for support or premium services</p>
        </div>
    </section>

    <!-- Contact Section -->
    <section style="padding: 80px 0;">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 60px; align-items: start;">
                <!-- Contact Form -->
                <div>
                    <h2 style="font-size: 2.5rem; color: var(--dark-color); margin-bottom: 30px;">Send Us a Message</h2>
                    <p style="font-size: 1.1rem; color: #666; margin-bottom: 30px;">Fill out the form below and we'll get back to you as soon as possible.</p>
                    
                    <form id="contactForm" action="https://formspree.io/f/YOUR_FORM_ID" method="POST" style="margin-top: 30px;">
                        <div style="margin-bottom: 20px;">
                            <label for="name" style="display: block; margin-bottom: 8px; font-weight: 500;">Full Name</label>
                            <input type="text" id="name" name="name" placeholder="Enter your full name" required style="width: 100%; padding: 12px 15px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 1rem;">
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="email" style="display: block; margin-bottom: 8px; font-weight: 500;">Email Address</label>
                            <input type="email" id="email" name="email" placeholder="Enter your email address" required style="width: 100%; padding: 12px 15px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 1rem;">
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="phone" style="display: block; margin-bottom: 8px; font-weight: 500;">Phone Number</label>
                            <input type="tel" id="phone" name="phone" placeholder="Enter your phone number" style="width: 100%; padding: 12px 15px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 1rem;">
                        </div>
                        
                        <div style="margin-bottom: 20px;">
                            <label for="subject" style="display: block; margin-bottom: 8px; font-weight: 500;">Subject</label>
                            <select id="subject" name="subject" required style="width: 100%; padding: 12px 15px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 1rem;">
                                <option value="">Select a subject</option>
                                <option value="General Inquiry">General Inquiry</option>
                                <option value="Premium Services">Premium Services</option>
                                <option value="Technical Support">Technical Support</option>
                                <option value="Business Proposal">Business Proposal</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        
                        <div style="margin-bottom: 30px;">
                            <label for="message" style="display: block; margin-bottom: 8px; font-weight: 500;">Message</label>
                            <textarea id="message" name="message" placeholder="Enter your message" required style="width: 100%; padding: 12px 15px; border: 1px solid #ddd; border-radius: var(--border-radius); font-size: 1rem; min-height: 150px;"></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" id="submitBtn" style="width: 100%; padding: 15px;">
                            <i class="fas fa-paper-plane"></i> Send Message
                        </button>

                        <!-- Success/Error Messages -->
                        <div id="formMessage" style="margin-top: 20px; padding: 15px; border-radius: 5px; display: none;"></div>
                    </form>
                </div>
                
                <!-- Contact Information -->
                <div>
                    <h2 style="font-size: 2.5rem; color: var(--dark-color); margin-bottom: 30px;">Contact Information</h2>
                    <p style="font-size: 1.1rem; color: #666; margin-bottom: 30px;">You can reach us through any of the following contact methods.</p>
                    
                    <div style="margin-bottom: 40px;">
                        <div style="display: flex; align-items: center; margin-bottom: 25px;">
                            <div style="background: var(--primary-color); color: #fff; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px; font-size: 1.2rem;">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <h3 style="font-size: 1.2rem; margin-bottom: 5px; color: var(--dark-color);">Phone</h3>
                                <p style="color: #666;">+92 300 1234567</p>
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; margin-bottom: 25px;">
                            <div style="background: var(--secondary-color); color: #fff; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px; font-size: 1.2rem;">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h3 style="font-size: 1.2rem; margin-bottom: 5px; color: var(--dark-color);">Email</h3>
                                <p style="color: #666;"><EMAIL></p>
                            </div>
                        </div>

                        <div style="display: flex; align-items: center; margin-bottom: 25px;">
                            <div style="background: var(--primary-color); color: #fff; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px; font-size: 1.2rem;">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div>
                                <h3 style="font-size: 1.2rem; margin-bottom: 5px; color: var(--dark-color);">WhatsApp</h3>
                                <p style="color: #666;">+92 300 1234567</p>
                            </div>
                        </div>
                        
                        <div style="display: flex; align-items: center;">
                            <div style="background: var(--secondary-color); color: #fff; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px; font-size: 1.2rem;">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <h3 style="font-size: 1.2rem; margin-bottom: 5px; color: var(--dark-color);">Location</h3>
                                <p style="color: #666;">Pakistan</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Premium Services -->
                    <div style="background: #f8f9fa; padding: 30px; border-radius: var(--border-radius); margin-top: 40px;">
                        <h3 style="font-size: 1.5rem; color: var(--dark-color); margin-bottom: 20px;">Premium Services</h3>
                        <p style="color: #666; margin-bottom: 20px;">Contact us for premium SIM data services including:</p>
                        <ul style="color: #666; padding-left: 20px; margin-bottom: 25px;">
                            <li style="margin-bottom: 10px;">Live SIM Tracking</li>
                            <li style="margin-bottom: 10px;">Detailed Owner Information</li>
                            <li style="margin-bottom: 10px;">Bulk SIM Data Lookup</li>
                            <li style="margin-bottom: 10px;">API Access for Businesses</li>
                            <li>Custom SIM Data Solutions</li>
                        </ul>
                        <a href="https://wa.me/+923001234567" class="btn btn-secondary" style="width: 100%;">
                            <i class="fab fa-whatsapp"></i> Contact on WhatsApp
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- AdSense Ad - After Contact Form Section -->
    <section style="padding: 20px 0; background: #f8f9fa; text-align: center;">
        <div class="container">
            <!-- Replace this div with your AdSense code -->
            <div style="background: #e9ecef; border: 2px dashed #6c757d; padding: 60px 20px; border-radius: 10px; color: #6c757d;">
                <i class="fas fa-ad" style="font-size: 2rem; margin-bottom: 10px;"></i>
                <p style="margin: 0; font-size: 1.1rem; font-weight: 500;">AdSense Banner Ad (728x90 or Responsive)</p>
                <small style="opacity: 0.7;">Paste your AdSense code here</small>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5rem; color: var(--dark-color); margin-bottom: 20px;">Frequently Asked Questions</h2>
                <p style="font-size: 1.1rem; color: #666;">Find answers to common questions about our services</p>
            </div>
            
            <div style="max-width: 800px; margin: 0 auto;">
                <div style="background: #fff; border-radius: var(--border-radius); box-shadow: var(--box-shadow); margin-bottom: 20px; padding: 25px;">
                    <h3 style="color: var(--dark-color); margin-bottom: 15px; font-size: 1.3rem;">How quickly will you respond to my inquiry?</h3>
                    <p style="color: #666; line-height: 1.6;">We typically respond to all inquiries within 24 hours. For urgent matters, please contact us via WhatsApp for faster response.</p>
                </div>
                
                <div style="background: #fff; border-radius: var(--border-radius); box-shadow: var(--box-shadow); margin-bottom: 20px; padding: 25px;">
                    <h3 style="color: var(--dark-color); margin-bottom: 15px; font-size: 1.3rem;">What are your premium services and how much do they cost?</h3>
                    <p style="color: #666; line-height: 1.6;">Our premium services include detailed SIM owner information, live tracking, and bulk lookups. Pricing varies based on your specific needs. Please contact us for a customized quote.</p>
                </div>
                
                <div style="background: #fff; border-radius: var(--border-radius); box-shadow: var(--box-shadow); margin-bottom: 20px; padding: 25px;">
                    <h3 style="color: var(--dark-color); margin-bottom: 15px; font-size: 1.3rem;">Do you offer API access for businesses?</h3>
                    <p style="color: #666; line-height: 1.6;">Yes, we provide API access for businesses that need to integrate SIM data verification into their systems. Contact us for API documentation and pricing.</p>
                </div>
                
                <div style="background: #fff; border-radius: var(--border-radius); box-shadow: var(--box-shadow); padding: 25px;">
                    <h3 style="color: var(--dark-color); margin-bottom: 15px; font-size: 1.3rem;">Is my information secure when I contact you?</h3>
                    <p style="color: #666; line-height: 1.6;">Yes, all information shared with us is kept strictly confidential. We use secure communication channels and do not share your data with third parties.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" style="background: var(--dark-color); color: #fff; padding: 50px 0 20px;">
        <div class="container">
            <div class="footer-content" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 30px;">
                <div class="footer-section">
                    <h3 style="margin-bottom: 20px; color: var(--primary-color);">PAKSIM</h3>
                    <p style="opacity: 0.8; line-height: 1.6; margin-bottom: 20px;">SIM Database | SIM Owner Details | CNIC SIM Check | Pak SIM Data | Live Tracker</p>
                </div>
                
                <div class="footer-section">
                    <h4 style="margin-bottom: 20px;">QUICK LINKS</h4>
                    <ul style="list-style: none;">
                        <li style="margin-bottom: 10px;"><a href="index.html" style="color: #ccc; text-decoration: none;">Homepage</a></li>
                        <li style="margin-bottom: 10px;"><a href="sim-owner-details.html" style="color: #ccc; text-decoration: none;">SIM Owner Details</a></li>
                        <li style="margin-bottom: 10px;"><a href="sim-data-online.html" style="color: #ccc; text-decoration: none;">CNIC SIM Check</a></li>
                        <li style="margin-bottom: 10px;"><a href="live-tracker.html" style="color: #ccc; text-decoration: none;">Live Tracker</a></li>
                        <li style="margin-bottom: 10px;"><a href="about.html" style="color: #ccc; text-decoration: none;">About Us</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 style="margin-bottom: 20px;">CONTACT US</h4>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-phone" style="color: var(--primary-color); margin-right: 10px;"></i>
                        <span>+92 300 1234567</span>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-envelope" style="color: var(--primary-color); margin-right: 10px;"></i>
                        <span><EMAIL></span>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom" style="border-top: 1px solid #444; padding-top: 20px; text-align: center;">
                <p style="opacity: 0.8;">© 2025 PakSim – SIM Database Online & CNIC SIM Information System for Pakistan. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Contact form submission
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value;
            
            // Show loading state
            const submitBtn = document.getElementById('submitBtn');
            const formMessage = document.getElementById('formMessage');

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            submitBtn.disabled = true;

            // Show success message
            setTimeout(() => {
                formMessage.innerHTML = 'Thank you! Your message has been sent successfully. We will get back to you soon.';
                formMessage.style.backgroundColor = '#d4edda';
                formMessage.style.color = '#155724';
                formMessage.style.border = '1px solid #c3e6cb';
                formMessage.style.display = 'block';

                // Reset button and form
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Message';
                submitBtn.disabled = false;

                // Reset form after 2 seconds
                setTimeout(() => {
                    this.reset();
                }, 2000);
            }, 1000)
        });
    </script>
</body>
</html>
