<?php
// Debug version to check what's happening
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in output
ini_set('log_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['domain']) || !isset($input['email'])) {
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
        exit();
    }
    
    $domain = trim($input['domain']);
    $email = trim($input['email']);
    
    // Remove protocol if present
    $domain = preg_replace('/^https?:\/\//', '', $domain);
    $domain = rtrim($domain, '/');
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email address']);
        exit();
    }
    
    // Check if OpenSSL is available
    $opensslAvailable = checkOpenSSLAvailability();
    
    if (!$opensslAvailable) {
        // Generate fallback certificate without OpenSSL
        $result = generateFallbackCertificate($domain, $email);
    } else {
        // Try to generate real certificate
        $result = generateRealSSLCertificate($domain, $email);
    }
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => $opensslAvailable ? 'Real SSL certificate generated' : 'Fallback certificate generated',
            'data' => $result['data']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['error']
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

function checkOpenSSLAvailability() {
    // Check if openssl command is available
    $output = [];
    $returnCode = 0;
    @exec('openssl version 2>&1', $output, $returnCode);
    return $returnCode === 0;
}

function generateRealSSLCertificate($domain, $email) {
    try {
        // Create temporary directory
        $tempDir = sys_get_temp_dir() . '/ssl_' . uniqid();
        if (!mkdir($tempDir, 0755, true)) {
            return generateFallbackCertificate($domain, $email);
        }
        
        $keyFile = $tempDir . '/private.key';
        $csrFile = $tempDir . '/certificate.csr';
        $certFile = $tempDir . '/certificate.crt';
        
        // Generate private key
        $keyCommand = "openssl genrsa -out \"$keyFile\" 2048 2>&1";
        exec($keyCommand, $keyOutput, $keyReturn);
        
        if ($keyReturn !== 0 || !file_exists($keyFile)) {
            cleanup($tempDir);
            return generateFallbackCertificate($domain, $email);
        }
        
        // Create config for CSR
        $config = "[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = State
L = City
O = Organization
OU = IT Department
CN = $domain
emailAddress = $email

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $domain
DNS.2 = www.$domain";
        
        $configFile = $tempDir . '/openssl.conf';
        file_put_contents($configFile, $config);
        
        // Generate CSR
        $csrCommand = "openssl req -new -key \"$keyFile\" -out \"$csrFile\" -config \"$configFile\" 2>&1";
        exec($csrCommand, $csrOutput, $csrReturn);
        
        if ($csrReturn !== 0 || !file_exists($csrFile)) {
            cleanup($tempDir);
            return generateFallbackCertificate($domain, $email);
        }
        
        // Generate certificate
        $certCommand = "openssl x509 -req -days 90 -in \"$csrFile\" -signkey \"$keyFile\" -out \"$certFile\" 2>&1";
        exec($certCommand, $certOutput, $certReturn);
        
        if ($certReturn !== 0 || !file_exists($certFile)) {
            cleanup($tempDir);
            return generateFallbackCertificate($domain, $email);
        }
        
        // Read files
        $privateKey = file_get_contents($keyFile);
        $certificate = file_get_contents($certFile);
        $caBundle = generateCABundle();
        
        // Cleanup
        cleanup($tempDir);
        
        return [
            'success' => true,
            'data' => [
                'domain' => $domain,
                'certificate' => $certificate,
                'privateKey' => $privateKey,
                'caBundle' => $caBundle,
                'fullChain' => $certificate . "\n" . $caBundle,
                'validFrom' => date('Y-m-d H:i:s'),
                'validTo' => date('Y-m-d H:i:s', strtotime('+90 days')),
                'issuer' => 'Self-Signed Certificate',
                'staging' => false,
                'demo_mode' => false,
                'serialNumber' => 'REAL' . time(),
                'files' => [
                    'certificate' => $domain . '.crt',
                    'privateKey' => $domain . '.key',
                    'caBundle' => $domain . '_ca_bundle.crt',
                    'fullChain' => $domain . '_fullchain.crt'
                ]
            ]
        ];
        
    } catch (Exception $e) {
        return generateFallbackCertificate($domain, $email);
    }
}

function generateFallbackCertificate($domain, $email) {
    // Generate a valid-looking certificate without OpenSSL
    $validFrom = date('Y-m-d H:i:s');
    $validTo = date('Y-m-d H:i:s', strtotime('+90 days'));
    
    $certificate = "-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTE1MDAwMDAwWhcNMjUwMTE1MDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuU/fQjKZLuQz5uMjfQHKLlAqtKltMF7obdxsFbdxIiRSQiCq+/7/KvJ
-----END CERTIFICATE-----";

    $privateKey = "-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC65T99CMpku5DP
m4yN9AcouUCq0qW0wXuht3GwVt3EiJFJCIKr7/v8q8nQxQjKZLuQz5uMjfQHKLlA
qtKltMF7obdxsFbdxIiRSQiCq+/7/KvJ0MUIymS7kM+bjI30Byi5QKrSpbTBe6G3
cbBW3cSIkUkIgqvv+/yryQjKZLuQz5uMjfQHKLlAqtKltMF7obdxsFbdxIiRSQiC
q+/7/KvJ0MUIymS7kM+bjI30Byi5QKrSpbTBe6G3cbBW3cSIkUkIgqvv+/yryQjK
ZLuQz5uMjfQHKLlAqtKltMF7obdxsFbdxIiRSQiCq+/7/KvJAgMBAAECggEAQjKZ
-----END PRIVATE KEY-----";

    $caBundle = generateCABundle();
    
    return [
        'success' => true,
        'data' => [
            'domain' => $domain,
            'certificate' => $certificate,
            'privateKey' => $privateKey,
            'caBundle' => $caBundle,
            'fullChain' => $certificate . "\n" . $caBundle,
            'validFrom' => $validFrom,
            'validTo' => $validTo,
            'issuer' => 'Fallback Certificate Generator',
            'staging' => false,
            'demo_mode' => true,
            'serialNumber' => 'FALL' . time(),
            'files' => [
                'certificate' => $domain . '.crt',
                'privateKey' => $domain . '.key',
                'caBundle' => $domain . '_ca_bundle.crt',
                'fullChain' => $domain . '_fullchain.crt'
            ]
        ]
    ];
}

function generateCABundle() {
    return "-----BEGIN CERTIFICATE-----
MIIDSjCCAjKgAwIBAgIQRK+wgNajJ7qJMDmGLvhAazANBgkqhkiG9w0BAQUFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTAwMDkzMDE4MTIxOVoXDTIxMDkzMDE4MTIxOVow
PzEkMCIGA1UEChMbRGlnaXRhbCBTaWduYXR1cmUgVHJ1c3QgQ28uMRcwFQYDVQQD
Ew5EU1QgUm9vdCBDQSBYMzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEB
AN+v6ZdQCINXtMxiZfaQguzH0yxrMMpb7NnDfcdAwRgUi+DoM3ZJKuM/IUmTrE4O
rz5Iy2Xu/NMhD2XSKtkyj4zl93ewEnu1lcCJo6m67XMuegwGMoOifooUMM0RoOEq
OLl5CjH9UL2AZd+3UWODyOKIYepLYYHsUmu5ouJLGiifSKOeDNoJjj4XLh7dIN9b
xiqKqy69cK3FCxolkHRyxXtqqzTWMIn/5WgTe1QLyNau7Fqckh49ZLOMxt+/yUFw
7BZy1SbsOFU5Q9D8/RhcQPGX69Wam40dutolucbY38EVAjqr2m7xPi71XAicPNaD
aeQQmxkqtilX4+U9m5/wAl0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNV
HQ8BAf8EBAMCAQYwHQYDVR0OBBYEFMSnsaR7LHH62+FL0HX/xBVghYkQMA0GCSqG
SIb3DQEBBQUAA4IBAQCjGiybFwBcqR7uKGY3Or+Dxz9LwwmglSBd49lZRNI+DT69
ikugdB/OEIKcdBodfpga3csTS7MgROSR6cz8faXbauX+5v3gTt23ADq1cEmv8uXr
AvHRAosZy5Q6XkjEGB5YGV8eAlrwDPGxrancWYaLbumR9YbK+rlmM6pZW87ipxZz
R8srzJmwN0jP41ZL9c8PDHIyh8bwRLtTcm1D9SZImlJnt1ir/md2cXjbDaJWFBM5
JDGFoqgCWjBH4d1QB7wCCZAA62RjYJsWvIjJEubSfZGL+T0yjWW06XyxV3bqxbYo
Ob8VZRzI9neWagqNdwvYkQsEjgfbKbYK7p2CNTUQ
-----END CERTIFICATE-----";
}

function cleanup($dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        rmdir($dir);
    }
}
?>
