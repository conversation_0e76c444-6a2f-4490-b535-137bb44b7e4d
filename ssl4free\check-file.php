<?php
echo "<h1>File Content Check</h1>";

$filePath = 'api/create-certificate.php';
echo "<p><strong>Checking file:</strong> $filePath</p>";

if (file_exists($filePath)) {
    $content = file_get_contents($filePath);
    echo "<p><strong>File size:</strong> " . strlen($content) . " bytes</p>";
    echo "<p><strong>Last modified:</strong> " . date('Y-m-d H:i:s', filemtime($filePath)) . "</p>";
    
    // Check for verification_method references
    $verificationMethodCount = substr_count($content, 'verification_method');
    echo "<p><strong>verification_method references:</strong> $verificationMethodCount</p>";
    
    if ($verificationMethodCount > 0) {
        echo "<p style='color: red;'>❌ File still contains verification_method references!</p>";
        
        // Show lines containing verification_method
        $lines = explode("\n", $content);
        echo "<h3>Lines containing 'verification_method':</h3>";
        foreach ($lines as $lineNum => $line) {
            if (stripos($line, 'verification_method') !== false) {
                echo "<p><strong>Line " . ($lineNum + 1) . ":</strong> " . htmlspecialchars($line) . "</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ File does not contain verification_method references</p>";
    }
    
    // Check for required field validation
    if (strpos($content, "!isset(\$input['verification_method'])") !== false) {
        echo "<p style='color: red;'>❌ File still checks for verification_method as required field!</p>";
    } else {
        echo "<p style='color: green;'>✅ File does not check for verification_method as required field</p>";
    }
    
    // Show first 50 lines of the file
    echo "<h3>First 50 lines of file:</h3>";
    $lines = explode("\n", $content);
    echo "<pre>";
    for ($i = 0; $i < min(50, count($lines)); $i++) {
        echo sprintf("%3d: %s\n", $i + 1, htmlspecialchars($lines[$i]));
    }
    echo "</pre>";
    
} else {
    echo "<p style='color: red;'>❌ File does not exist!</p>";
}

echo "<p><a href='test-cert-creation.php'>← Back to Test</a></p>";
?>
