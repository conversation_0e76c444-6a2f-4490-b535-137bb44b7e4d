<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    // Check for JSON errors
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid JSON: ' . json_last_error_msg()
        ]);
        exit();
    }

    // Validate input
    if (!$input || !isset($input['domain']) || !isset($input['email'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Missing required fields: domain and email'
        ]);
        exit();
    }

    $domain = htmlspecialchars(trim($input['domain']));
    $email = filter_var(trim($input['email']), FILTER_VALIDATE_EMAIL);

    if (!$email) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid email address'
        ]);
        exit();
    }

    // Simple domain validation
    $domain = preg_replace('/^https?:\/\//', '', $domain);
    $domain = rtrim($domain, '/');

    if (!preg_match('/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/', $domain)) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid domain format'
        ]);
        exit();
    }

    // Generate demo SSL certificate
    $certificate = "-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTE1MDAwMDAwWhcNMjUwMTE1MDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuU/fQjKZLuQz5uMjfQHKLlAqtKltMF7obdxsFbdxIiRSQiCq+/7/KvJ
-----END CERTIFICATE-----";

    $privateKey = "-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC65T99CMpku5DP
m4yN9AcouUCq0qW0wXuht3GwVt3EiJFJCIKr7/v8q8nQxQjKZLuQz5uMjfQHKLlA
qtKltMF7obdxsFbdxIiRSQiCq+/7/KvJ0MUIymS7kM+bjI30Byi5QKrSpbTBe6G3
cbBW3cSIkUkIgqvv+/yryQjKZLuQz5uMjfQHKLlAqtKltMF7obdxsFbdxIiRSQiC
q+/7/KvJ0MUIymS7kM+bjI30Byi5QKrSpbTBe6G3cbBW3cSIkUkIgqvv+/yryQjK
ZLuQz5uMjfQHKLlAqtKltMF7obdxsFbdxIiRSQiCq+/7/KvJAgMBAAECggEAQjKZ
-----END PRIVATE KEY-----";

    $caBundle = "-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTE1MDAwMDAwWhcNMjUwMTE1MDAwMDAwWjBF
-----END CERTIFICATE-----";

    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'SSL certificate generated successfully (demo)',
        'data' => [
            'domain' => $domain,
            'certificate' => $certificate,
            'privateKey' => $privateKey,
            'caBundle' => $caBundle,
            'fullChain' => $certificate . "\n" . $caBundle,
            'validFrom' => date('Y-m-d H:i:s'),
            'validTo' => date('Y-m-d H:i:s', strtotime('+90 days')),
            'issuer' => 'SSL Generator (Demo)',
            'staging' => false,
            'demo_mode' => true,
            'serialNumber' => 'DEMO' . time(),
            'files' => [
                'certificate' => $domain . '.crt',
                'privateKey' => $domain . '.key',
                'caBundle' => $domain . '_ca_bundle.crt',
                'fullChain' => $domain . '_fullchain.crt'
            ]
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>