<?php
require_once 'domain-validator.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!$input || !isset($input['domain'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Domain is required']);
    exit();
}

$domain = htmlspecialchars(trim($input['domain']));
$email = isset($input['email']) ? filter_var(trim($input['email']), FILTER_VALIDATE_EMAIL) : '';

// Remove protocol if present
$domain = preg_replace('/^https?:\/\//', '', $domain);
$domain = rtrim($domain, '/');

try {
    // Perform comprehensive domain validation
    $validationResult = DomainValidator::validateDomain($domain, $email);
    
    // Get instructions for fixing issues
    $instructions = DomainValidator::getValidationInstructions($domain, $validationResult);
    
    // Log validation attempt
    $logEntry = date('Y-m-d H:i:s') . " - Domain validation for: $domain, Result: " . 
                ($validationResult['can_generate_ssl'] ? 'PASS' : 'FAIL') . "\n";
    file_put_contents(__DIR__ . '/domain_validation.log', $logEntry, FILE_APPEND | LOCK_EX);
    
    // Return detailed validation result
    echo json_encode([
        'success' => true,
        'domain' => $domain,
        'validation' => $validationResult,
        'instructions' => $instructions,
        'can_generate_ssl' => $validationResult['can_generate_ssl'],
        'message' => $validationResult['can_generate_ssl'] 
            ? 'Domain validation passed! Ready for SSL generation.' 
            : 'Domain validation failed. Please fix the issues below.'
    ]);
    
} catch (Exception $e) {
    error_log("Domain validation error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Domain validation failed: ' . $e->getMessage(),
        'domain' => $domain,
        'can_generate_ssl' => false
    ]);
}
?>
