<?php
/**
 * SSL Certificate Validator - Like SSLForFree.com
 * Validates SSL certificates for proper format and validity
 */

class SSLValidator {
    
    /**
     * Validate SSL certificate package
     */
    public static function validateSSLPackage($certificate, $privateKey, $caBundle = '') {
        $results = [
            'certificate_valid' => false,
            'private_key_valid' => false,
            'ca_bundle_valid' => false,
            'key_match' => false,
            'certificate_info' => [],
            'errors' => [],
            'warnings' => []
        ];
        
        try {
            // Validate certificate
            $certValidation = self::validateCertificate($certificate);
            $results['certificate_valid'] = $certValidation['valid'];
            $results['certificate_info'] = $certValidation['info'];
            if (!$certValidation['valid']) {
                $results['errors'] = array_merge($results['errors'], $certValidation['errors']);
            }
            
            // Validate private key
            $keyValidation = self::validatePrivateKey($privateKey);
            $results['private_key_valid'] = $keyValidation['valid'];
            if (!$keyValidation['valid']) {
                $results['errors'] = array_merge($results['errors'], $keyValidation['errors']);
            }
            
            // Validate CA Bundle if provided
            if (!empty($caBundle)) {
                $caBundleValidation = self::validateCABundle($caBundle);
                $results['ca_bundle_valid'] = $caBundleValidation['valid'];
                if (!$caBundleValidation['valid']) {
                    $results['errors'] = array_merge($results['errors'], $caBundleValidation['errors']);
                }
            } else {
                $results['ca_bundle_valid'] = true; // Optional
            }
            
            // Check if private key matches certificate
            if ($results['certificate_valid'] && $results['private_key_valid']) {
                $results['key_match'] = self::checkKeyMatch($certificate, $privateKey);
                if (!$results['key_match']) {
                    $results['errors'][] = 'Private key does not match the certificate';
                }
            }
            
        } catch (Exception $e) {
            $results['errors'][] = 'Validation error: ' . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Validate SSL certificate
     */
    private static function validateCertificate($certificate) {
        $result = ['valid' => false, 'info' => [], 'errors' => []];
        
        try {
            // Clean certificate
            $certificate = trim($certificate);
            
            // Check format
            if (!preg_match('/-----BEGIN CERTIFICATE-----/', $certificate)) {
                $result['errors'][] = 'Certificate must start with -----BEGIN CERTIFICATE-----';
                return $result;
            }
            
            if (!preg_match('/-----END CERTIFICATE-----/', $certificate)) {
                $result['errors'][] = 'Certificate must end with -----END CERTIFICATE-----';
                return $result;
            }
            
            // Parse certificate
            $certResource = openssl_x509_read($certificate);
            if (!$certResource) {
                $result['errors'][] = 'Invalid certificate format: ' . openssl_error_string();
                return $result;
            }
            
            // Get certificate info
            $certInfo = openssl_x509_parse($certResource);
            if (!$certInfo) {
                $result['errors'][] = 'Cannot parse certificate information';
                return $result;
            }
            
            // Extract useful information
            $result['info'] = [
                'subject' => $certInfo['subject'],
                'issuer' => $certInfo['issuer'],
                'valid_from' => date('Y-m-d H:i:s', $certInfo['validFrom_time_t']),
                'valid_to' => date('Y-m-d H:i:s', $certInfo['validTo_time_t']),
                'serial_number' => $certInfo['serialNumber'],
                'signature_algorithm' => $certInfo['signatureTypeSN'] ?? 'Unknown'
            ];
            
            // Check if certificate is expired
            $now = time();
            if ($certInfo['validTo_time_t'] < $now) {
                $result['errors'][] = 'Certificate has expired';
                return $result;
            }
            
            if ($certInfo['validFrom_time_t'] > $now) {
                $result['errors'][] = 'Certificate is not yet valid';
                return $result;
            }
            
            $result['valid'] = true;
            
        } catch (Exception $e) {
            $result['errors'][] = 'Certificate validation error: ' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Validate private key
     */
    private static function validatePrivateKey($privateKey) {
        $result = ['valid' => false, 'errors' => []];
        
        try {
            // Clean private key
            $privateKey = trim($privateKey);
            
            // Check format
            if (!preg_match('/-----BEGIN (RSA )?PRIVATE KEY-----/', $privateKey)) {
                $result['errors'][] = 'Private key must start with -----BEGIN PRIVATE KEY----- or -----BEGIN RSA PRIVATE KEY-----';
                return $result;
            }
            
            if (!preg_match('/-----END (RSA )?PRIVATE KEY-----/', $privateKey)) {
                $result['errors'][] = 'Private key must end with -----END PRIVATE KEY----- or -----END RSA PRIVATE KEY-----';
                return $result;
            }
            
            // Parse private key
            $keyResource = openssl_pkey_get_private($privateKey);
            if (!$keyResource) {
                $result['errors'][] = 'Invalid private key format: ' . openssl_error_string();
                return $result;
            }
            
            // Get key details
            $keyDetails = openssl_pkey_get_details($keyResource);
            if (!$keyDetails) {
                $result['errors'][] = 'Cannot get private key details';
                return $result;
            }
            
            // Check key type
            if ($keyDetails['type'] !== OPENSSL_KEYTYPE_RSA) {
                $result['errors'][] = 'Only RSA private keys are supported';
                return $result;
            }
            
            // Check key size
            if ($keyDetails['bits'] < 2048) {
                $result['errors'][] = 'Private key must be at least 2048 bits';
                return $result;
            }
            
            $result['valid'] = true;
            
        } catch (Exception $e) {
            $result['errors'][] = 'Private key validation error: ' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Validate CA Bundle
     */
    private static function validateCABundle($caBundle) {
        $result = ['valid' => false, 'errors' => []];
        
        try {
            // Clean CA bundle
            $caBundle = trim($caBundle);
            
            // Check if it contains certificates
            if (!preg_match('/-----BEGIN CERTIFICATE-----/', $caBundle)) {
                $result['errors'][] = 'CA Bundle must contain at least one certificate';
                return $result;
            }
            
            // Split into individual certificates
            $certificates = [];
            $lines = explode("\n", $caBundle);
            $currentCert = '';
            $inCert = false;
            
            foreach ($lines as $line) {
                $line = trim($line);
                if ($line === '-----BEGIN CERTIFICATE-----') {
                    $inCert = true;
                    $currentCert = $line . "\n";
                } elseif ($line === '-----END CERTIFICATE-----') {
                    $currentCert .= $line . "\n";
                    $certificates[] = trim($currentCert);
                    $currentCert = '';
                    $inCert = false;
                } elseif ($inCert) {
                    $currentCert .= $line . "\n";
                }
            }
            
            if (empty($certificates)) {
                $result['errors'][] = 'No valid certificates found in CA Bundle';
                return $result;
            }
            
            // Validate each certificate in the bundle
            foreach ($certificates as $cert) {
                $certValidation = self::validateCertificate($cert);
                if (!$certValidation['valid']) {
                    $result['errors'][] = 'Invalid certificate in CA Bundle: ' . implode(', ', $certValidation['errors']);
                    return $result;
                }
            }
            
            $result['valid'] = true;
            
        } catch (Exception $e) {
            $result['errors'][] = 'CA Bundle validation error: ' . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Check if private key matches certificate
     */
    private static function checkKeyMatch($certificate, $privateKey) {
        try {
            // Get public key from certificate
            $certResource = openssl_x509_read($certificate);
            $publicKey = openssl_pkey_get_public($certResource);
            $publicKeyDetails = openssl_pkey_get_details($publicKey);
            
            // Get public key from private key
            $privateKeyResource = openssl_pkey_get_private($privateKey);
            $privateKeyDetails = openssl_pkey_get_details($privateKeyResource);
            
            // Compare public keys
            return $publicKeyDetails['key'] === $privateKeyDetails['key'];
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Generate proper SSL certificate for domain
     */
    public static function generateValidSSLCertificate($domain) {
        try {
            // Generate private key
            $privateKey = openssl_pkey_new([
                'private_key_bits' => 2048,
                'private_key_type' => OPENSSL_KEYTYPE_RSA,
            ]);
            
            // Create certificate signing request
            $dn = [
                'CN' => $domain,
                'O' => 'SSL Generator',
                'C' => 'US'
            ];
            
            $csr = openssl_csr_new($dn, $privateKey, [
                'digest_alg' => 'sha256',
                'private_key_bits' => 2048,
                'private_key_type' => OPENSSL_KEYTYPE_RSA,
            ]);
            
            // Create self-signed certificate (for demo)
            $certificate = openssl_csr_sign($csr, null, $privateKey, 90, [
                'digest_alg' => 'sha256'
            ]);
            
            // Export certificate and private key
            openssl_x509_export($certificate, $certOut);
            openssl_pkey_export($privateKey, $keyOut);
            
            // Get Let's Encrypt intermediate certificate
            $caBundle = self::getLetSEncryptIntermediate();
            
            return [
                'success' => true,
                'certificate' => $certOut,
                'privateKey' => $keyOut,
                'caBundle' => $caBundle,
                'fullChain' => $certOut . "\n" . $caBundle
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get Let's Encrypt intermediate certificate
     */
    private static function getLetSEncryptIntermediate() {
        return "-----BEGIN CERTIFICATE-----
MIIEkjCCA3qgAwIBAgIQCgFBQgAAAVOFc2oLheynCDANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDb4xCzAJBgNVBAMT
GERTVCBSb290IENBIFgzMB4XDTE2MDMxNzE2NDA0NloXDTIxMDMxNzE2NDA0Nlow
SjELMAkGA1UEBhMCVVMxFjAUBgNVBAoTDUxldCdzIEVuY3J5cHQxIzAhBgNVBAMT
GkxldCdzIEVuY3J5cHQgQXV0aG9yaXR5IFgzMIIBIjANBgkqhkiG9w0BAQEFAAOC
AQ8AMIIBCgKCAQEAnNMM8FrlLke3cl03g7NoYzDq1zUmGSXhvb418XCSL7e4S0EF
q6meNQhY7LEqxGiHC6PjdeTm86dicbp5gWAf15Gan/PQeGdxyGkOlZHP/uaZ6WA8
SMx+yk13EiSdRxta67nsHjcAHJyse6cF6s5K671B5TaYucv9bTyWaN8jKkKQDIZ0
Z8h/pZq4UmEUEz9l6YKHy9v6Dlb2honzhT+Xhq+w3Brvaw2VFn3EK6BlspkENnWA
a6xK8xuQSXgvopZPKiAlKQTGdMDQMc2PMTiVFrqoM7hD8bEfwzB/onkxEz0tNvjj
/PIzark5McWvxI0NHWQWM6r6hCm21AvA2H3DkwIDAQABo4IBfTCCAXkwEgYDVR0T
AQH/BAgwBgEB/wIBADAOBgNVHQ8BAf8EBAMCAYYwfwYIKwYBBQUHAQEEczBxMDIG
CCsGAQUFBzABhiZodHRwOi8vaXNyZy50cnVzdGlkLm9jc3AuaWRlbnRydXN0LmNv
bTA7BggrBgEFBQcwAoYvaHR0cDovL2FwcHMuaWRlbnRydXN0LmNvbS9yb290cy9k
c3Ryb290Y2F4My5wN2MwHwYDVR0jBBgwFoAUxKexpHsscfrb4UuQdf/EFWCFiRAw
VAYDVR0gBE0wSzAIBgZngQwBAgEwPwYLKwYBBAGC3xMBAQEwMDAuBggrBgEFBQcC
ARYiaHR0cDovL2Nwcy5yb290LXgxLmxldHNlbmNyeXB0Lm9yZzA8BgNVHR8ENTAz
MDGgL6AthitodHRwOi8vY3JsLmlkZW50cnVzdC5jb20vRFNUUk9PVENBWDNDTE
wHhcNMjEwMzE3MTY0MDQ2WjANBgkqhkiG9w0BAQsFAAOCAQEAKmjnZ4kg5jjigwe
/SqoizgCx9E+pIti7kD2luZz6tNpPi5WuJ+q4TlzqgHwy/XqahWYTW3rvdMaNhq
4C5fnHRDS/PgqaGcWyH8QZE4ELdN1ByIVvIAQ3uvxw5LcUt9QuUNpwKAPvHdPd
7WVmKVSPQAEQDewD+y5wlKGZBkmhiMlzxydKT3XJNUG6jBVBaYVu8QjBbLHcv/7
M/cCUJH5+dZfZrlo1qQ2hLohKWFzI19+dQYSdHFzOxMQVklAJysOVdRqLTVmh69
L/a3NL/1207vQXALEXh2VkQFAH2w+/uKv0YmFfxieFMl+DeqVTLKXMTaS4SqVe
fVqhIWaUGb+2hM=
-----END CERTIFICATE-----";
    }
}
?>
