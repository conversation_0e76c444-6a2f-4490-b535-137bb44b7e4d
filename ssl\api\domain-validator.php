<?php
/**
 * Domain Validation System
 * Proper domain verification before SSL generation
 */

class DomainValidator {
    
    /**
     * Validate domain before SSL generation
     */
    public static function validateDomain($domain, $email) {
        $results = [
            'domain_valid' => false,
            'dns_resolves' => false,
            'server_accessible' => false,
            'challenge_path_accessible' => false,
            'can_generate_ssl' => false,
            'errors' => [],
            'warnings' => []
        ];
        
        try {
            // Step 1: Basic domain format validation
            if (!self::isValidDomainFormat($domain)) {
                $results['errors'][] = "Invalid domain format: $domain";
                return $results;
            }
            $results['domain_valid'] = true;
            
            // Step 2: DNS resolution check
            if (!self::checkDNSResolution($domain)) {
                $results['errors'][] = "Domain DNS does not resolve: $domain";
                return $results;
            }
            $results['dns_resolves'] = true;
            
            // Step 3: Server accessibility check
            $serverCheck = self::checkServerAccessibility($domain);
            if (!$serverCheck['accessible']) {
                $results['errors'][] = "Server not accessible: " . $serverCheck['error'];
                return $results;
            }
            $results['server_accessible'] = true;
            
            // Step 4: Challenge path accessibility check
            $challengeCheck = self::checkChallengePathAccessibility($domain);
            if (!$challengeCheck['accessible']) {
                $results['warnings'][] = "Challenge path may not be accessible: " . $challengeCheck['error'];
                // Don't return here - this might be fixable
            } else {
                $results['challenge_path_accessible'] = true;
            }
            
            // Step 5: Final decision (More flexible for testing)
            if ($results['domain_valid'] && $results['dns_resolves']) {
                $results['can_generate_ssl'] = true;

                if (!$results['server_accessible']) {
                    $results['warnings'][] = "Server not accessible. SSL generation will use demo mode.";
                }

                if (!$results['challenge_path_accessible']) {
                    $results['warnings'][] = "Challenge path not accessible. SSL generation will use demo mode.";
                }
            } else if ($results['domain_valid']) {
                // Allow demo mode even if DNS doesn't resolve
                $results['can_generate_ssl'] = true;
                $results['warnings'][] = "Domain validation incomplete. Using demo mode for testing.";
            }
            
        } catch (Exception $e) {
            $results['errors'][] = "Validation error: " . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Check if domain format is valid
     */
    private static function isValidDomainFormat($domain) {
        // Remove protocol if present
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        
        // Remove trailing slash
        $domain = rtrim($domain, '/');
        
        // Basic domain validation
        return filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME);
    }
    
    /**
     * Check DNS resolution
     */
    private static function checkDNSResolution($domain) {
        // Check A record
        $aRecords = @dns_get_record($domain, DNS_A);
        if ($aRecords && count($aRecords) > 0) {
            return true;
        }
        
        // Check AAAA record (IPv6)
        $aaaaRecords = @dns_get_record($domain, DNS_AAAA);
        if ($aaaaRecords && count($aaaaRecords) > 0) {
            return true;
        }
        
        // Check CNAME record
        $cnameRecords = @dns_get_record($domain, DNS_CNAME);
        if ($cnameRecords && count($cnameRecords) > 0) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check server accessibility
     */
    private static function checkServerAccessibility($domain) {
        $result = ['accessible' => false, 'error' => '', 'status_code' => 0];
        
        try {
            $url = "http://$domain/";
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HEADER => true,
                CURLOPT_NOBODY => true, // HEAD request
                CURLOPT_TIMEOUT => 10,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_USERAGENT => 'SSL-Generator-Domain-Validator/1.0'
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            $result['status_code'] = $httpCode;
            
            if ($error) {
                $result['error'] = "Connection error: $error";
                return $result;
            }
            
            // Accept various HTTP status codes as "accessible"
            if ($httpCode >= 200 && $httpCode < 500) {
                $result['accessible'] = true;
            } else {
                $result['error'] = "HTTP status code: $httpCode";
            }
            
        } catch (Exception $e) {
            $result['error'] = "Exception: " . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Check challenge path accessibility
     */
    private static function checkChallengePathAccessibility($domain) {
        $result = ['accessible' => false, 'error' => ''];
        
        try {
            // Create a test file
            $testToken = 'test-' . bin2hex(random_bytes(8));
            $testContent = 'SSL-Generator-Test-' . time();
            
            $challengePath = __DIR__ . '/../.well-known/acme-challenge/';
            if (!is_dir($challengePath)) {
                mkdir($challengePath, 0755, true);
            }
            
            $testFile = $challengePath . $testToken;
            file_put_contents($testFile, $testContent);
            
            // Try to access the test file via HTTP
            $testUrl = "http://$domain/.well-known/acme-challenge/$testToken";
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $testUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_USERAGENT => 'SSL-Generator-Challenge-Validator/1.0'
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            // Clean up test file
            if (file_exists($testFile)) {
                unlink($testFile);
            }
            
            if ($error) {
                $result['error'] = "Connection error: $error";
                return $result;
            }
            
            if ($httpCode === 200 && trim($response) === $testContent) {
                $result['accessible'] = true;
            } else {
                $result['error'] = "Challenge path not accessible. HTTP code: $httpCode, Expected: '$testContent', Got: '" . substr($response, 0, 50) . "'";
            }
            
        } catch (Exception $e) {
            $result['error'] = "Exception: " . $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * Get domain validation instructions
     */
    public static function getValidationInstructions($domain, $validationResult) {
        $instructions = [];
        
        if (!$validationResult['domain_valid']) {
            $instructions[] = "❌ Fix domain format: Use format like 'example.com' (without http:// or https://)";
        }
        
        if (!$validationResult['dns_resolves']) {
            $instructions[] = "❌ Fix DNS: Point your domain '$domain' to your server IP address";
            $instructions[] = "   - Update A record in your DNS settings";
            $instructions[] = "   - Wait for DNS propagation (can take up to 24 hours)";
            $instructions[] = "   - Test with: nslookup $domain";
        }
        
        if (!$validationResult['server_accessible']) {
            $instructions[] = "❌ Fix server accessibility:";
            $instructions[] = "   - Ensure web server is running";
            $instructions[] = "   - Check firewall settings (allow port 80)";
            $instructions[] = "   - Verify domain points to correct server";
            $instructions[] = "   - Test with: curl -I http://$domain/";
        }
        
        if (!$validationResult['challenge_path_accessible']) {
            $instructions[] = "⚠️  Fix challenge path:";
            $instructions[] = "   - Ensure /.well-known/acme-challenge/ directory exists";
            $instructions[] = "   - Set correct permissions: chmod 755 .well-known/acme-challenge/";
            $instructions[] = "   - Configure web server to serve files from this directory";
            $instructions[] = "   - Test with: curl http://$domain/.well-known/acme-challenge/test";
        }
        
        if ($validationResult['can_generate_ssl']) {
            $instructions[] = "✅ Domain validation passed! You can generate SSL certificates.";
            
            if (!empty($validationResult['warnings'])) {
                $instructions[] = "⚠️  Warnings:";
                foreach ($validationResult['warnings'] as $warning) {
                    $instructions[] = "   - $warning";
                }
            }
        }
        
        return $instructions;
    }
    
    /**
     * Quick domain check for frontend
     */
    public static function quickCheck($domain) {
        $isValid = self::isValidDomainFormat($domain);
        $dnsResolves = $isValid ? self::checkDNSResolution($domain) : false;
        
        return [
            'valid' => $isValid,
            'dns_resolves' => $dnsResolves,
            'ready_for_ssl' => $isValid && $dnsResolves
        ];
    }
}
?>
