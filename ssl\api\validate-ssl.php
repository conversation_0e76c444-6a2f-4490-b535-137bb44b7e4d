<?php
require_once 'ssl-validator.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!$input || !isset($input['certificate']) || !isset($input['privateKey'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Certificate and private key are required']);
    exit();
}

$certificate = $input['certificate'];
$privateKey = $input['privateKey'];
$caBundle = isset($input['caBundle']) ? $input['caBundle'] : '';

try {
    // Validate SSL package
    $validation = SSLValidator::validateSSLPackage($certificate, $privateKey, $caBundle);
    
    // Log validation attempt
    $domain = isset($input['domain']) ? $input['domain'] : 'unknown';
    $logEntry = date('Y-m-d H:i:s') . " - SSL validation for domain: $domain, " . 
                "cert_valid: " . ($validation['certificate_valid'] ? 'YES' : 'NO') . ", " .
                "key_valid: " . ($validation['private_key_valid'] ? 'YES' : 'NO') . ", " .
                "key_match: " . ($validation['key_match'] ? 'YES' : 'NO') . "\n";
    file_put_contents(__DIR__ . '/ssl_validation.log', $logEntry, FILE_APPEND | LOCK_EX);
    
    // Return validation results
    echo json_encode([
        'success' => true,
        'message' => 'SSL package validation completed',
        'validation' => $validation,
        'summary' => [
            'all_valid' => $validation['certificate_valid'] && $validation['private_key_valid'] && $validation['ca_bundle_valid'] && $validation['key_match'],
            'certificate_status' => $validation['certificate_valid'] ? 'Valid' : 'Invalid',
            'private_key_status' => $validation['private_key_valid'] ? 'Valid' : 'Invalid',
            'ca_bundle_status' => $validation['ca_bundle_valid'] ? 'Valid' : 'Invalid',
            'key_match_status' => $validation['key_match'] ? 'Matches' : 'Does not match'
        ]
    ]);
    
} catch (Exception $e) {
    error_log("SSL validation error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'SSL validation failed: ' . $e->getMessage()
    ]);
}
?>
