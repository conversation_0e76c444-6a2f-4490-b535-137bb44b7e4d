<?php
// Quick test for domain validation
require_once 'api/domain-validator.php';

$testDomain = 'jobzhit.com';

echo "<h2>Testing Domain: $testDomain</h2>";

$result = DomainValidator::validateDomain($testDomain, '<EMAIL>');

echo "<h3>Validation Results:</h3>";
echo "<pre>";
print_r($result);
echo "</pre>";

$instructions = DomainValidator::getValidationInstructions($testDomain, $result);

echo "<h3>Instructions:</h3>";
echo "<ul>";
foreach ($instructions as $instruction) {
    echo "<li>$instruction</li>";
}
echo "</ul>";

echo "<h3>Can Generate SSL: " . ($result['can_generate_ssl'] ? 'YES' : 'NO') . "</h3>";
?>
