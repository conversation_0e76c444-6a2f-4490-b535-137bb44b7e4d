<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIM Data Online - Check CNIC SIM Information | Pakistan 2025</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Check SIM data online in Pakistan. Find all SIM cards registered against CNIC number. Updated 2025 database for all networks.">
    <meta name="keywords" content="sim data online, cnic sim check, pakistan sim database, mobile number verification, sim registration">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/new-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <i class="fas fa-database" style="font-size: 2rem; color: var(--primary-color);"></i>
                    <span class="logo-text">PakSim</span>
                </a>

                <ul class="nav-menu">
                    <li class="nav-item"><a href="index.html" class="nav-link">Homepage</a></li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link" style="color: var(--primary-color);">SIM DATA <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="sim-owner-details.html">SIM Owner Details</a></li>
                            <li><a href="sim-data-online.html">SIM DATA ONLINE</a></li>
                            <li><a href="live-tracker.html">Live Tracker</a></li>
                        </ul>
                    </li>
                    <li class="nav-item"><a href="about.html" class="nav-link">About Us</a></li>
                    <li class="nav-item"><a href="faq.html" class="nav-link">FAQ</a></li>
                    <li class="nav-item"><a href="blog.html" class="nav-link">Blog</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-link">Contact Us</a></li>
                </ul>
                
                <div class="nav-actions">
                    <a href="contact.html" class="btn btn-primary">Get Started</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header" style="background: var(--gradient-primary); color: #fff; padding: 60px 0; text-align: center;">
        <div class="container">
            <h1 style="font-size: 3rem; margin-bottom: 20px;">SIM Data Online 2025</h1>
            <p style="font-size: 1.2rem; opacity: 0.9;">Check CNIC SIM information and mobile number verification online</p>
        </div>
    </section>

    <!-- Search Section -->
    <section style="padding: 60px 0; background: #f8f9fa;">
        <div class="container">
            <div style="max-width: 900px; margin: 0 auto;">
                <div class="sim-search-form">
                    <h3><i class="fas fa-database"></i> Check SIM Data Online 2025</h3>
                    <form class="search-form" id="simSearchForm">
                        <div class="form-group">
                            <label for="searchType"><i class="fas fa-filter"></i> Search Type</label>
                            <select id="searchType" name="searchType" required>
                                <option value="">🔍 Select Search Type</option>
                                <option value="mobile">📱 Mobile Number Lookup</option>
                                <option value="cnic">🆔 CNIC SIM Check</option>
                            </select>
                        </div>

                        <div class="form-group has-icon">
                            <label for="searchInput"><i class="fas fa-keyboard"></i> Enter Number</label>
                            <input type="text" id="searchInput" name="searchInput" placeholder="Enter mobile or CNIC number..." required>
                        </div>

                        <button type="submit" class="btn btn-secondary btn-large">
                            <i class="fas fa-search-plus"></i> Check Data
                        </button>
                    </form>

                    <!-- Enhanced Features Info -->
                    <div style="margin-top: 30px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="text-align: center; padding: 20px; background: rgba(40, 120, 235, 0.05); border-radius: 12px; border: 1px solid rgba(40, 120, 235, 0.1);">
                            <div style="font-size: 2rem; color: var(--primary-color); margin-bottom: 10px;">⚡</div>
                            <h4 style="color: var(--dark-color); margin-bottom: 8px; font-size: 1rem;">Instant Results</h4>
                            <p style="color: #666; font-size: 0.85rem; margin: 0;">Get SIM data in seconds</p>
                        </div>

                        <div style="text-align: center; padding: 20px; background: rgba(255, 114, 58, 0.05); border-radius: 12px; border: 1px solid rgba(255, 114, 58, 0.1);">
                            <div style="font-size: 2rem; color: var(--secondary-color); margin-bottom: 10px;">🔒</div>
                            <h4 style="color: var(--dark-color); margin-bottom: 8px; font-size: 1rem;">100% Secure</h4>
                            <p style="color: #666; font-size: 0.85rem; margin: 0;">Private & encrypted searches</p>
                        </div>

                        <div style="text-align: center; padding: 20px; background: rgba(40, 120, 235, 0.05); border-radius: 12px; border: 1px solid rgba(40, 120, 235, 0.1);">
                            <div style="font-size: 2rem; color: var(--primary-color); margin-bottom: 10px;">📊</div>
                            <h4 style="color: var(--dark-color); margin-bottom: 8px; font-size: 1rem;">Updated 2025</h4>
                            <p style="color: #666; font-size: 0.85rem; margin: 0;">Latest database records</p>
                        </div>

                        <div style="text-align: center; padding: 20px; background: rgba(255, 114, 58, 0.05); border-radius: 12px; border: 1px solid rgba(255, 114, 58, 0.1);">
                            <div style="font-size: 2rem; color: var(--secondary-color); margin-bottom: 10px;">🌐</div>
                            <h4 style="color: var(--dark-color); margin-bottom: 8px; font-size: 1rem;">All Networks</h4>
                            <p style="color: #666; font-size: 0.85rem; margin: 0;">Jazz, Telenor, Zong, Ufone</p>
                        </div>
                    </div>

                    <!-- Quick Examples -->
                    <div style="margin-top: 25px; text-align: center; font-size: 0.9rem; color: #666;">
                        <p style="margin-bottom: 15px;"><strong>🚀 Try These Examples:</strong></p>
                        <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                            <span style="background: rgba(40, 120, 235, 0.1); color: var(--primary-color); padding: 8px 15px; border-radius: 25px; cursor: pointer; transition: all 0.3s ease;" onclick="fillExample('03001234567')" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">📱 Jazz Number</span>
                            <span style="background: rgba(255, 114, 58, 0.1); color: var(--secondary-color); padding: 8px 15px; border-radius: 25px; cursor: pointer; transition: all 0.3s ease;" onclick="fillExample('1234512345671')" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">🆔 CNIC Check</span>
                            <span style="background: rgba(40, 120, 235, 0.1); color: var(--primary-color); padding: 8px 15px; border-radius: 25px; cursor: pointer; transition: all 0.3s ease;" onclick="fillExample('03211234567')" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">📱 Telenor Number</span>
                            <span style="background: rgba(255, 114, 58, 0.1); color: var(--secondary-color); padding: 8px 15px; border-radius: 25px; cursor: pointer; transition: all 0.3s ease;" onclick="fillExample('03101234567')" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">📱 Zong Number</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Results -->
    <section class="search-results" id="searchResults" style="display: none;">
        <div class="container">
            <div class="results-header">
                <h2><i class="fas fa-database"></i> SIM Data Results</h2>
                <button class="btn btn-primary" onclick="clearResults()">
                    <i class="fas fa-times"></i> Clear Results
                </button>
            </div>
            <div class="results-content" id="resultsContent">
                <!-- Results will be displayed here -->
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section style="padding: 80px 0;">
        <div class="container">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5rem; color: var(--dark-color); margin-bottom: 20px;">SIM Data Online Features</h2>
                <p style="font-size: 1.1rem; color: #666;">Comprehensive online SIM verification and data checking services</p>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Mobile Number Lookup</h3>
                    <p style="color: #666; line-height: 1.6;">Search any Pakistani mobile number to get complete owner details and registration information.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">CNIC SIM Check</h3>
                    <p style="color: #666; line-height: 1.6;">Find all SIM cards registered against any CNIC number across all Pakistani networks.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">All Networks Supported</h3>
                    <p style="color: #666; line-height: 1.6;">Check SIM data for Jazz, Telenor, Zong, Ufone, and Warid networks in one place.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Updated Database 2025</h3>
                    <p style="color: #666; line-height: 1.6;">Access the most recent SIM database updated for 2025 with latest registrations.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Instant Results</h3>
                    <p style="color: #666; line-height: 1.6;">Get SIM data results instantly without any delays or waiting time.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Secure & Private</h3>
                    <p style="color: #666; line-height: 1.6;">All searches are encrypted and private. We don't store your search history.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How to Use -->
    <section style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5rem; color: var(--dark-color); margin-bottom: 20px;">How to Check SIM Data Online</h2>
                <p style="font-size: 1.1rem; color: #666;">Follow these simple steps to check SIM information online</p>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px;">
                <div style="text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 1.5rem; font-weight: 700;">
                        1
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Select Search Type</h3>
                    <p style="color: #666; line-height: 1.6;">Choose whether you want to search by mobile number or CNIC number.</p>
                </div>
                
                <div style="text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 1.5rem; font-weight: 700;">
                        2
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Enter Number</h3>
                    <p style="color: #666; line-height: 1.6;">Type the mobile number or CNIC you want to search for in the input field.</p>
                </div>
                
                <div style="text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 1.5rem; font-weight: 700;">
                        3
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Click Search</h3>
                    <p style="color: #666; line-height: 1.6;">Press the search button and our system will query the database instantly.</p>
                </div>
                
                <div style="text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 1.5rem; font-weight: 700;">
                        4
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">View Results</h3>
                    <p style="color: #666; line-height: 1.6;">Get comprehensive SIM data including owner details, network info, and more.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section style="padding: 80px 0; background: var(--gradient-primary); color: #fff; text-align: center;">
        <div class="container">
            <h2 style="font-size: 2.5rem; margin-bottom: 20px;">Start Checking SIM Data Online</h2>
            <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 30px;">Access Pakistan's most comprehensive SIM database with instant results.</p>
            <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
                <a href="#simSearchForm" class="btn btn-secondary" style="background: #fff; color: var(--primary-color);">
                    <i class="fas fa-search"></i> Start Searching
                </a>
                <a href="contact.html" class="btn" style="background: rgba(255, 255, 255, 0.2); color: #fff; border: 2px solid #fff;">
                    <i class="fas fa-headset"></i> Get Support
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" style="background: var(--dark-color); color: #fff; padding: 50px 0 20px;">
        <div class="container">
            <div class="footer-content" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 30px;">
                <div class="footer-section">
                    <h3 style="margin-bottom: 20px; color: var(--primary-color);">PAKSIM</h3>
                    <p style="opacity: 0.8; line-height: 1.6; margin-bottom: 20px;">SIM Database | SIM Owner Details | CNIC SIM Check | Pak SIM Data | Live Tracker</p>
                </div>
                
                <div class="footer-section">
                    <h4 style="margin-bottom: 20px;">QUICK LINKS</h4>
                    <ul style="list-style: none;">
                        <li style="margin-bottom: 10px;"><a href="index.html" style="color: #ccc; text-decoration: none;">Homepage</a></li>
                        <li style="margin-bottom: 10px;"><a href="sim-owner-details.html" style="color: #ccc; text-decoration: none;">SIM Owner Details</a></li>
                        <li style="margin-bottom: 10px;"><a href="sim-data-online.html" style="color: #ccc; text-decoration: none;">CNIC SIM Check</a></li>
                        <li style="margin-bottom: 10px;"><a href="live-tracker.html" style="color: #ccc; text-decoration: none;">Live Tracker</a></li>
                        <li style="margin-bottom: 10px;"><a href="about.html" style="color: #ccc; text-decoration: none;">About Us</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 style="margin-bottom: 20px;">CONTACT US</h4>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-phone" style="color: var(--primary-color); margin-right: 10px;"></i>
                        <span>+92 300 1234567</span>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-envelope" style="color: var(--primary-color); margin-right: 10px;"></i>
                        <span><EMAIL></span>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom" style="border-top: 1px solid #444; padding-top: 20px; text-align: center;">
                <p style="opacity: 0.8;">© 2025 PakSim – SIM Database Online & CNIC SIM Information System for Pakistan. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/real-scraper.js"></script>
    <script src="js/main-app.js"></script>
</body>
</html>
