<!DOCTYPE html>
<html>
<head>
    <title>Test with Manual CSR</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        textarea { width: 100%; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Test Certificate Creation with Manual CSR</h1>
    
    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $testDomain = trim($_POST['domain']);
        $testEmail = trim($_POST['email']);
        $manualCSR = trim($_POST['csr']);
        
        echo "<h2>Testing with Manual CSR</h2>";
        echo "<p><strong>Domain:</strong> $testDomain</p>";
        echo "<p><strong>Email:</strong> $testEmail</p>";
        echo "<p><strong>CSR Length:</strong> " . strlen($manualCSR) . " characters</p>";
        
        // Test the new certificate creation API with manual CSR
        $postData = json_encode([
            'domain' => $testDomain,
            'email' => $testEmail,
            'manual_csr' => $manualCSR
        ]);
        
        echo "<h3>Request Data:</h3>";
        echo "<pre>" . htmlspecialchars($postData) . "</pre>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost/ssl4free/create-cert-new.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "<h3>Response:</h3>";
        echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
        
        if ($error) {
            echo "<p class='error'>❌ cURL Error: $error</p>";
        } else {
            echo "<p class='success'>✅ cURL Success</p>";
        }
        
        echo "<p><strong>Response Body:</strong></p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        $responseData = json_decode($response, true);
        if ($responseData) {
            if (isset($responseData['success']) && $responseData['success']) {
                echo "<p class='success'>🎉 SUCCESS! Certificate created with manual CSR!</p>";
                
                if (isset($responseData['data']['certificate_id'])) {
                    echo "<p class='success'>Certificate ID: " . $responseData['data']['certificate_id'] . "</p>";
                }
                
                if (isset($responseData['data']['validation'])) {
                    echo "<h4>Validation Instructions:</h4>";
                    echo "<pre>" . htmlspecialchars(json_encode($responseData['data']['validation'], JSON_PRETTY_PRINT)) . "</pre>";
                }
            } else {
                echo "<p class='error'>❌ FAILED: " . ($responseData['message'] ?? 'Unknown error') . "</p>";
                
                if (isset($responseData['details'])) {
                    echo "<h4>Error Details:</h4>";
                    echo "<pre>" . htmlspecialchars(json_encode($responseData['details'], JSON_PRETTY_PRINT)) . "</pre>";
                }
            }
        }
    }
    ?>
    
    <h2>Generate SSL Certificate with Manual CSR</h2>
    
    <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3>📋 How to Generate CSR:</h3>
        <ol>
            <li>Go to: <a href="https://www.ssl.com/online-csr-generator/" target="_blank">SSL.com CSR Generator</a></li>
            <li>Enter your domain name (e.g., test.jobzhit.com)</li>
            <li>Fill in organization details</li>
            <li>Generate and copy the CSR</li>
            <li>Paste it below</li>
        </ol>
    </div>
    
    <form method="POST">
        <div style="margin: 10px 0;">
            <label><strong>Domain Name:</strong></label><br>
            <input type="text" name="domain" value="test.jobzhit.com" style="width: 300px; padding: 5px;" required>
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>Email Address:</strong></label><br>
            <input type="email" name="email" value="<EMAIL>" style="width: 300px; padding: 5px;" required>
        </div>
        
        <div style="margin: 10px 0;">
            <label><strong>Certificate Signing Request (CSR):</strong></label><br>
            <textarea name="csr" rows="15" placeholder="-----BEGIN CERTIFICATE REQUEST-----
Paste your CSR here...
-----END CERTIFICATE REQUEST-----" required></textarea>
        </div>
        
        <button type="submit" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            Test Certificate Creation
        </button>
    </form>
    
    <h3>Sample Valid CSR for Testing:</h3>
    <p>You can use this sample CSR for testing (replace domain in the CSR generator):</p>
    <textarea rows="10" readonly style="background: #f9f9f9;">-----BEGIN CERTIFICATE REQUEST-----
MIICvjCCAaYCAQAwejELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAkNBMQswCQYDVQQH
DAJTRjESMBAGA1UECgwJU1NMNEZyZWUxFjAUBgNVBAsMDUlUIERlcGFydG1lbnQx
GjAYBgNVBAMMEXRlc3Quam9iemhpdC5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQDZndBfQL3cXzrXYAECAwQFBgcICQoLDA0ODxAREhMUFRYXGBka
GxwdHh8gISIjJCUmJygpKissLS4vMDEyMzQ1Njc4OTo7PD0+P0BBQkNERUZHSElK
S0xNTk9QUVJTVFVWV1hZWltcXV5fYGFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6
e3x9fn+AgYKDhIWGh4iJiouMjY6PkJGSk5SVlpeYmZqbnJ2en6ChoqOkpaanqKmq
q6ytrq+wsbKztLW2t7i5uru8vb6/wMHCw8TFxsfIycrLzM3Oz9DR0tPU1dbX2Nna
29zd3t/g4eLj5OXm5+jp6uvs7e7v8PHy8/T19vf4+fr7/P3+/wIDAQABoAAwDQYJ
KoZIhvcNAQELBQADggEBAExample_CSR_Content_Here_For_Testing_Purposes
-----END CERTIFICATE REQUEST-----</textarea>
    
    <p><a href="test-new-cert.php">← Back to Test</a></p>
</body>
</html>
