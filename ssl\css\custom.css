/* Custom CSS for SSL Generator */

/* SSL Color Variables */
:root {
    --ssl-green: #10b981;
    --ssl-green-dark: #059669;
    --ssl-blue: #3b82f6;
    --ssl-blue-dark: #1e40af;
}

/* SSL Color Classes */
.text-ssl-green {
    color: var(--ssl-green) !important;
}

.bg-ssl-green {
    background-color: var(--ssl-green) !important;
}

.border-ssl-green {
    border-color: var(--ssl-green) !important;
}

.text-ssl-blue {
    color: var(--ssl-blue) !important;
}

.bg-ssl-blue {
    background-color: var(--ssl-blue) !important;
}

.border-ssl-blue {
    border-color: var(--ssl-blue) !important;
}

/* Step Indicators */
.step-active {
    background-color: var(--ssl-green) !important;
    color: white !important;
}

.step-inactive {
    background-color: #e5e7eb !important;
    color: #6b7280 !important;
}

/* Additional animations and effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Utility classes */
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.bounce-animation {
    animation: bounce 2s infinite;
}

/* Custom gradients */
.gradient-ssl {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.gradient-security {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

.gradient-text {
    background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced button styles */
.btn-ssl {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-ssl:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

.btn-ssl:active {
    transform: translateY(0);
}

.btn-security {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-security:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

/* Enhanced card styles */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* SSL badge styles */
.ssl-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: #059669;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin: 4px;
}

.security-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: #1e40af;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin: 4px;
}

/* Progress bar enhancements */
.progress-bar {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    height: 8px;
    border-radius: 4px;
    transition: width 0.5s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* Step indicator enhancements */
.step-indicator {
    position: relative;
    z-index: 1;
}

.step-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(16, 185, 129, 0.1);
    z-index: -1;
    transition: all 0.3s ease;
}

.step-indicator.active::before {
    background: rgba(16, 185, 129, 0.2);
    transform: translate(-50%, -50%) scale(1.2);
}

/* Form enhancements */
.form-input {
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
}

.form-input:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    outline: none;
}

.form-input:invalid {
    border-color: #ef4444;
}

.form-input:valid {
    border-color: #10b981;
}

/* Loading spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #10b981;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 24px;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.notification.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.notification.info {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .step-indicator::before {
        width: 40px;
        height: 40px;
    }
    
    .btn-ssl,
    .btn-security {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .ssl-badge,
    .security-badge {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card-hover {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .form-input {
        background-color: #374151;
        color: #f9fafb;
        border-color: #4b5563;
    }
    
    .form-input:focus {
        border-color: #10b981;
        background-color: #374151;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.focus-visible:focus {
    outline: 2px solid #10b981;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-ssl,
    .btn-security {
        border: 2px solid currentColor;
    }
    
    .ssl-badge,
    .security-badge {
        border-width: 2px;
    }
}
