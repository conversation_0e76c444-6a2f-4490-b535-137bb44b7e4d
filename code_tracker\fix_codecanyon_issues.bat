@echo off
echo 🔧 CodeCanyon Issues Fixer
echo ========================

set "BASE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_Submission"
set "SOURCE_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_Pro_Complete"

echo 📋 Fixing CodeCanyon submission issues...

:: 1. Fix Thumbnail Issues
echo 🖼️ Creating proper thumbnail (80x80px, under 50KB)...
if not exist "%BASE_DIR%\Thumbnail" mkdir "%BASE_DIR%\Thumbnail"

:: Create a simple 80x80 thumbnail using PowerShell
powershell -Command "
Add-Type -AssemblyName System.Drawing;
$bitmap = New-Object System.Drawing.Bitmap(80, 80);
$graphics = [System.Drawing.Graphics]::FromImage($bitmap);
$graphics.Clear([System.Drawing.Color]::FromArgb(37, 211, 102));
$font = New-Object System.Drawing.Font('Arial', 12, [System.Drawing.FontStyle]::Bold);
$brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White);
$graphics.DrawString('WA', $font, $brush, 25, 25);
$graphics.DrawString('Pro', $font, $brush, 20, 45);
$bitmap.Save('%BASE_DIR%\Thumbnail\thumbnail.png', [System.Drawing.Imaging.ImageFormat]::Png);
$graphics.Dispose();
$bitmap.Dispose();
"

:: 2. Fix Theme Preview - Convert PNG to JPG with proper names
echo 🎨 Converting Theme Preview images to JPG...
if not exist "%BASE_DIR%\Theme_Preview" mkdir "%BASE_DIR%\Theme_Preview"

:: Use PowerShell to convert PNG to JPG
powershell -Command "
Add-Type -AssemblyName System.Drawing;
$sourceDir = '%SOURCE_DIR%\WhatsApp_Widget_Screenshots';
$destDir = '%BASE_DIR%\Theme_Preview';
$files = @(
    @{src='01_General_Settings_Tab.png'; dest='01_general_settings.jpg'},
    @{src='02_Agents_Management_Tab.png'; dest='02_agents_management.jpg'},
    @{src='03_Appearance_Themes_Tab.png'; dest='03_appearance_themes.jpg'},
    @{src='04_Working_Hours_Tab.png'; dest='04_working_hours.jpg'},
    @{src='05_Advanced_Settings_Tab.png'; dest='05_advanced_settings.jpg'},
    @{src='06_Preview_Tab.png'; dest='06_preview_tab.jpg'}
);
foreach($file in $files) {
    $srcPath = Join-Path $sourceDir $file.src;
    $destPath = Join-Path $destDir $file.dest;
    if(Test-Path $srcPath) {
        $img = [System.Drawing.Image]::FromFile($srcPath);
        $img.Save($destPath, [System.Drawing.Imaging.ImageFormat]::Jpeg);
        $img.Dispose();
        Write-Host \"Converted: $($file.src) -> $($file.dest)\";
    }
}
"

:: 3. Fix WordPress Theme ZIP structure
echo 📦 Creating proper WordPress Theme ZIP structure...
if exist "%BASE_DIR%\WordPress_Theme" rmdir /s /q "%BASE_DIR%\WordPress_Theme"
mkdir "%BASE_DIR%\WordPress_Theme"

:: Create proper folder structure for WordPress plugin
mkdir "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro"
copy "%SOURCE_DIR%\whatsapp-chat-widget.php" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\style.css" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\script.js" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\admin-style.css" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\admin-script.js" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\readme.txt" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\LICENSE.txt" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul

:: Copy documentation
copy "%SOURCE_DIR%\INSTALLATION-GUIDE.md" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\FAQ.md" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul
copy "%SOURCE_DIR%\DEVELOPER-GUIDE.md" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\" >nul

:: Copy demo folder
mkdir "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\demo"
copy "%SOURCE_DIR%\demo\index.html" "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro\demo\" >nul

:: Create ZIP with proper structure
echo 📦 Creating WordPress plugin ZIP...
powershell -Command "Compress-Archive -Path '%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro' -DestinationPath '%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -Force"

:: Remove the folder, keep only ZIP
rmdir /s /q "%BASE_DIR%\WordPress_Theme\whatsapp-widget-pro"

echo ✅ All issues fixed!
echo.
echo 📋 Fixed Issues:
echo    ✅ Thumbnail: Created 80x80px PNG under 50KB
echo    ✅ Theme Preview: Converted all PNG to JPG
echo    ✅ WordPress Theme: Proper ZIP structure created
echo    ✅ File naming: All files properly named
echo    ✅ File formats: Correct extensions used
echo.
echo 🎯 Ready for CodeCanyon re-submission!
pause
