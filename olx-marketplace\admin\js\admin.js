/**
 * Admin Panel JavaScript
 * OLX Marketplace
 */

const API_BASE_URL = '../backend/api';
let currentAdmin = null;

// Initialize admin panel
document.addEventListener('DOMContentLoaded', function() {
    checkAdminAuth();
    setupAdminEventListeners();
    loadDashboardData();
});

// Check admin authentication
async function checkAdminAuth() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth.php?action=check-session`);
        const data = await response.json();
        
        if (data.authenticated && data.user.is_admin) {
            currentAdmin = data.user;
            document.getElementById('adminName').textContent = data.user.full_name;
        } else {
            // Redirect to login
            window.location.href = '../index.html';
        }
    } catch (error) {
        console.error('Auth check failed:', error);
        window.location.href = '../index.html';
    }
}

// Setup event listeners
function setupAdminEventListeners() {
    // Sidebar menu navigation
    document.querySelectorAll('.menu-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active menu item
            document.querySelectorAll('.menu-link').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Show corresponding section
            const section = this.dataset.section;
            showSection(section);
        });
    });
}

// Show section
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show selected section
    const section = document.getElementById(sectionName);
    if (section) {
        section.classList.add('active');
        
        // Update page title
        const titles = {
            'dashboard': 'Dashboard',
            'users': 'Users Management',
            'products': 'Products Management',
            'categories': 'Categories Management',
            'reports': 'Reports & Complaints',
            'settings': 'Site Settings',
            'analytics': 'Analytics & Statistics'
        };
        
        document.getElementById('pageTitle').textContent = titles[sectionName] || 'Admin Panel';
        
        // Load section data
        loadSectionData(sectionName);
    }
}

// Load section data
function loadSectionData(sectionName) {
    switch(sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'users':
            loadUsersData();
            break;
        case 'products':
            loadProductsData();
            break;
        case 'categories':
            loadCategoriesData();
            break;
        case 'reports':
            loadReportsData();
            break;
        case 'settings':
            loadSettingsData();
            break;
        case 'analytics':
            loadAnalyticsData();
            break;
    }
}

// Load dashboard data
async function loadDashboardData() {
    try {
        // Load statistics
        const [usersResponse, productsResponse] = await Promise.all([
            fetch(`${API_BASE_URL}/admin.php?action=get-stats&type=users`),
            fetch(`${API_BASE_URL}/admin.php?action=get-stats&type=products`)
        ]);
        
        if (usersResponse.ok) {
            const usersData = await usersResponse.json();
            document.getElementById('totalUsers').textContent = usersData.total || 0;
        }
        
        if (productsResponse.ok) {
            const productsData = await productsResponse.json();
            document.getElementById('totalProducts').textContent = productsData.total || 0;
        }
        
        // Load recent activities
        loadRecentActivities();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

// Load recent activities
async function loadRecentActivities() {
    try {
        const response = await fetch(`${API_BASE_URL}/admin.php?action=recent-activities`);
        const data = await response.json();
        
        const container = document.getElementById('recentActivities');
        
        if (response.ok && data.activities && data.activities.length > 0) {
            container.innerHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Activity</th>
                            <th>User</th>
                            <th>Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.activities.map(activity => `
                            <tr>
                                <td>${activity.description}</td>
                                <td>${activity.user_name}</td>
                                <td>${formatTimeAgo(activity.created_at)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        } else {
            container.innerHTML = '<p>No recent activities found.</p>';
        }
    } catch (error) {
        console.error('Error loading recent activities:', error);
        document.getElementById('recentActivities').innerHTML = '<p>Error loading activities.</p>';
    }
}

// Load users data
async function loadUsersData() {
    try {
        const response = await fetch(`${API_BASE_URL}/admin.php?action=get-users`);
        const data = await response.json();
        
        const container = document.getElementById('usersTable');
        
        if (response.ok && data.users && data.users.length > 0) {
            container.innerHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Full Name</th>
                            <th>Status</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.users.map(user => `
                            <tr>
                                <td>${user.id}</td>
                                <td>${user.username}</td>
                                <td>${user.email}</td>
                                <td>${user.full_name}</td>
                                <td>
                                    <span class="status-badge ${user.is_active ? 'status-active' : 'status-inactive'}">
                                        ${user.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </td>
                                <td>${formatDate(user.created_at)}</td>
                                <td>
                                    <button class="btn btn-sm" onclick="editUser(${user.id})">Edit</button>
                                    <button class="btn btn-danger btn-sm" onclick="toggleUserStatus(${user.id})">
                                        ${user.is_active ? 'Deactivate' : 'Activate'}
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        } else {
            container.innerHTML = '<p>No users found.</p>';
        }
    } catch (error) {
        console.error('Error loading users:', error);
        document.getElementById('usersTable').innerHTML = '<p>Error loading users.</p>';
    }
}

// Load products data
async function loadProductsData() {
    try {
        const response = await fetch(`${API_BASE_URL}/admin.php?action=get-products`);
        const data = await response.json();
        
        const container = document.getElementById('productsTable');
        
        if (response.ok && data.products && data.products.length > 0) {
            container.innerHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Seller</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.products.map(product => `
                            <tr>
                                <td>${product.id}</td>
                                <td>${product.title}</td>
                                <td>${product.category_name || 'N/A'}</td>
                                <td>Rs ${Number(product.price).toLocaleString()}</td>
                                <td>${product.seller_name}</td>
                                <td>
                                    <span class="status-badge ${product.is_active ? 'status-active' : 'status-inactive'}">
                                        ${product.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </td>
                                <td>${formatDate(product.created_at)}</td>
                                <td>
                                    <button class="btn btn-sm" onclick="editProduct(${product.id})">Edit</button>
                                    <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})">Delete</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        } else {
            container.innerHTML = '<p>No products found.</p>';
        }
    } catch (error) {
        console.error('Error loading products:', error);
        document.getElementById('productsTable').innerHTML = '<p>Error loading products.</p>';
    }
}

// Load categories data
async function loadCategoriesData() {
    try {
        const response = await fetch(`${API_BASE_URL}/admin.php?action=get-categories`);
        const data = await response.json();
        
        const container = document.getElementById('categoriesTable');
        
        if (response.ok && data.categories && data.categories.length > 0) {
            container.innerHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Slug</th>
                            <th>Icon</th>
                            <th>Products Count</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.categories.map(category => `
                            <tr>
                                <td>${category.id}</td>
                                <td>${category.name}</td>
                                <td>${category.slug}</td>
                                <td><i class="${category.icon}"></i></td>
                                <td>${category.products_count || 0}</td>
                                <td>
                                    <span class="status-badge ${category.is_active ? 'status-active' : 'status-inactive'}">
                                        ${category.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm" onclick="editCategory(${category.id})">Edit</button>
                                    <button class="btn btn-danger btn-sm" onclick="deleteCategory(${category.id})">Delete</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        } else {
            container.innerHTML = '<p>No categories found.</p>';
        }
    } catch (error) {
        console.error('Error loading categories:', error);
        document.getElementById('categoriesTable').innerHTML = '<p>Error loading categories.</p>';
    }
}

// Load reports data
async function loadReportsData() {
    document.getElementById('reportsTable').innerHTML = '<p>Reports functionality will be implemented.</p>';
}

// Load settings data
async function loadSettingsData() {
    document.getElementById('settingsForm').innerHTML = '<p>Settings functionality will be implemented.</p>';
}

// Load analytics data
async function loadAnalyticsData() {
    document.getElementById('analyticsContent').innerHTML = '<p>Analytics functionality will be implemented.</p>';
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return `${Math.ceil(diffDays / 30)} months ago`;
}

// Action functions
function editUser(userId) {
    showNotification('Edit user functionality will be implemented', 'info');
}

function toggleUserStatus(userId) {
    showNotification('Toggle user status functionality will be implemented', 'info');
}

function editProduct(productId) {
    showNotification('Edit product functionality will be implemented', 'info');
}

function deleteProduct(productId) {
    if (confirm('Are you sure you want to delete this product?')) {
        showNotification('Delete product functionality will be implemented', 'info');
    }
}

function editCategory(categoryId) {
    showNotification('Edit category functionality will be implemented', 'info');
}

function deleteCategory(categoryId) {
    if (confirm('Are you sure you want to delete this category?')) {
        showNotification('Delete category functionality will be implemented', 'info');
    }
}

// Modal functions
function showAddUserModal() {
    showNotification('Add user modal will be implemented', 'info');
}

function showAddProductModal() {
    showNotification('Add product modal will be implemented', 'info');
}

function showAddCategoryModal() {
    showNotification('Add category modal will be implemented', 'info');
}

function saveSettings() {
    showNotification('Save settings functionality will be implemented', 'info');
}

// Logout function
async function logout() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth.php?action=logout`, {
            method: 'POST'
        });
        
        if (response.ok) {
            window.location.href = '../index.html';
        }
    } catch (error) {
        console.error('Logout error:', error);
        window.location.href = '../index.html';
    }
}
