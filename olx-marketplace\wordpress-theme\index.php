<?php
/**
 * The main template file
 *
 * @package OLX_Marketplace
 */

get_header(); ?>

<!-- Categories Section -->
<section class="categories">
    <div class="container">
        <h2>Browse Categories</h2>
        <div class="category-grid">
            <?php
            $categories = get_terms(array(
                'taxonomy' => 'product_category',
                'hide_empty' => false,
                'number' => 8
            ));
            
            $category_icons = array(
                'mobiles' => 'fas fa-mobile-alt',
                'cars' => 'fas fa-car',
                'bikes' => 'fas fa-motorcycle',
                'houses' => 'fas fa-home',
                'electronics' => 'fas fa-laptop',
                'fashion' => 'fas fa-tshirt',
                'furniture' => 'fas fa-couch',
                'jobs' => 'fas fa-briefcase'
            );
            
            if (!empty($categories)) :
                foreach ($categories as $category) :
                    $icon = isset($category_icons[$category->slug]) ? $category_icons[$category->slug] : 'fas fa-tag';
                    ?>
                    <div class="category-card" data-category="<?php echo esc_attr($category->slug); ?>">
                        <i class="<?php echo esc_attr($icon); ?>"></i>
                        <h3><?php echo esc_html($category->name); ?></h3>
                        <p><?php echo esc_html($category->count); ?> ads</p>
                    </div>
                    <?php
                endforeach;
            else :
                // Default categories if none exist
                $default_categories = array(
                    array('name' => 'Mobiles', 'icon' => 'fas fa-mobile-alt', 'count' => '1,234'),
                    array('name' => 'Cars', 'icon' => 'fas fa-car', 'count' => '856'),
                    array('name' => 'Bikes', 'icon' => 'fas fa-motorcycle', 'count' => '642'),
                    array('name' => 'Houses', 'icon' => 'fas fa-home', 'count' => '423'),
                    array('name' => 'Electronics', 'icon' => 'fas fa-laptop', 'count' => '789'),
                    array('name' => 'Fashion', 'icon' => 'fas fa-tshirt', 'count' => '567'),
                    array('name' => 'Furniture', 'icon' => 'fas fa-couch', 'count' => '345'),
                    array('name' => 'Jobs', 'icon' => 'fas fa-briefcase', 'count' => '234')
                );
                
                foreach ($default_categories as $category) :
                    ?>
                    <div class="category-card">
                        <i class="<?php echo esc_attr($category['icon']); ?>"></i>
                        <h3><?php echo esc_html($category['name']); ?></h3>
                        <p><?php echo esc_html($category['count']); ?> ads</p>
                    </div>
                    <?php
                endforeach;
            endif;
            ?>
        </div>
    </div>
</section>

<!-- Featured Products -->
<section class="featured-products">
    <div class="container">
        <h2>Fresh Recommendations</h2>
        <div class="products-grid" id="productsGrid">
            <?php
            $products_query = new WP_Query(array(
                'post_type' => 'product',
                'post_status' => 'publish',
                'posts_per_page' => 12,
                'orderby' => 'date',
                'order' => 'DESC'
            ));
            
            if ($products_query->have_posts()) :
                while ($products_query->have_posts()) : $products_query->the_post();
                    $price = get_post_meta(get_the_ID(), '_product_price', true);
                    $location = get_post_meta(get_the_ID(), '_product_location', true);
                    $formatted_price = $price ? 'Rs ' . number_format($price) : 'Price on request';
                    ?>
                    <div class="product-card" onclick="window.location.href='<?php echo esc_url(get_permalink()); ?>'">
                        <div class="product-image">
                            <?php if (has_post_thumbnail()) : ?>
                                <?php the_post_thumbnail('medium', array('alt' => get_the_title())); ?>
                            <?php else : ?>
                                <img src="https://via.placeholder.com/280x200/f0f0f0/666?text=No+Image" alt="<?php echo esc_attr(get_the_title()); ?>">
                            <?php endif; ?>
                        </div>
                        <div class="product-info">
                            <div class="product-price"><?php echo esc_html($formatted_price); ?></div>
                            <div class="product-title"><?php echo esc_html(wp_trim_words(get_the_title(), 8)); ?></div>
                            <div class="product-location">
                                <i class="fas fa-map-marker-alt"></i> 
                                <?php echo esc_html($location ? $location : 'Location not specified'); ?>
                            </div>
                            <div class="product-date"><?php echo esc_html(human_time_diff(get_the_time('U'), current_time('timestamp')) . ' ago'); ?></div>
                        </div>
                    </div>
                    <?php
                endwhile;
                wp_reset_postdata();
            else :
                // Show sample products if no products exist
                ?>
                <div style="grid-column: 1/-1; text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-box-open" style="font-size: 48px; margin-bottom: 20px; display: block;"></i>
                    <h3>No products found</h3>
                    <p>Be the first to post a product!</p>
                    <a href="<?php echo esc_url(wp_registration_url()); ?>" class="btn-primary" style="display: inline-block; margin-top: 15px; text-decoration: none;">Get Started</a>
                </div>
                <?php
            endif;
            ?>
        </div>
        
        <?php if ($products_query->found_posts > 12) : ?>
        <div class="load-more">
            <button class="load-more-btn" data-page="1" data-max-pages="<?php echo esc_attr($products_query->max_num_pages); ?>">Load More</button>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Blog Posts Section (if any) -->
<?php
$blog_query = new WP_Query(array(
    'post_type' => 'post',
    'post_status' => 'publish',
    'posts_per_page' => 3,
    'orderby' => 'date',
    'order' => 'DESC'
));

if ($blog_query->have_posts()) :
?>
<section class="blog-section" style="padding: 40px 0; background: #fff;">
    <div class="container">
        <h2 style="margin-bottom: 30px; color: #002f34; font-size: 28px;">Latest News & Tips</h2>
        <div class="blog-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <?php
            while ($blog_query->have_posts()) : $blog_query->the_post();
                ?>
                <article class="blog-card" style="background: #f8f9fa; border-radius: 8px; overflow: hidden; transition: transform 0.3s;">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="blog-image" style="height: 200px; overflow: hidden;">
                            <?php the_post_thumbnail('medium', array('style' => 'width: 100%; height: 100%; object-fit: cover;')); ?>
                        </div>
                    <?php endif; ?>
                    <div class="blog-content" style="padding: 20px;">
                        <h3 style="margin-bottom: 10px; color: #002f34;">
                            <a href="<?php echo esc_url(get_permalink()); ?>" style="text-decoration: none; color: inherit;">
                                <?php echo esc_html(wp_trim_words(get_the_title(), 8)); ?>
                            </a>
                        </h3>
                        <p style="color: #666; margin-bottom: 10px;"><?php echo esc_html(wp_trim_words(get_the_excerpt(), 15)); ?></p>
                        <div style="color: #999; font-size: 12px;"><?php echo esc_html(get_the_date()); ?></div>
                    </div>
                </article>
                <?php
            endwhile;
            wp_reset_postdata();
            ?>
        </div>
    </div>
</section>
<?php endif; ?>

<?php get_footer(); ?>
