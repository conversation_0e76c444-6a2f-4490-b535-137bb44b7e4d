/**
 * Product Details Page JavaScript
 * OLX Marketplace
 */

let currentProduct = null;

// Initialize product details page
document.addEventListener('DOMContentLoaded', function() {
    loadProductDetails();
    setupProductDetailsEventListeners();
});

// Load product details
async function loadProductDetails() {
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id');
    const productSlug = urlParams.get('slug');
    
    if (!productId && !productSlug) {
        showError('Product not found');
        return;
    }
    
    try {
        showLoadingSpinner();
        
        const identifier = productSlug || productId;
        const param = productSlug ? 'slug' : 'id';
        
        const response = await fetch(`${API_BASE_URL}/products.php?action=read-one&${param}=${identifier}`);
        const product = await response.json();
        
        if (response.ok) {
            currentProduct = product;
            renderProductDetails(product);
            updateBreadcrumb(product);
            loadRelatedProducts(product.category_id, product.id);
        } else {
            showError(product.message || 'Product not found');
        }
    } catch (error) {
        console.error('Error loading product:', error);
        showError('Failed to load product details');
    } finally {
        hideLoadingSpinner();
    }
}

// Render product details
function renderProductDetails(product) {
    const container = document.getElementById('productDetailContainer');
    
    const imageUrl = product.images && product.images.length > 0 
        ? `backend/uploads/products/${product.images[0].image_path}` 
        : 'https://via.placeholder.com/600x400/f0f0f0/666?text=No+Image';
    
    const price = product.price ? `Rs ${Number(product.price).toLocaleString()}` : 'Price on request';
    const timeAgo = formatTimeAgo(product.created_at);
    
    container.innerHTML = `
        <!-- Product Images and Details -->
        <div class="product-main">
            <!-- Product Images -->
            <div class="product-images">
                <div class="main-image">
                    <img id="mainImage" src="${imageUrl}" alt="${product.title}">
                </div>
                
                ${product.images && product.images.length > 1 ? `
                <div class="image-gallery">
                    ${product.images.map((img, index) => `
                        <img src="backend/uploads/products/${img.image_path}" 
                             alt="Product Image ${index + 1}"
                             onclick="changeMainImage(this.src)"
                             ${index === 0 ? 'class="active"' : ''}>
                    `).join('')}
                </div>
                ` : ''}
            </div>
            
            <!-- Product Description -->
            <div class="product-description">
                <h2>Description</h2>
                <div class="content">
                    ${product.description || '<p style="color: #666; font-style: italic;">No description provided for this product.</p>'}
                </div>
            </div>
            
            <!-- Related Products will be loaded here -->
            <div id="relatedProductsContainer"></div>
        </div>
        
        <!-- Sidebar with Product Info and Contact -->
        <div class="product-sidebar">
            <!-- Product Info Card -->
            <div class="product-info-card">
                <h1>${product.title}</h1>
                
                <div class="product-price">${price}</div>
                
                <div class="product-meta">
                    <div class="meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        ${product.location || 'Location not specified'}
                    </div>
                    
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        ${timeAgo}
                    </div>
                    
                    ${product.condition_type ? `
                    <div class="meta-item">
                        <i class="fas fa-info-circle"></i>
                        Condition: ${product.condition_type.charAt(0).toUpperCase() + product.condition_type.slice(1)}
                    </div>
                    ` : ''}
                    
                    ${product.category_name ? `
                    <div class="meta-item">
                        <i class="fas fa-tag"></i>
                        ${product.category_name}
                    </div>
                    ` : ''}
                    
                    <div class="meta-item">
                        <i class="fas fa-eye"></i>
                        ${product.views_count || 0} views
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="btn-primary" onclick="showContactInfo()">
                        <i class="fas fa-phone"></i> Show Contact Info
                    </button>
                    
                    <button class="btn-secondary" onclick="toggleFavorite(${product.id})">
                        <i class="fas fa-heart"></i> Add to Favorites
                    </button>
                    
                    <button class="btn-secondary" onclick="shareProduct()">
                        <i class="fas fa-share"></i> Share
                    </button>
                </div>
            </div>
            
            <!-- Contact Info Card (Hidden by default) -->
            <div id="contactInfo" class="contact-info-card">
                <h3>Contact Information</h3>
                
                ${product.contact_phone ? `
                <div class="contact-item">
                    <strong>Phone:</strong><br>
                    <a href="tel:${product.contact_phone}" style="color: #002f34; text-decoration: none; font-size: 18px;">
                        ${product.contact_phone}
                    </a>
                </div>
                ` : ''}
                
                ${product.contact_email ? `
                <div class="contact-item">
                    <strong>Email:</strong><br>
                    <a href="mailto:${product.contact_email}" style="color: #002f34; text-decoration: none;">
                        ${product.contact_email}
                    </a>
                </div>
                ` : ''}
                
                <div class="contact-item">
                    <strong>Seller:</strong><br>
                    ${product.seller_name || product.username}
                </div>
            </div>
            
            <!-- Safety Tips -->
            <div class="safety-tips">
                <h4><i class="fas fa-shield-alt"></i> Safety Tips</h4>
                <ul>
                    <li>Meet in a public place</li>
                    <li>Check the item before payment</li>
                    <li>Pay only after receiving the item</li>
                    <li>Beware of unrealistic offers</li>
                </ul>
            </div>
        </div>
    `;
    
    // Update page title
    document.title = `${product.title} - OLX Marketplace`;
}

// Load related products
async function loadRelatedProducts(categoryId, currentProductId) {
    try {
        const response = await fetch(`${API_BASE_URL}/products.php?action=read&category_id=${categoryId}&limit=4`);
        const data = await response.json();
        
        if (response.ok && data.products.length > 0) {
            // Filter out current product
            const relatedProducts = data.products.filter(p => p.id != currentProductId);
            
            if (relatedProducts.length > 0) {
                renderRelatedProducts(relatedProducts);
            }
        }
    } catch (error) {
        console.error('Error loading related products:', error);
    }
}

// Render related products
function renderRelatedProducts(products) {
    const container = document.getElementById('relatedProductsContainer');
    
    container.innerHTML = `
        <div class="related-products">
            <h3>Related Products</h3>
            <div class="related-grid">
                ${products.map(product => {
                    const imageUrl = product.primary_image 
                        ? `backend/uploads/products/${product.primary_image}` 
                        : 'https://via.placeholder.com/200x150/f0f0f0/666?text=No+Image';
                    
                    const price = product.price ? `Rs ${Number(product.price).toLocaleString()}` : 'Price on request';
                    
                    return `
                        <div class="related-product" onclick="window.location.href='product-details.html?${product.slug ? 'slug=' + product.slug : 'id=' + product.id}'">
                            <div class="related-image">
                                <img src="${imageUrl}" alt="${product.title}">
                            </div>
                            <div class="related-info">
                                <div class="related-price">${price}</div>
                                <div class="related-title">${product.title.length > 30 ? product.title.substring(0, 30) + '...' : product.title}</div>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>
    `;
}

// Update breadcrumb
function updateBreadcrumb(product) {
    const categoryElement = document.getElementById('breadcrumbCategory');
    const titleElement = document.getElementById('breadcrumbTitle');
    
    if (categoryElement && product.category_name) {
        categoryElement.textContent = product.category_name;
    }
    
    if (titleElement) {
        const shortTitle = product.title.length > 30 ? product.title.substring(0, 30) + '...' : product.title;
        titleElement.textContent = shortTitle;
    }
}

// Setup event listeners
function setupProductDetailsEventListeners() {
    // Search functionality
    const searchBtn = document.querySelector('.search-btn');
    const searchInput = document.getElementById('searchInput');
    
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            const searchTerm = searchInput.value.trim();
            const location = document.getElementById('locationSelect').value;
            
            if (searchTerm || location) {
                let searchUrl = 'index.html?';
                if (searchTerm) searchUrl += 'search=' + encodeURIComponent(searchTerm);
                if (location) searchUrl += (searchTerm ? '&' : '') + 'location=' + encodeURIComponent(location);
                
                window.location.href = searchUrl;
            }
        });
    }
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchBtn.click();
            }
        });
    }
}

// Change main image
function changeMainImage(src) {
    const mainImage = document.getElementById('mainImage');
    if (mainImage) {
        mainImage.src = src;
    }
    
    // Update active state
    document.querySelectorAll('.image-gallery img').forEach(img => {
        img.classList.remove('active');
    });
    
    event.target.classList.add('active');
}

// Show contact info
function showContactInfo() {
    const contactInfo = document.getElementById('contactInfo');
    if (contactInfo) {
        if (contactInfo.style.display === 'none' || contactInfo.style.display === '') {
            contactInfo.style.display = 'block';
            contactInfo.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        } else {
            contactInfo.style.display = 'none';
        }
    }
}

// Toggle favorite
function toggleFavorite(productId) {
    if (!currentUser) {
        showError('Please login to add favorites');
        return;
    }
    
    // Implement favorite functionality
    showSuccess('Favorite functionality will be implemented');
}

// Share product
function shareProduct() {
    if (navigator.share && currentProduct) {
        navigator.share({
            title: currentProduct.title,
            text: `Check out this product on OLX Marketplace`,
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(function() {
            showSuccess('Product link copied to clipboard!');
        }).catch(function() {
            showError('Failed to copy link');
        });
    }
}
