<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free SSL Generator - Secure Your Website with Free SSL Certificate</title>
    <meta name="description" content="Generate free SSL certificates for your website. Secure your domain with trusted SSL encryption. Easy, fast, and completely free.">
    <meta name="keywords" content="free ssl certificate, ssl generator, website security, https, encryption">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .ssl-green {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .security-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        }
        .step-active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        .step-inactive {
            background: #f3f4f6;
            color: #6b7280;
        }
        .step-completed {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ssl-green': '#10b981',
                        'ssl-blue': '#3b82f6',
                        'security-dark': '#1e40af'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-shield-alt text-ssl-green text-2xl mr-2"></i>
                        <span class="text-xl font-bold text-gray-900">SSL Generator</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="#features" class="text-gray-700 hover:text-ssl-green transition-colors">Features</a>
                    <a href="#faq" class="text-gray-700 hover:text-ssl-green transition-colors">FAQ</a>
                    <a href="terms.html" class="text-gray-700 hover:text-ssl-green transition-colors">Terms</a>
                    <a href="privacy.html" class="text-gray-700 hover:text-ssl-green transition-colors">Privacy</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="mb-8">
                <i class="fas fa-lock text-6xl text-white mb-4 pulse-animation"></i>
            </div>
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Free SSL Generator
            </h1>
            <p class="text-xl md:text-2xl text-white mb-8 max-w-3xl mx-auto">
                Secure Your Website with a Free SSL Certificate
            </p>
            <p class="text-lg text-white/90 mb-12 max-w-2xl mx-auto">
                Generate trusted SSL certificates in minutes. Protect your visitors, boost SEO rankings, and build trust with HTTPS encryption.
            </p>
            <button onclick="scrollToGenerator()" class="ssl-green text-white px-8 py-4 rounded-lg text-lg font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                <i class="fas fa-certificate mr-2"></i>
                Generate Free SSL Now
            </button>
        </div>
    </section>

    <!-- SSL Badges Section -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-center items-center space-x-8 flex-wrap">
                <div class="flex items-center mb-4">
                    <i class="fas fa-shield-check text-ssl-green text-2xl mr-2"></i>
                    <span class="text-gray-700 font-semibold">256-bit Encryption</span>
                </div>
                <div class="flex items-center mb-4">
                    <i class="fas fa-globe text-ssl-blue text-2xl mr-2"></i>
                    <span class="text-gray-700 font-semibold">Trusted Worldwide</span>
                </div>
                <div class="flex items-center mb-4">
                    <i class="fas fa-clock text-ssl-green text-2xl mr-2"></i>
                    <span class="text-gray-700 font-semibold">90-Day Validity</span>
                </div>
                <div class="flex items-center mb-4">
                    <i class="fas fa-mobile-alt text-ssl-blue text-2xl mr-2"></i>
                    <span class="text-gray-700 font-semibold">Mobile Compatible</span>
                </div>
            </div>
        </div>
    </section>

    <!-- SSL Generator Section -->
    <section id="generator" class="py-20 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Generate Your SSL Certificate
                </h2>
                <p class="text-lg text-gray-600">
                    Follow these simple steps to secure your website
                </p>
            </div>

            <!-- Progress Steps -->
            <div class="flex justify-center mb-12">
                <div class="flex items-center space-x-4">
                    <div id="step1-indicator" class="step-active w-10 h-10 rounded-full flex items-center justify-center font-bold">1</div>
                    <div class="w-16 h-1 bg-gray-300" id="progress1"></div>
                    <div id="step2-indicator" class="step-inactive w-10 h-10 rounded-full flex items-center justify-center font-bold">2</div>
                    <div class="w-16 h-1 bg-gray-300" id="progress2"></div>
                    <div id="step3-indicator" class="step-inactive w-10 h-10 rounded-full flex items-center justify-center font-bold">3</div>
                    <div class="w-16 h-1 bg-gray-300" id="progress3"></div>
                    <div id="step4-indicator" class="step-inactive w-10 h-10 rounded-full flex items-center justify-center font-bold">4</div>
                </div>
            </div>

            <!-- SSL Generator Form -->
            <div class="bg-white rounded-lg shadow-xl p-8">
                <!-- Step 1: Domain Input -->
                <div id="step1" class="step-content">
                    <div class="text-center mb-8">
                        <i class="fas fa-globe text-4xl text-ssl-blue mb-4"></i>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Step 1: Enter Your Domain</h3>
                        <p class="text-gray-600">Enter the domain name you want to secure with SSL</p>
                    </div>
                    
                    <form id="domainForm" class="max-w-md mx-auto">
                        <div class="mb-6">
                            <label for="domain" class="block text-sm font-medium text-gray-700 mb-2">Domain Name</label>
                            <div class="relative">
                                <input type="text" id="domain" name="domain"
                                       placeholder="example.com"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ssl-green focus:border-transparent"
                                       required>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <i class="fas fa-globe text-gray-400"></i>
                                </div>
                            </div>
                            <p class="text-sm text-gray-500 mt-2">Enter domain without http:// or https://</p>
                        </div>
                        
                        <button type="submit" class="w-full ssl-green text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                            <i class="fas fa-arrow-right mr-2"></i>
                            Continue to Email Verification
                        </button>
                    </form>
                </div>

                <!-- Step 2: Email Input -->
                <div id="step2" class="step-content hidden">
                    <div class="text-center mb-8">
                        <i class="fas fa-envelope text-4xl text-ssl-blue mb-4"></i>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Step 2: Email Verification</h3>
                        <p class="text-gray-600">Enter your email address for certificate notifications</p>
                    </div>
                    
                    <form id="emailForm" class="max-w-md mx-auto">
                        <div class="mb-6">
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <div class="relative">
                                <input type="email" id="email" name="email"
                                       placeholder="<EMAIL>"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ssl-green focus:border-transparent"
                                       required>
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                </div>
                            </div>
                            <p class="text-sm text-gray-500 mt-2">We'll send your certificate to this email</p>
                        </div>
                        
                        <div class="flex space-x-4">
                            <button type="button" onclick="previousStep()" class="flex-1 bg-gray-500 text-white py-3 rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i>
                                Back
                            </button>
                            <button type="submit" class="flex-1 ssl-green text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                                <i class="fas fa-arrow-right mr-2"></i>
                                Generate SSL
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Step 3: Loading -->
                <div id="step3" class="step-content hidden">
                    <div class="text-center py-12">
                        <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-ssl-green mx-auto mb-6"></div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Generating Your SSL Certificate</h3>
                        <p class="text-gray-600 mb-4">Please wait while we create your secure certificate...</p>
                        <div class="max-w-md mx-auto">
                            <div class="bg-gray-200 rounded-full h-2">
                                <div id="progress-bar" class="bg-ssl-green h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <p id="progress-text" class="text-sm text-gray-500 mt-2">Initializing...</p>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Results -->
                <div id="step4" class="step-content hidden">
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-check text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">SSL Certificate Generated!</h3>
                        <p class="text-gray-600">Your SSL certificate is ready for download and installation</p>
                    </div>

                    <!-- Certificate Information -->
                    <div id="certificateInfo" class="bg-gray-50 rounded-lg p-6 mb-8">
                        <!-- Certificate details will be populated here -->
                    </div>

                    <!-- Download Buttons -->
                    <div class="space-y-6">
                        <!-- SSL Certificate (CRT) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-certificate text-ssl-green mr-1"></i>
                                SSL Certificate (CRT)
                                <span id="certStatus" class="ml-2 text-sm text-green-600"><i class="fas fa-check-circle mr-1"></i>Valid</span>
                            </label>
                            <textarea id="sslCertificate" readonly 
                                      class="w-full h-32 px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 font-mono text-sm"
                                      placeholder="Your SSL certificate will appear here..."></textarea>
                            <div class="flex space-x-2 mt-2">
                                <button onclick="copyCertificate()" class="bg-ssl-blue text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-copy mr-2"></i>Copy Certificate
                                </button>
                                <button onclick="validateCertificate()" class="bg-ssl-green text-white px-4 py-2 rounded hover:bg-green-600 transition-colors">
                                    <i class="fas fa-check-circle mr-2"></i>Validate
                                </button>
                            </div>
                        </div>

                        <!-- Private Key (KEY) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-key text-ssl-green mr-1"></i>
                                Private Key (KEY)
                                <span id="keyStatus" class="ml-2 text-sm text-green-600"><i class="fas fa-check-circle mr-1"></i>Valid</span>
                            </label>
                            <textarea id="privateKey" readonly 
                                      class="w-full h-32 px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 font-mono text-sm"
                                      placeholder="Your private key will appear here..."></textarea>
                            <div class="flex space-x-2 mt-2">
                                <button onclick="copyPrivateKey()" class="bg-ssl-blue text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-copy mr-2"></i>Copy Private Key
                                </button>
                                <button onclick="validatePrivateKey()" class="bg-ssl-green text-white px-4 py-2 rounded hover:bg-green-600 transition-colors">
                                    <i class="fas fa-check-circle mr-2"></i>Validate
                                </button>
                            </div>
                        </div>

                        <!-- CA Bundle (CABUNDLE) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-shield-alt text-ssl-green mr-1"></i>
                                Certificate Authority Bundle (CABUNDLE)
                                <span id="caBundleStatus" class="ml-2 text-sm text-green-600"><i class="fas fa-check-circle mr-1"></i>Valid</span>
                            </label>
                            <textarea id="caBundle" readonly 
                                      class="w-full h-32 px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 font-mono text-sm"
                                      placeholder="Your CA bundle will appear here..."></textarea>
                            <div class="flex space-x-2 mt-2">
                                <button onclick="copyCABundle()" class="bg-ssl-blue text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-copy mr-2"></i>Copy CA Bundle
                                </button>
                                <button onclick="validateCABundle()" class="bg-ssl-green text-white px-4 py-2 rounded hover:bg-green-600 transition-colors">
                                    <i class="fas fa-check-circle mr-2"></i>Validate
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="js/ssl-generator.js"></script>
</body>
</html>
