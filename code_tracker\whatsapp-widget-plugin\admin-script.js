/**
 * WhatsApp Widget Pro - Admin JavaScript
 * Version: 2.0.0
 */

jQuery(document).ready(function($) {
    
    // Tab switching functionality
    $('.whatsapp-admin-tab').on('click', function() {
        const tabId = $(this).data('tab');
        
        // Update active tab
        $('.whatsapp-admin-tab').removeClass('active');
        $(this).addClass('active');
        
        // Update active content
        $('.whatsapp-tab-content').removeClass('active');
        $('#tab-' + tabId).addClass('active');
        
        // Update preview if preview tab is selected
        if (tabId === 'preview') {
            updatePreview();
        }
    });
    
    // Real-time preview updates
    $('#whatsapp-admin-form input, #whatsapp-admin-form select').on('change', function() {
        if ($('#tab-preview').hasClass('active')) {
            updatePreview();
        }
    });
    
    // Agent management
    let agentIndex = $('.whatsapp-agent-item').length;
    
    window.addAgent = function() {
        const agentHtml = `
            <div class="whatsapp-agent-item">
                <div class="whatsapp-agent-header">
                    <div class="whatsapp-agent-toggle">
                        <span class="whatsapp-toggle">
                            <input type="checkbox" name="agents[${agentIndex}][enabled]" checked>
                            <span class="whatsapp-toggle-slider"></span>
                        </span>
                        <strong>Agent ${agentIndex + 1}</strong>
                    </div>
                    <button type="button" class="whatsapp-agent-remove" onclick="removeAgent(this)">Remove</button>
                </div>
                
                <div class="whatsapp-form-grid">
                    <div class="whatsapp-form-group">
                        <label>Agent Name</label>
                        <input type="text" name="agents[${agentIndex}][name]" placeholder="John Doe" required>
                    </div>
                    
                    <div class="whatsapp-form-group">
                        <label>WhatsApp Number</label>
                        <input type="text" name="agents[${agentIndex}][phone]" placeholder="+923001234567" required>
                    </div>
                    
                    <div class="whatsapp-form-group">
                        <label>Job Title</label>
                        <input type="text" name="agents[${agentIndex}][title]" placeholder="Customer Support">
                    </div>
                    
                    <div class="whatsapp-form-group">
                        <label>Avatar URL (Optional)</label>
                        <input type="url" name="agents[${agentIndex}][avatar]" placeholder="https://example.com/avatar.jpg">
                    </div>
                </div>
            </div>
        `;
        
        $('#whatsapp-agents-list').append(agentHtml);
        agentIndex++;
    };
    
    window.removeAgent = function(button) {
        if ($('.whatsapp-agent-item').length > 1) {
            $(button).closest('.whatsapp-agent-item').remove();
        } else {
            alert('At least one agent is required.');
        }
    };
    
    // Preview functionality
    function updatePreview() {
        const position = $('select[name="whatsapp_position"]').val();
        const size = $('select[name="whatsapp_size"]').val();
        const theme = $('select[name="whatsapp_theme"]').val();
        const bubbleEnabled = $('input[name="whatsapp_bubble_enabled"]').is(':checked');
        
        const previewHtml = `
            <div class="whatsapp-widget whatsapp-theme-${theme} ${position} widget-loaded" 
                 style="width: ${size}px; height: ${size}px;">
                <a href="#" onclick="return false;">
                    <i class="fab fa-whatsapp"></i>
                    <span class="online-indicator"></span>
                </a>
            </div>
        `;
        
        $('#whatsapp-preview-container').html(previewHtml);
    }
    
    // Test widget functionality
    window.testWidget = function() {
        const phone = $('input[name="whatsapp_phone"]').val();
        const message = $('textarea[name="whatsapp_message"]').val();
        
        if (phone) {
            const whatsappUrl = `https://wa.me/${phone}?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        } else {
            alert('Please enter a phone number first.');
        }
    };
    
    // Export settings functionality
    window.exportSettings = function() {
        const formData = new FormData($('#whatsapp-admin-form')[0]);
        const settings = {};
        
        for (let [key, value] of formData.entries()) {
            settings[key] = value;
        }
        
        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'whatsapp-widget-settings.json';
        link.click();
    };
    
    // Form validation
    $('#whatsapp-admin-form').on('submit', function(e) {
        let isValid = true;
        const requiredFields = $(this).find('[required]');
        
        requiredFields.each(function() {
            if (!$(this).val().trim()) {
                $(this).css('border-color', '#dc3545');
                isValid = false;
            } else {
                $(this).css('border-color', '#e9ecef');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }
        
        // Show loading state
        const submitButton = $(this).find('.whatsapp-save-button');
        const originalText = submitButton.html();
        submitButton.html('<i class="fas fa-spinner fa-spin"></i> Saving...').prop('disabled', true);
        
        // Re-enable button after form submission
        setTimeout(function() {
            submitButton.html(originalText).prop('disabled', false);
        }, 2000);
    });
    
    // Initialize preview on page load
    updatePreview();
    
    // Auto-save functionality (optional)
    let autoSaveTimeout;
    $('#whatsapp-admin-form input, #whatsapp-admin-form select, #whatsapp-admin-form textarea').on('input change', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(function() {
            // Could implement auto-save here
            console.log('Auto-save triggered');
        }, 5000);
    });
    
    // Working hours quick actions
    $('.whatsapp-working-hours').on('click', '.quick-action', function() {
        const action = $(this).data('action');
        const checkboxes = $('.whatsapp-working-hours input[type="checkbox"]');
        
        if (action === 'enable-all') {
            checkboxes.prop('checked', true);
        } else if (action === 'disable-all') {
            checkboxes.prop('checked', false);
        } else if (action === 'weekdays-only') {
            checkboxes.each(function(index) {
                $(this).prop('checked', index < 5); // Monday to Friday
            });
        }
    });
    
    // Add quick action buttons to working hours section
    if ($('.whatsapp-working-hours').length) {
        const quickActions = `
            <div style="margin-bottom: 16px;">
                <button type="button" class="button quick-action" data-action="enable-all">Enable All</button>
                <button type="button" class="button quick-action" data-action="disable-all">Disable All</button>
                <button type="button" class="button quick-action" data-action="weekdays-only">Weekdays Only</button>
            </div>
        `;
        $('.whatsapp-working-hours').before(quickActions);
    }
    
    // Theme preview
    $('select[name="whatsapp_theme"]').on('change', function() {
        updatePreview();
    });
    
    // Success message auto-hide
    setTimeout(function() {
        $('.whatsapp-success-message').fadeOut();
    }, 5000);
    
});
