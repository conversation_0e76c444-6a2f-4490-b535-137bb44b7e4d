# OLX Marketplace - Complete Website Solution

A complete marketplace website similar to OLX for buying and selling products, built with HTML, CSS, JavaScript, PHP backend, MySQL database, and WordPress integration.

## 🎉 **COMPLETE BACKEND SYSTEM INCLUDED!**

This is a **FULL-STACK** marketplace solution with:
- ✅ **Complete PHP Backend** with MySQL database
- ✅ **User Authentication System** (Register, Login, Password Reset)
- ✅ **Product Management** (Add, Edit, Delete, Image Upload)
- ✅ **Advanced Search & Filters** with database integration
- ✅ **Admin Dashboard** for complete site management
- ✅ **Real-time Data** from database
- ✅ **Responsive Design** for all devices
- ✅ **WordPress Integration** ready

## 🚀 Features

### Frontend Features
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Modern UI/UX**: Clean, professional design similar to OLX
- **Product Categories**: Organized browsing by categories
- **Advanced Search**: Search by keywords, location, and category
- **Product Listings**: Grid layout with product cards
- **User Authentication**: Login and registration modals
- **Interactive Elements**: Hover effects, animations, and smooth scrolling

### WordPress Integration
- **Custom Post Type**: Products with custom fields
- **Custom Taxonomy**: Product categories
- **User Management**: WordPress user system integration
- **Admin Panel**: Easy product management through WordPress admin
- **SEO Friendly**: Proper meta tags and structured data
- **Widget Support**: Customizable sidebar and footer widgets

### Product Management
- **Product Details**: Price, location, condition, contact information
- **Image Gallery**: Multiple product images support
- **Contact System**: Phone and email contact options
- **Safety Tips**: Built-in safety guidelines for users
- **Related Products**: Automatic related product suggestions

## 📁 Project Structure

```
olx-marketplace/
├── index.html                 # Main homepage (standalone version)
├── css/
│   └── style.css             # Main stylesheet
├── js/
│   └── main.js               # JavaScript functionality
├── wordpress-theme/          # WordPress theme files
│   ├── style.css            # WordPress theme stylesheet
│   ├── functions.php        # Theme functions and custom post types
│   ├── index.php            # Main template file
│   ├── header.php           # Header template
│   ├── footer.php           # Footer template
│   └── single-product.php   # Single product template
└── README.md                # This file
```

## 🛠️ Installation

### Quick Setup (Recommended)

1. **Download and Extract**
   - Extract all files to your web server directory (e.g., `htdocs`, `www`, `public_html`)

2. **Run Setup Script**
   - Open your browser and go to: `http://localhost/olx-marketplace/setup.php`
   - The setup script will automatically:
     - Create the MySQL database
     - Set up all tables with sample data
     - Create upload directories
     - Create default admin user

3. **Start Using**
   - **Homepage**: `http://localhost/olx-marketplace/index.html`
   - **Admin Panel**: `http://localhost/olx-marketplace/admin/index.html`
   - **Admin Login**: <EMAIL> / admin123

### Manual Installation

#### Option 1: Full Backend Version (Recommended)

1. **Requirements**
   - PHP 7.4 or higher
   - MySQL 5.6 or higher
   - Web server (Apache/Nginx)
   - GD extension for image processing

2. **Database Setup**
   - Create MySQL database named `olx_marketplace`
   - Import `backend/database/schema.sql`
   - Or run `setup.php` for automatic setup

3. **Configuration**
   - Update database credentials in `backend/config/database.php`
   - Set up upload directories with write permissions

#### Option 2: Standalone HTML Version

1. **Simple Setup**
   - Open `index.html` in your web browser
   - No backend functionality, but design works perfectly

### Option 2: WordPress Integration

1. **Prerequisites**
   - WordPress 5.0 or higher
   - PHP 7.4 or higher
   - MySQL 5.6 or higher

2. **Install WordPress Theme**
   ```bash
   # Copy the wordpress-theme folder to your WordPress themes directory
   cp -r wordpress-theme /path/to/wordpress/wp-content/themes/olx-marketplace
   ```

3. **Activate the Theme**
   - Go to WordPress Admin → Appearance → Themes
   - Find "OLX Marketplace" and click "Activate"

4. **Configure the Theme**
   - The theme will automatically create the "Products" post type
   - Go to Products → Add New to start adding products
   - Configure categories under Products → Categories

## ⚙️ Configuration

### WordPress Setup

1. **Create Essential Pages**
   ```
   - Home (set as front page)
   - Privacy Policy
   - Terms of Service
   - Contact Us
   - Help/FAQ
   ```

2. **Configure Menus**
   - Go to Appearance → Menus
   - Create Primary Menu and Footer Menu
   - Assign to appropriate locations

3. **Set Up Widgets**
   - Go to Appearance → Widgets
   - Add widgets to Sidebar and Footer Widget Area

4. **Configure Permalinks**
   - Go to Settings → Permalinks
   - Choose "Post name" structure for SEO-friendly URLs

### Customization Options

1. **Logo and Branding**
   - Go to Appearance → Customize → Site Identity
   - Upload your logo and set site title/tagline

2. **Colors and Styling**
   - Modify `css/style.css` for color scheme changes
   - Main brand colors: #002f34 (dark), #23e5db (accent), #ffce32 (yellow)

3. **Social Media Links**
   - Edit `footer.php` to add your social media URLs
   - Or use WordPress Customizer to add social media options

## 🎯 Usage

### For Users (Frontend)

1. **Browsing Products**
   - Use the search bar to find specific items
   - Filter by location and category
   - Browse by categories on the homepage

2. **User Registration**
   - Click "Login" in the header
   - Switch to "Register" to create an account
   - Fill in required information

3. **Posting Products** (WordPress version)
   - Login to your account
   - Click "SELL" button in header
   - Fill in product details and upload images

### For Administrators (WordPress)

1. **Managing Products**
   - Go to Products in WordPress admin
   - Add, edit, or delete product listings
   - Manage product categories

2. **User Management**
   - Go to Users in WordPress admin
   - Manage user accounts and permissions

3. **Site Configuration**
   - Use WordPress Customizer for theme options
   - Configure widgets and menus
   - Set up essential pages

## 🔧 Customization

### Adding New Categories

1. **WordPress Admin**
   ```php
   // Go to Products → Categories
   // Add new categories with icons
   ```

2. **Update Category Icons**
   ```php
   // Edit functions.php
   $category_icons = array(
       'your-category-slug' => 'fas fa-your-icon',
   );
   ```

### Styling Modifications

1. **Colors**
   ```css
   /* Edit css/style.css */
   :root {
       --primary-color: #002f34;
       --accent-color: #23e5db;
       --warning-color: #ffce32;
   }
   ```

2. **Layout Changes**
   ```css
   /* Modify grid layouts */
   .products-grid {
       grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
   }
   ```

### Adding New Features

1. **Custom Fields**
   ```php
   // Add to functions.php
   add_action('add_meta_boxes', 'your_custom_meta_box');
   ```

2. **New Post Types**
   ```php
   // Register additional post types in functions.php
   register_post_type('your_post_type', $args);
   ```

## 📱 Mobile Responsiveness

The theme is fully responsive and includes:
- Mobile-first design approach
- Touch-friendly interface
- Optimized images and loading
- Responsive navigation menu
- Mobile-optimized forms

## 🔒 Security Features

- WordPress nonce verification
- Data sanitization and validation
- SQL injection prevention
- XSS protection
- Safe file uploads

## 🚀 Performance Optimization

- Optimized CSS and JavaScript
- Image lazy loading (can be added)
- Minified assets (for production)
- Efficient database queries
- Caching-friendly structure

## 📞 Support

For support and customization:
- Check WordPress documentation
- Review the code comments
- Test in a staging environment first
- Keep backups before making changes

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📋 Changelog

### Version 1.0.0
- Initial release
- Complete marketplace functionality
- WordPress integration
- Responsive design
- User authentication
- Product management system

---

**Note**: This is a complete marketplace solution. For production use, consider adding:
- Payment gateway integration
- Advanced user profiles
- Messaging system between buyers/sellers
- Email notifications
- Advanced search filters
- Admin analytics dashboard
