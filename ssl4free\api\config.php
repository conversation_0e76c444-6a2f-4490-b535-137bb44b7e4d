<?php
// SSL4Free Configuration
define('ZEROSSL_API_KEY', '0081ad6fd2da62aee4a586bd0b4b0447'); // Get from https://app.zerossl.com/developer
define('ZEROSSL_API_URL', 'https://api.zerossl.com');

// EAB (External Account Binding) Credentials for enhanced features
define('ZEROSSL_EAB_KID', 'RCCUd-enS9kckc60lVnoOQ');
define('ZEROSSL_EAB_HMAC_KEY', 'YHTFEbup8QyCpnr8vKFBnK1bVFGg0IRRw-VUcXMbvPhOj1_AWlm55zmlZmJmRcERmrboKw7aJfu8KaiSlK7ONw');

// Email configuration for sending certificates
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'SSL4Free');

// Application settings
define('APP_NAME', 'SSL4Free');
define('APP_URL', 'https://yourdomain.com');
define('TEMP_DIR', __DIR__ . '/../temp/');
define('LOG_FILE', __DIR__ . '/../logs/ssl4free.log');

// Create necessary directories
if (!file_exists(TEMP_DIR)) {
    mkdir(TEMP_DIR, 0755, true);
}

if (!file_exists(dirname(LOG_FILE))) {
    mkdir(dirname(LOG_FILE), 0755, true);
}

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', LOG_FILE);

// Set headers for API responses
function setApiHeaders() {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    
    // Handle preflight requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }
}

// Log function
function logMessage($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

// API response function
function apiResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => date('c')
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    return json_encode($response, JSON_PRETTY_PRINT);
}

// ZeroSSL API request function (Fixed for correct authentication)
function zeroSSLRequest($endpoint, $method = 'GET', $data = null, $useEAB = false) {
    // Add access_key parameter to URL (ZeroSSL uses access_key, not Bearer token)
    $separator = strpos($endpoint, '?') !== false ? '&' : '?';
    $url = ZEROSSL_API_URL . $endpoint . $separator . 'access_key=' . ZEROSSL_API_KEY;

    $headers = [
        'Content-Type: application/json'
    ];

    // Add EAB headers if requested
    if ($useEAB && defined('ZEROSSL_EAB_KID') && defined('ZEROSSL_EAB_HMAC_KEY')) {
        $headers[] = 'EAB-KID: ' . ZEROSSL_EAB_KID;
        $headers[] = 'EAB-HMAC-KEY: ' . ZEROSSL_EAB_HMAC_KEY;
        logMessage("Using EAB credentials for enhanced API access", 'INFO');
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);

    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            // For POST requests, send data as JSON in body
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    } elseif ($method === 'PUT') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    } elseif ($method === 'DELETE') {
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        logMessage("cURL Error: $error", 'ERROR');
        return false;
    }

    $decodedResponse = json_decode($response, true);

    if ($httpCode >= 400) {
        logMessage("ZeroSSL API Error: HTTP $httpCode - " . $response, 'ERROR');
        return false;
    }

    logMessage("ZeroSSL API Success: HTTP $httpCode", 'INFO');
    return $decodedResponse;
}

// Validate domain function
function validateDomain($domain) {
    // Remove protocol if present
    $domain = preg_replace('/^https?:\/\//', '', $domain);
    $domain = rtrim($domain, '/');

    // Basic domain validation - more flexible regex
    if (!preg_match('/^[a-zA-Z0-9][a-zA-Z0-9.-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/', $domain)) {
        logMessage("Domain format validation failed for: $domain", 'WARNING');
        return false;
    }

    // Skip DNS resolution check for now - it's causing issues
    // Many domains might not resolve from local environment
    logMessage("Domain format validation passed for: $domain", 'INFO');
    return true;

    // Optional: Try DNS resolution but don't fail if it doesn't work
    /*
    try {
        $ip = @gethostbyname($domain);
        if ($ip === $domain) {
            logMessage("Domain DNS resolution failed for: $domain (but allowing anyway)", 'WARNING');
        } else {
            logMessage("Domain DNS resolution successful for: $domain -> $ip", 'INFO');
        }
    } catch (Exception $e) {
        logMessage("DNS check error for $domain: " . $e->getMessage(), 'WARNING');
    }
    */
}

// Validate email function
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Generate random string
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Save certificate data temporarily
function saveCertificateData($domain, $data) {
    $filename = TEMP_DIR . 'cert_' . md5($domain) . '.json';
    file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT));
    return $filename;
}

// Load certificate data
function loadCertificateData($domain) {
    $filename = TEMP_DIR . 'cert_' . md5($domain) . '.json';
    if (file_exists($filename)) {
        return json_decode(file_get_contents($filename), true);
    }
    return null;
}

// Clean up old temporary files (older than 24 hours)
function cleanupTempFiles() {
    $files = glob(TEMP_DIR . '*');
    $now = time();
    
    foreach ($files as $file) {
        if (is_file($file) && ($now - filemtime($file)) > 86400) { // 24 hours
            unlink($file);
        }
    }
}

// Send email function using PHPMailer (you'll need to install PHPMailer)
function sendEmail($to, $subject, $body, $attachments = []) {
    // For now, we'll use PHP's mail() function
    // In production, use PHPMailer for better reliability
    
    $headers = [
        'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
        'Reply-To: ' . FROM_EMAIL,
        'Content-Type: text/html; charset=UTF-8',
        'MIME-Version: 1.0'
    ];
    
    $success = mail($to, $subject, $body, implode("\r\n", $headers));
    
    if ($success) {
        logMessage("Email sent successfully to: $to", 'INFO');
    } else {
        logMessage("Failed to send email to: $to", 'ERROR');
    }
    
    return $success;
}

// Check ZeroSSL API key validity
function checkZeroSSLApiKey() {
    if (ZEROSSL_API_KEY === 'YOUR_ZEROSSL_API_KEY_HERE') {
        return false;
    }

    // Test API key by making a simple request
    $response = zeroSSLRequest('/certificates?limit=1');
    return $response !== false;
}

// Initialize cleanup on each request (randomly)
if (rand(1, 100) === 1) {
    cleanupTempFiles();
}

// Check if ZeroSSL API key is configured
if (!checkZeroSSLApiKey() && basename($_SERVER['PHP_SELF']) !== 'setup.php') {
    logMessage('ZeroSSL API key not configured or invalid', 'WARNING');
}
?>
