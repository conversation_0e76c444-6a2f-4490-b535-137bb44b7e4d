/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    gap: 20px;
}

.logo h1 {
    color: #002f34;
    font-size: 24px;
    font-weight: bold;
}

.logo i {
    color: #23e5db;
    margin-right: 8px;
}

/* Search Container */
.search-container {
    flex: 1;
    max-width: 600px;
}

.search-box {
    display: flex;
    background: #fff;
    border: 2px solid #002f34;
    border-radius: 4px;
    overflow: hidden;
}

.search-box input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    outline: none;
    font-size: 16px;
}

.search-box select {
    padding: 12px 15px;
    border: none;
    border-left: 1px solid #ddd;
    outline: none;
    background: #f8f9fa;
    min-width: 150px;
}

.search-btn {
    background: #002f34;
    color: white;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

.search-btn:hover {
    background: #001a1d;
}

/* Navigation Menu */
.nav-menu {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-link {
    text-decoration: none;
    color: #002f34;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background 0.3s;
}

.nav-link:hover {
    background: #f8f9fa;
}

.sell-btn {
    background: #ffce32;
    color: #002f34;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 4px;
    font-weight: bold;
    transition: background 0.3s;
}

.sell-btn:hover {
    background: #ffc107;
}

/* Categories Section */
.categories {
    padding: 40px 0;
    background: #fff;
}

.categories h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #002f34;
    font-size: 28px;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.category-card {
    text-align: center;
    padding: 25px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s;
    cursor: pointer;
    border: 2px solid transparent;
}

.category-card:hover {
    background: #fff;
    border-color: #23e5db;
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.category-card i {
    font-size: 32px;
    color: #002f34;
    margin-bottom: 10px;
}

.category-card h3 {
    color: #002f34;
    margin-bottom: 5px;
    font-size: 16px;
}

.category-card p {
    color: #666;
    font-size: 14px;
}

/* Featured Products */
.featured-products {
    padding: 40px 0;
}

.featured-products h2 {
    margin-bottom: 30px;
    color: #002f34;
    font-size: 28px;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.product-card {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    width: 100%;
    height: 200px;
    background: #f0f0f0;
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-info {
    padding: 15px;
}

.product-price {
    font-size: 20px;
    font-weight: bold;
    color: #002f34;
    margin-bottom: 8px;
}

.product-title {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
}

.product-location {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
}

.product-date {
    color: #999;
    font-size: 12px;
}

.load-more {
    text-align: center;
}

.load-more-btn {
    background: #002f34;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

.load-more-btn:hover {
    background: #001a1d;
}

/* Footer */
.footer {
    background: #002f34;
    color: #fff;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 15px;
    color: #23e5db;
}

.footer-section p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: #23e5db;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    color: #ccc;
    font-size: 20px;
    transition: color 0.3s;
}

.social-links a:hover {
    color: #23e5db;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #444;
    color: #ccc;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    position: relative;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

.modal h2 {
    margin-bottom: 20px;
    color: #002f34;
    text-align: center;
}

.form-group {
    margin-bottom: 15px;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.btn-primary {
    width: 100%;
    background: #002f34;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 15px;
}

.btn-primary:hover {
    background: #001a1d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-wrapper {
        flex-direction: column;
        gap: 15px;
    }
    
    .search-box {
        flex-direction: column;
    }
    
    .search-box select {
        border-left: none;
        border-top: 1px solid #ddd;
    }
    
    .nav-menu {
        width: 100%;
        justify-content: center;
    }
    
    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }
}
