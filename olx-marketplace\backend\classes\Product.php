<?php
/**
 * Product Class - Handle all product operations
 * OLX Marketplace Backend
 */

require_once '../config/database.php';

class Product {
    private $conn;
    private $table_name = "products";

    public $id;
    public $user_id;
    public $category_id;
    public $title;
    public $slug;
    public $description;
    public $price;
    public $condition_type;
    public $location;
    public $contact_phone;
    public $contact_email;
    public $is_featured;
    public $is_active;
    public $is_sold;
    public $views_count;
    public $favorites_count;
    public $created_at;
    public $updated_at;
    public $expires_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create new product
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET user_id=:user_id, category_id=:category_id, title=:title, 
                      slug=:slug, description=:description, price=:price, 
                      condition_type=:condition_type, location=:location,
                      contact_phone=:contact_phone, contact_email=:contact_email,
                      expires_at=:expires_at";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->slug = $this->generateSlug($this->title);
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->location = htmlspecialchars(strip_tags($this->location));
        $this->contact_phone = htmlspecialchars(strip_tags($this->contact_phone));
        $this->contact_email = htmlspecialchars(strip_tags($this->contact_email));

        // Set expiry date (30 days from now)
        $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));

        // Bind values
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":category_id", $this->category_id);
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":slug", $this->slug);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":price", $this->price);
        $stmt->bindParam(":condition_type", $this->condition_type);
        $stmt->bindParam(":location", $this->location);
        $stmt->bindParam(":contact_phone", $this->contact_phone);
        $stmt->bindParam(":contact_email", $this->contact_email);
        $stmt->bindParam(":expires_at", $expires_at);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Read all products with pagination and filters
    public function read($page = 1, $limit = 12, $filters = []) {
        $offset = ($page - 1) * $limit;
        
        $query = "SELECT p.*, c.name as category_name, c.icon as category_icon,
                         u.username, u.full_name as seller_name,
                         (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM " . $this->table_name . " p
                  LEFT JOIN categories c ON p.category_id = c.id
                  LEFT JOIN users u ON p.user_id = u.id
                  WHERE p.is_active = 1 AND p.expires_at > NOW()";

        $params = [];

        // Apply filters
        if(!empty($filters['search'])) {
            $query .= " AND (MATCH(p.title, p.description) AGAINST (:search IN NATURAL LANGUAGE MODE) 
                        OR p.title LIKE :search_like OR p.location LIKE :search_like)";
            $params[':search'] = $filters['search'];
            $params[':search_like'] = '%' . $filters['search'] . '%';
        }

        if(!empty($filters['category_id'])) {
            $query .= " AND p.category_id = :category_id";
            $params[':category_id'] = $filters['category_id'];
        }

        if(!empty($filters['location'])) {
            $query .= " AND p.location LIKE :location";
            $params[':location'] = '%' . $filters['location'] . '%';
        }

        if(!empty($filters['min_price'])) {
            $query .= " AND p.price >= :min_price";
            $params[':min_price'] = $filters['min_price'];
        }

        if(!empty($filters['max_price'])) {
            $query .= " AND p.price <= :max_price";
            $params[':max_price'] = $filters['max_price'];
        }

        if(!empty($filters['condition'])) {
            $query .= " AND p.condition_type = :condition";
            $params[':condition'] = $filters['condition'];
        }

        if(!empty($filters['user_id'])) {
            $query .= " AND p.user_id = :user_id";
            $params[':user_id'] = $filters['user_id'];
        }

        // Sorting
        $sort_by = isset($filters['sort_by']) ? $filters['sort_by'] : 'created_at';
        $sort_order = isset($filters['sort_order']) ? $filters['sort_order'] : 'DESC';
        
        $allowed_sort_fields = ['created_at', 'price', 'title', 'views_count', 'favorites_count'];
        if(!in_array($sort_by, $allowed_sort_fields)) {
            $sort_by = 'created_at';
        }
        
        $query .= " ORDER BY p." . $sort_by . " " . $sort_order;
        $query .= " LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);

        // Bind filter parameters
        foreach($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->bindValue(":limit", $limit, PDO::PARAM_INT);
        $stmt->bindValue(":offset", $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get total products count with filters
    public function getTotalCount($filters = []) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " p
                  WHERE p.is_active = 1 AND p.expires_at > NOW()";

        $params = [];

        // Apply same filters as read method
        if(!empty($filters['search'])) {
            $query .= " AND (MATCH(p.title, p.description) AGAINST (:search IN NATURAL LANGUAGE MODE) 
                        OR p.title LIKE :search_like OR p.location LIKE :search_like)";
            $params[':search'] = $filters['search'];
            $params[':search_like'] = '%' . $filters['search'] . '%';
        }

        if(!empty($filters['category_id'])) {
            $query .= " AND p.category_id = :category_id";
            $params[':category_id'] = $filters['category_id'];
        }

        if(!empty($filters['location'])) {
            $query .= " AND p.location LIKE :location";
            $params[':location'] = '%' . $filters['location'] . '%';
        }

        if(!empty($filters['min_price'])) {
            $query .= " AND p.price >= :min_price";
            $params[':min_price'] = $filters['min_price'];
        }

        if(!empty($filters['max_price'])) {
            $query .= " AND p.price <= :max_price";
            $params[':max_price'] = $filters['max_price'];
        }

        if(!empty($filters['condition'])) {
            $query .= " AND p.condition_type = :condition";
            $params[':condition'] = $filters['condition'];
        }

        if(!empty($filters['user_id'])) {
            $query .= " AND p.user_id = :user_id";
            $params[':user_id'] = $filters['user_id'];
        }

        $stmt = $this->conn->prepare($query);

        foreach($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'];
    }

    // Get single product by ID or slug
    public function readOne($identifier, $by_slug = false) {
        $field = $by_slug ? 'slug' : 'id';
        
        $query = "SELECT p.*, c.name as category_name, c.icon as category_icon,
                         u.username, u.full_name as seller_name, u.phone as seller_phone,
                         u.avatar as seller_avatar, u.location as seller_location
                  FROM " . $this->table_name . " p
                  LEFT JOIN categories c ON p.category_id = c.id
                  LEFT JOIN users u ON p.user_id = u.id
                  WHERE p." . $field . " = :identifier AND p.is_active = 1 LIMIT 1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":identifier", $identifier);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            // Get product images
            $images_query = "SELECT * FROM product_images WHERE product_id = :product_id ORDER BY sort_order, is_primary DESC";
            $images_stmt = $this->conn->prepare($images_query);
            $images_stmt->bindParam(":product_id", $row['id']);
            $images_stmt->execute();
            $row['images'] = $images_stmt->fetchAll(PDO::FETCH_ASSOC);

            // Increment view count
            $this->incrementViews($row['id']);
        }

        return $row;
    }

    // Update product
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET title=:title, slug=:slug, description=:description, 
                      price=:price, condition_type=:condition_type, 
                      location=:location, contact_phone=:contact_phone, 
                      contact_email=:contact_email, category_id=:category_id
                  WHERE id=:id AND user_id=:user_id";

        $stmt = $this->conn->prepare($query);

        // Sanitize inputs
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->slug = $this->generateSlug($this->title);
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->location = htmlspecialchars(strip_tags($this->location));
        $this->contact_phone = htmlspecialchars(strip_tags($this->contact_phone));
        $this->contact_email = htmlspecialchars(strip_tags($this->contact_email));

        // Bind values
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":slug", $this->slug);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":price", $this->price);
        $stmt->bindParam(":condition_type", $this->condition_type);
        $stmt->bindParam(":location", $this->location);
        $stmt->bindParam(":contact_phone", $this->contact_phone);
        $stmt->bindParam(":contact_email", $this->contact_email);
        $stmt->bindParam(":category_id", $this->category_id);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":user_id", $this->user_id);

        return $stmt->execute();
    }

    // Delete product
    public function delete() {
        $query = "UPDATE " . $this->table_name . " 
                  SET is_active = 0 
                  WHERE id = :id AND user_id = :user_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":user_id", $this->user_id);

        return $stmt->execute();
    }

    // Mark as sold
    public function markAsSold() {
        $query = "UPDATE " . $this->table_name . " 
                  SET is_sold = 1 
                  WHERE id = :id AND user_id = :user_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":user_id", $this->user_id);

        return $stmt->execute();
    }

    // Increment view count
    private function incrementViews($product_id) {
        $query = "UPDATE " . $this->table_name . " 
                  SET views_count = views_count + 1 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $product_id);
        $stmt->execute();
    }

    // Generate unique slug
    private function generateSlug($title) {
        $slug = strtolower($title);
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');

        // Check if slug exists and make it unique
        $original_slug = $slug;
        $counter = 1;

        while($this->slugExists($slug)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    // Check if slug exists
    private function slugExists($slug) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE slug = :slug";
        if(isset($this->id)) {
            $query .= " AND id != :id";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":slug", $slug);
        
        if(isset($this->id)) {
            $stmt->bindParam(":id", $this->id);
        }

        $stmt->execute();
        return $stmt->rowCount() > 0;
    }

    // Get related products
    public function getRelatedProducts($limit = 4) {
        $query = "SELECT p.*, c.name as category_name,
                         (SELECT image_path FROM product_images WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM " . $this->table_name . " p
                  LEFT JOIN categories c ON p.category_id = c.id
                  WHERE p.category_id = :category_id AND p.id != :id 
                        AND p.is_active = 1 AND p.expires_at > NOW()
                  ORDER BY RAND()
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":category_id", $this->category_id);
        $stmt->bindParam(":id", $this->id);
        $stmt->bindValue(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Validate product data
    public static function validateProduct($data) {
        $errors = [];

        if(empty($data['title']) || strlen($data['title']) < 3) {
            $errors[] = "Title must be at least 3 characters long";
        }

        if(empty($data['description']) || strlen($data['description']) < 10) {
            $errors[] = "Description must be at least 10 characters long";
        }

        if(empty($data['price']) || !is_numeric($data['price']) || $data['price'] <= 0) {
            $errors[] = "Valid price is required";
        }

        if(empty($data['category_id']) || !is_numeric($data['category_id'])) {
            $errors[] = "Category is required";
        }

        if(empty($data['location'])) {
            $errors[] = "Location is required";
        }

        if(!empty($data['contact_phone']) && !preg_match('/^(\+92|0)?[0-9]{10}$/', $data['contact_phone'])) {
            $errors[] = "Invalid phone number format";
        }

        if(!empty($data['contact_email']) && !filter_var($data['contact_email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Invalid email format";
        }

        $allowed_conditions = ['new', 'used', 'refurbished'];
        if(!empty($data['condition_type']) && !in_array($data['condition_type'], $allowed_conditions)) {
            $errors[] = "Invalid condition type";
        }

        return $errors;
    }
}
?>
