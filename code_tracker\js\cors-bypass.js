/**
 * CORS Bypass Solutions for Web Scraping
 * Multiple strategies to handle cross-origin requests
 * Educational purpose only
 */

class CORSBypass {
    constructor() {
        this.proxyServices = [
            {
                name: 'AllOrigins',
                url: 'https://api.allorigins.win/get?url=',
                responseType: 'json',
                extractContent: (data) => data.contents
            },
            {
                name: 'CORS Anywhere',
                url: 'https://cors-anywhere.herokuapp.com/',
                responseType: 'text',
                extractContent: (data) => data
            },
            {
                name: 'CodeTabs Proxy',
                url: 'https://api.codetabs.com/v1/proxy?quest=',
                responseType: 'text',
                extractContent: (data) => data
            },
            {
                name: 'ThingProxy',
                url: 'https://thingproxy.freeboard.io/fetch/',
                responseType: 'text',
                extractContent: (data) => data
            },
            {
                name: 'YaCDN Proxy',
                url: 'https://yacdn.org/proxy/',
                responseType: 'text',
                extractContent: (data) => data
            }
        ];

        this.fallbackProxies = [
            'https://crossorigin.me/',
            'https://cors.bridged.cc/',
            'https://api.codetabs.com/v1/proxy/?quest='
        ];

        this.requestTimeout = 10000; // 10 seconds
        this.maxRetries = 3;
    }

    // Main function to fetch content with CORS bypass
    async fetchWithCORSBypass(url, options = {}) {
        console.log(`🌐 Attempting CORS bypass for: ${url}`);
        
        const requestOptions = {
            method: 'GET',
            timeout: this.requestTimeout,
            ...options
        };

        // Try each proxy service
        for (const proxy of this.proxyServices) {
            try {
                console.log(`🔄 Trying ${proxy.name}...`);
                const result = await this.fetchThroughProxy(url, proxy, requestOptions);
                
                if (result.success) {
                    console.log(`✅ Success with ${proxy.name}`);
                    return result;
                }
            } catch (error) {
                console.log(`❌ ${proxy.name} failed: ${error.message}`);
                continue;
            }
        }

        // Try fallback proxies
        for (const fallbackProxy of this.fallbackProxies) {
            try {
                console.log(`🔄 Trying fallback proxy: ${fallbackProxy}`);
                const result = await this.fetchThroughSimpleProxy(url, fallbackProxy, requestOptions);
                
                if (result.success) {
                    console.log(`✅ Success with fallback proxy`);
                    return result;
                }
            } catch (error) {
                console.log(`❌ Fallback proxy failed: ${error.message}`);
                continue;
            }
        }

        // Try direct request as last resort
        try {
            console.log('🔄 Trying direct request...');
            const response = await this.fetchWithTimeout(url, requestOptions);
            
            if (response.ok) {
                const content = await response.text();
                return {
                    success: true,
                    content: content,
                    source: 'Direct Request',
                    url: url
                };
            }
        } catch (error) {
            console.log(`❌ Direct request failed: ${error.message}`);
        }

        return {
            success: false,
            error: 'All CORS bypass methods failed',
            url: url
        };
    }

    // Fetch through a specific proxy service
    async fetchThroughProxy(url, proxy, options) {
        const proxyUrl = proxy.url + encodeURIComponent(url);
        
        const response = await this.fetchWithTimeout(proxyUrl, {
            ...options,
            headers: {
                'User-Agent': this.getRandomUserAgent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                ...options.headers
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        let content;
        if (proxy.responseType === 'json') {
            const jsonData = await response.json();
            content = proxy.extractContent(jsonData);
        } else {
            content = await response.text();
            content = proxy.extractContent(content);
        }

        return {
            success: true,
            content: content,
            source: proxy.name,
            url: url
        };
    }

    // Fetch through simple proxy
    async fetchThroughSimpleProxy(url, proxyUrl, options) {
        const fullProxyUrl = proxyUrl + encodeURIComponent(url);
        
        const response = await this.fetchWithTimeout(fullProxyUrl, {
            ...options,
            headers: {
                'User-Agent': this.getRandomUserAgent(),
                ...options.headers
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const content = await response.text();
        
        return {
            success: true,
            content: content,
            source: 'Fallback Proxy',
            url: url
        };
    }

    // Fetch with timeout
    async fetchWithTimeout(url, options) {
        const timeout = options.timeout || this.requestTimeout;
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    // POST request with CORS bypass
    async postWithCORSBypass(url, data, options = {}) {
        console.log(`📤 POST request with CORS bypass to: ${url}`);
        
        const postOptions = {
            method: 'POST',
            body: data,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                ...options.headers
            },
            ...options
        };

        // For POST requests, we need to use proxies that support POST
        const postSupportedProxies = this.proxyServices.filter(proxy => 
            proxy.name === 'AllOrigins' || proxy.name === 'CORS Anywhere'
        );

        for (const proxy of postSupportedProxies) {
            try {
                console.log(`🔄 POST via ${proxy.name}...`);
                
                if (proxy.name === 'AllOrigins') {
                    // AllOrigins requires special handling for POST
                    const result = await this.postThroughAllOrigins(url, data, postOptions);
                    if (result.success) return result;
                } else {
                    // Standard proxy POST
                    const result = await this.fetchThroughProxy(url, proxy, postOptions);
                    if (result.success) return result;
                }
            } catch (error) {
                console.log(`❌ POST via ${proxy.name} failed: ${error.message}`);
                continue;
            }
        }

        return {
            success: false,
            error: 'All POST CORS bypass methods failed',
            url: url
        };
    }

    // Special handling for AllOrigins POST requests
    async postThroughAllOrigins(url, data, options) {
        const allOriginsUrl = 'https://api.allorigins.win/get';
        
        const formData = new FormData();
        formData.append('url', url);
        formData.append('method', 'POST');
        formData.append('body', data);
        
        const response = await this.fetchWithTimeout(allOriginsUrl, {
            method: 'POST',
            body: formData,
            headers: {
                'User-Agent': this.getRandomUserAgent()
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const jsonData = await response.json();
        
        return {
            success: true,
            content: jsonData.contents,
            source: 'AllOrigins POST',
            url: url
        };
    }

    // Check if CORS bypass is needed
    async checkCORSSupport(url) {
        try {
            const response = await fetch(url, {
                method: 'HEAD',
                mode: 'cors'
            });
            
            return {
                corsSupported: true,
                status: response.status
            };
        } catch (error) {
            return {
                corsSupported: false,
                error: error.message
            };
        }
    }

    // Get random user agent
    getRandomUserAgent() {
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0'
        ];
        
        return userAgents[Math.floor(Math.random() * userAgents.length)];
    }

    // Utility function to create form data
    createFormData(data) {
        if (typeof data === 'string') {
            return data;
        }
        
        if (data instanceof FormData) {
            return data;
        }
        
        // Convert object to form data
        const formData = new URLSearchParams();
        for (const [key, value] of Object.entries(data)) {
            formData.append(key, value);
        }
        
        return formData.toString();
    }

    // Test all proxy services
    async testProxyServices() {
        console.log('🧪 Testing all proxy services...');
        
        const testUrl = 'https://httpbin.org/get';
        const results = [];
        
        for (const proxy of this.proxyServices) {
            try {
                const startTime = Date.now();
                const result = await this.fetchThroughProxy(testUrl, proxy, {});
                const endTime = Date.now();
                
                results.push({
                    name: proxy.name,
                    success: result.success,
                    responseTime: endTime - startTime,
                    error: null
                });
            } catch (error) {
                results.push({
                    name: proxy.name,
                    success: false,
                    responseTime: null,
                    error: error.message
                });
            }
        }
        
        console.table(results);
        return results;
    }
}

// Export the CORS bypass utility
window.CORSBypass = new CORSBypass();

console.log('🌐 CORS Bypass utility loaded - Educational use only');
