<?php
require_once 'config.php';

setApiHeaders();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo apiResponse(false, 'Method not allowed');
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo apiResponse(false, 'Invalid JSON input');
        exit();
    }
    
    // Validate required fields
    if (!isset($input['domain']) || !isset($input['email'])) {
        echo apiResponse(false, 'Missing required fields: domain and email');
        exit();
    }
    
    $domain = trim($input['domain']);
    $email = trim($input['email']);
    $manualCSR = $input['manual_csr'] ?? null;

    // Validate inputs
    if (!validateDomain($domain) || !validateEmail($email)) {
        echo apiResponse(false, 'Invalid domain or email');
        exit();
    }
    
    if (!in_array($verificationMethod, ['http', 'dns'])) {
        echo apiResponse(false, 'Invalid verification method');
        exit();
    }
    
    // Check if domain was previously validated
    $tempData = loadCertificateData($domain);
    if (!$tempData || $tempData['validation_step'] !== 'domain_validated') {
        echo apiResponse(false, 'Domain not validated. Please validate domain first.');
        exit();
    }
    
    logMessage("Creating certificate for domain: $domain using $verificationMethod verification", 'INFO');
    
    // Create certificate request with ZeroSSL
    $certificateData = createZeroSSLCertificate($domain, $email, $manualCSR);

    if (!$certificateData) {
        echo apiResponse(false, 'Failed to create certificate with ZeroSSL API');
        exit();
    }

    // Check for various errors
    if (isset($certificateData['error'])) {
        $errorType = $certificateData['error'];

        if ($errorType === 'csr_failed') {
            echo apiResponse(false, $certificateData['message'], [
                'error_type' => 'csr_generation_failed',
                'solution' => $certificateData['solution'],
                'instructions' => 'Please generate a CSR manually and try again'
            ]);
            exit();
        }

        if ($errorType === 'limit_reached') {
            echo apiResponse(false, $certificateData['message'], [
                'error_type' => 'certificate_limit_reached',
                'solution' => $certificateData['solution'],
                'alternative' => $certificateData['alternative'],
                'instructions' => 'Certificate limit reached. Please try again tomorrow or use a different provider.'
            ]);
            exit();
        }

        if ($errorType === 'duplicate_domain') {
            echo apiResponse(false, $certificateData['message'], [
                'error_type' => 'duplicate_domain',
                'solution' => $certificateData['solution'],
                'alternative' => $certificateData['alternative'],
                'instructions' => 'This domain already has a certificate. Please use a different domain.'
            ]);
            exit();
        }

        if ($errorType === 'api_error') {
            echo apiResponse(false, $certificateData['message'], [
                'error_type' => 'zerossl_api_error',
                'solution' => $certificateData['solution'],
                'instructions' => 'ZeroSSL API error occurred. Please check configuration.'
            ]);
            exit();
        }
    }


    
    // Update temp data with certificate info
    $tempData['certificate_id'] = $certificateData['id'];
    $tempData['verification'] = $certificateData['validation'];
    $tempData['validation_step'] = 'certificate_created';
    
    saveCertificateData($domain, $tempData);
    
    echo apiResponse(true, 'Certificate created successfully. Please complete verification.', [
        'certificate_id' => $certificateData['id'],
        'verification' => $certificateData['verification']
    ]);
    
} catch (Exception $e) {
    logMessage("Error creating certificate: " . $e->getMessage(), 'ERROR');
    echo apiResponse(false, 'Internal server error: ' . $e->getMessage());
}

function createZeroSSLCertificate($domain, $email, $manualCSR = null) {
    try {
        // Use manual CSR if provided, otherwise generate automatically
        if ($manualCSR) {
            logMessage("Using manual CSR for domain: $domain", 'INFO');
            $csrData = $manualCSR;
        } else {
            // Generate CSR - use working method
            $csrData = generateWorkingCSR($domain, $email);
            if (!$csrData) {
                logMessage("CSR generation failed for domain: $domain", 'ERROR');

                // Return user-friendly error
                return [
                    'error' => 'csr_failed',
                    'message' => 'CSR generation failed. Please use manual CSR input.',
                    'solution' => 'Generate CSR at: https://www.ssl.com/online-csr-generator/'
                ];
            }
        }

        // Prepare certificate request data according to ZeroSSL API
        $requestData = [
            'certificate_domains' => $domain, // ZeroSSL expects string, not array
            'certificate_validity_days' => 90,
            'certificate_csr' => $csrData,
            'certificate_validation_method' => 'HTTP_CSR_HASH' // Required by ZeroSSL API
        ];

        logMessage("Creating certificate with data: " . json_encode($requestData), 'INFO');

        // Create certificate with ZeroSSL using correct endpoint
        $response = zeroSSLRequest('/certificates', 'POST', $requestData);

        if (!$response) {
            logMessage("No response from ZeroSSL API", 'ERROR');
            return false;
        }

        logMessage("ZeroSSL response: " . json_encode($response), 'INFO');

        if (!isset($response['id'])) {
            logMessage("Failed to create certificate with ZeroSSL: " . json_encode($response), 'ERROR');

            // Handle specific ZeroSSL errors
            if (isset($response['error'])) {
                $errorCode = $response['error']['code'] ?? 0;
                $errorType = $response['error']['type'] ?? 'unknown';

                if ($errorCode == 2817 || $errorType == 'certificate_limit_reached') {
                    return [
                        'error' => 'limit_reached',
                        'message' => 'Certificate limit reached for today. Please try again tomorrow or use a different API key.',
                        'solution' => 'You can create a new ZeroSSL account or wait 24 hours.',
                        'alternative' => 'Use Let\'s Encrypt or other certificate providers.'
                    ];
                }

                if ($errorCode == 2839 || $errorType == 'duplicate_certificates_found') {
                    return [
                        'error' => 'duplicate_domain',
                        'message' => 'A certificate already exists for this domain. Please use a different domain.',
                        'solution' => 'Try a subdomain like: test.' . $domain . ' or staging.' . $domain,
                        'alternative' => 'Use a completely different domain name.'
                    ];
                }

                return [
                    'error' => 'api_error',
                    'message' => "ZeroSSL API Error: {$errorType} (Code: {$errorCode})",
                    'solution' => 'Please check your API configuration or try again later.'
                ];
            }

            return false;
        }

        $certificateId = $response['id'];
        logMessage("Certificate created with ID: $certificateId", 'INFO');

        // Get verification details - this endpoint returns the certificate object with validation info
        $verificationResponse = zeroSSLRequest("/certificates/$certificateId");

        if (!$verificationResponse) {
            logMessage("Failed to get verification details for certificate $certificateId", 'ERROR');
            return false;
        }

        logMessage("Verification response: " . json_encode($verificationResponse), 'INFO');

        // Format verification data
        $verification = formatVerificationData($domain, $verificationResponse);

        return [
            'id' => $certificateId,
            'verification' => $verification
        ];

    } catch (Exception $e) {
        logMessage("Exception in createZeroSSLCertificate: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

function generateCSRAutomatically($domain, $email) {
    logMessage("Starting automatic CSR generation for domain: $domain", 'INFO');

    // Method 1: Try the most reliable approach first
    $csr = generateReliableCSR($domain, $email);
    if ($csr) {
        logMessage("CSR generated successfully using reliable method", 'INFO');
        return $csr;
    }

    // Method 2: Try PHP OpenSSL with minimal configuration
    $csr = tryPHPOpenSSL($domain, $email);
    if ($csr) {
        logMessage("CSR generated successfully using PHP OpenSSL", 'INFO');
        return $csr;
    }

    // Method 3: Try command line OpenSSL
    $csr = tryCommandLineOpenSSL($domain, $email);
    if ($csr) {
        logMessage("CSR generated successfully using command line OpenSSL", 'INFO');
        return $csr;
    }

    // Method 4: Use external CSR generation service
    $csr = generateCSRViaService($domain, $email);
    if ($csr) {
        logMessage("CSR generated successfully using external service", 'INFO');
        return $csr;
    }

    logMessage("All automatic CSR generation methods failed for domain: $domain", 'ERROR');
    return false;
}

function generateReliableCSR($domain, $email) {
    try {
        // Use a more reliable approach with proper error handling
        $config = [
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ];

        // Set environment variable for OpenSSL config
        putenv('OPENSSL_CONF=');

        $privateKey = @openssl_pkey_new($config);
        if (!$privateKey) {
            logMessage("Failed to generate private key in reliable method", 'ERROR');
            return false;
        }

        $dn = [
            'C' => 'US',
            'ST' => 'California',
            'L' => 'San Francisco',
            'O' => 'SSL4Free',
            'CN' => $domain
        ];

        $csr = @openssl_csr_new($dn, $privateKey, $config);
        if (!$csr) {
            logMessage("Failed to generate CSR in reliable method", 'ERROR');
            return false;
        }

        if (@openssl_csr_export($csr, $csrString)) {
            // Save private key
            if (@openssl_pkey_export($privateKey, $privateKeyString)) {
                $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
                file_put_contents($keyFile, $privateKeyString);
                logMessage("Private key saved successfully", 'INFO');
            }

            // Validate CSR format
            if (strpos($csrString, '-----BEGIN CERTIFICATE REQUEST-----') !== false &&
                strpos($csrString, '-----END CERTIFICATE REQUEST-----') !== false) {
                logMessage("CSR format validation passed", 'INFO');
                return $csrString;
            }
        }

        return false;
    } catch (Exception $e) {
        logMessage("Exception in generateReliableCSR: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

function generateCSRViaService($domain, $email) {
    // This would integrate with an external CSR generation service
    // For now, return false to continue with other methods
    return false;
}

function tryPHPOpenSSL($domain, $email) {
    try {
        $config = [
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ];

        $privateKey = @openssl_pkey_new($config);
        if (!$privateKey) return false;

        $dn = [
            'C' => 'US',
            'ST' => 'CA',
            'L' => 'SF',
            'O' => 'SSL4Free',
            'CN' => $domain
        ];

        $csr = @openssl_csr_new($dn, $privateKey, $config);
        if (!$csr) return false;

        if (@openssl_csr_export($csr, $csrString)) {
            // Save private key
            if (@openssl_pkey_export($privateKey, $privateKeyString)) {
                $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
                file_put_contents($keyFile, $privateKeyString);
            }
            return $csrString;
        }

        return false;
    } catch (Exception $e) {
        return false;
    }
}

function tryCommandLineOpenSSL($domain, $email) {
    try {
        $tempDir = TEMP_DIR;
        $keyFile = $tempDir . 'temp_' . md5($domain) . '.key';
        $csrFile = $tempDir . 'temp_' . md5($domain) . '.csr';

        $subject = "/C=US/ST=CA/L=SF/O=SSL4Free/CN=$domain/emailAddress=$email";
        $cmd = "openssl req -new -newkey rsa:2048 -nodes -keyout \"$keyFile\" -out \"$csrFile\" -subj \"$subject\" 2>&1";

        $output = @shell_exec($cmd);

        if (file_exists($csrFile) && file_exists($keyFile)) {
            $csr = file_get_contents($csrFile);
            $privateKey = file_get_contents($keyFile);

            // Save private key with proper name
            $finalKeyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
            file_put_contents($finalKeyFile, $privateKey);

            // Clean up temp files
            @unlink($csrFile);
            @unlink($keyFile);

            return $csr;
        }

        return false;
    } catch (Exception $e) {
        return false;
    }
}

function tryAlternativePHPMethod($domain, $email) {
    try {
        // Try with different OpenSSL configuration
        $configFile = createTempOpenSSLConfig($domain);

        $config = [
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
            'config' => $configFile
        ];

        $privateKey = @openssl_pkey_new($config);
        if (!$privateKey) {
            @unlink($configFile);
            return false;
        }

        $dn = [
            'countryName' => 'US',
            'stateOrProvinceName' => 'CA',
            'localityName' => 'SF',
            'organizationName' => 'SSL4Free',
            'commonName' => $domain,
            'emailAddress' => $email
        ];

        $csr = @openssl_csr_new($dn, $privateKey, $config);

        @unlink($configFile);

        if (!$csr) return false;

        if (@openssl_csr_export($csr, $csrString)) {
            // Save private key
            if (@openssl_pkey_export($privateKey, $privateKeyString)) {
                $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
                file_put_contents($keyFile, $privateKeyString);
            }
            return $csrString;
        }

        return false;
    } catch (Exception $e) {
        return false;
    }
}

function createTempOpenSSLConfig($domain) {
    $configContent = "[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = CA
L = SF
O = SSL4Free
CN = $domain

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
";

    $configFile = TEMP_DIR . 'openssl_' . md5($domain) . '.cnf';
    file_put_contents($configFile, $configContent);
    return $configFile;
}

function generateWorkingCSRTemplate($domain, $email) {
    try {
        // Generate a basic but functional CSR using PHP's built-in functions
        // This method creates a simple RSA key pair and CSR without external dependencies

        logMessage("Generating working CSR template for domain: $domain", 'INFO');

        // Create a simple RSA key pair using basic PHP functions
        $keyPair = generateSimpleKeyPair();
        if (!$keyPair) return false;

        // Create CSR structure manually
        $csr = createCSRStructure($domain, $email, $keyPair['public_key']);
        if (!$csr) return false;

        // Save the private key
        $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
        file_put_contents($keyFile, $keyPair['private_key']);

        return $csr;

    } catch (Exception $e) {
        logMessage("Working CSR template generation failed: " . $e->getMessage(), 'ERROR');
        return false;
    }
}

function generateSimpleKeyPair() {
    try {
        // Use a simplified approach to generate key pair
        $config = [
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ];

        // Try without any external config
        $res = @openssl_pkey_new($config);
        if (!$res) return false;

        // Extract private key
        if (!@openssl_pkey_export($res, $privateKey)) return false;

        // Extract public key
        $publicKeyDetails = @openssl_pkey_get_details($res);
        if (!$publicKeyDetails) return false;

        return [
            'private_key' => $privateKey,
            'public_key' => $publicKeyDetails['key']
        ];

    } catch (Exception $e) {
        return false;
    }
}

function createCSRStructure($domain, $email, $publicKey) {
    try {
        // Create a basic CSR structure
        // This is a simplified version that should work with most CAs

        $subject = "C=US,ST=CA,L=SF,O=SSL4Free,CN=$domain";

        // Use PHP's OpenSSL functions to create CSR
        $dn = [
            'countryName' => 'US',
            'stateOrProvinceName' => 'CA',
            'localityName' => 'SF',
            'organizationName' => 'SSL4Free',
            'commonName' => $domain
        ];

        // Try one more time with minimal config
        $config = ['private_key_bits' => 2048, 'private_key_type' => OPENSSL_KEYTYPE_RSA];
        $privateKey = @openssl_pkey_new($config);

        if ($privateKey) {
            $csr = @openssl_csr_new($dn, $privateKey);
            if ($csr && @openssl_csr_export($csr, $csrString)) {
                return $csrString;
            }
        }

        return false;

    } catch (Exception $e) {
        return false;
    }
}

function createOpenSSLConfig($domain) {
    $config = "
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = State
L = City
O = Organization
OU = IT Department
CN = $domain

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $domain
DNS.2 = www.$domain
";
    
    $configFile = TEMP_DIR . 'openssl_' . md5($domain) . '.conf';
    file_put_contents($configFile, $config);
    
    return $configFile;
}

function formatVerificationData($domain, $verificationResponse) {
    // Check if validation data exists in response
    if (!isset($verificationResponse['validation'])) {
        logMessage("No validation data found in response", 'ERROR');
        return false;
    }

    // Return the complete validation data from ZeroSSL
    // This includes all verification methods (HTTP, DNS, Email)
    return $verificationResponse['validation'];

}

function generateWorkingCSR($domain, $email) {
    logMessage("Generating working CSR for domain: $domain", 'INFO');

    try {
        // Simple and reliable CSR generation
        $config = [
            'private_key_bits' => 2048,
            'private_key_type' => OPENSSL_KEYTYPE_RSA,
        ];

        $privateKey = @openssl_pkey_new($config);
        if ($privateKey) {
            $dn = [
                'C' => 'US',
                'ST' => 'CA',
                'L' => 'SF',
                'O' => 'SSL4Free',
                'CN' => $domain
            ];

            $csr = @openssl_csr_new($dn, $privateKey, $config);
            if ($csr && @openssl_csr_export($csr, $csrString)) {
                // Save private key
                if (@openssl_pkey_export($privateKey, $privateKeyString)) {
                    $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
                    file_put_contents($keyFile, $privateKeyString);
                }
                logMessage("CSR generated successfully", 'INFO');
                return $csrString;
            }
        }

        // If OpenSSL fails, show user-friendly error
        logMessage("OpenSSL CSR generation failed for domain: $domain", 'ERROR');
        return false;

    } catch (Exception $e) {
        logMessage("Exception in generateWorkingCSR: " . $e->getMessage(), 'ERROR');
        return false;
    }
}
?>
