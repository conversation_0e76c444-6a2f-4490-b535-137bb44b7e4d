<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL Generator Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8">SSL Certificate Generator Test</h1>
        
        <div class="max-w-2xl mx-auto">
            <!-- Input Form -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Generate SSL Certificate</h2>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Domain</label>
                    <input type="text" id="domain" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="example.com">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input type="email" id="email" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="<EMAIL>">
                </div>
                
                <button onclick="generateSSL()" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                    Generate SSL Certificate
                </button>
            </div>
            
            <!-- Results -->
            <div id="results" class="bg-white rounded-lg shadow-md p-6" style="display: none;">
                <h2 class="text-xl font-semibold mb-4">SSL Certificate Generated</h2>
                
                <!-- Certificate -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        SSL Certificate (CRT)
                        <span id="certStatus" class="ml-2 text-sm text-green-600">
                            <i class="fas fa-check-circle"></i> Valid
                        </span>
                    </label>
                    <textarea id="certificate" readonly class="w-full h-32 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-xs"></textarea>
                    <button onclick="copyCert()" class="mt-2 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        <i class="fas fa-copy mr-1"></i> Copy Certificate
                    </button>
                </div>
                
                <!-- Private Key -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Private Key (KEY)
                        <span id="keyStatus" class="ml-2 text-sm text-green-600">
                            <i class="fas fa-check-circle"></i> Valid
                        </span>
                    </label>
                    <textarea id="privateKey" readonly class="w-full h-32 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-xs"></textarea>
                    <button onclick="copyKey()" class="mt-2 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        <i class="fas fa-copy mr-1"></i> Copy Private Key
                    </button>
                </div>
                
                <!-- CA Bundle -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        CA Bundle (CABUNDLE)
                        <span id="caStatus" class="ml-2 text-sm text-green-600">
                            <i class="fas fa-check-circle"></i> Valid
                        </span>
                    </label>
                    <textarea id="caBundle" readonly class="w-full h-32 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-xs"></textarea>
                    <button onclick="copyCA()" class="mt-2 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                        <i class="fas fa-copy mr-1"></i> Copy CA Bundle
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function generateSSL() {
            const domain = document.getElementById('domain').value;
            const email = document.getElementById('email').value;
            
            if (!domain || !email) {
                alert('Please enter both domain and email');
                return;
            }
            
            try {
                const response = await fetch('api/simple-ssl.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        domain: domain,
                        email: email,
                        staging: false
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Show results
                    document.getElementById('certificate').value = data.data.certificate;
                    document.getElementById('privateKey').value = data.data.privateKey;
                    document.getElementById('caBundle').value = data.data.caBundle;
                    
                    // Show results section
                    document.getElementById('results').style.display = 'block';
                    
                    // Scroll to results
                    document.getElementById('results').scrollIntoView({ behavior: 'smooth' });
                    
                    alert('SSL Certificate generated successfully!');
                } else {
                    alert('Error: ' + data.message);
                }
                
            } catch (error) {
                alert('Error generating SSL: ' + error.message);
            }
        }
        
        function copyCert() {
            copyToClipboard(document.getElementById('certificate').value, 'Certificate copied!');
        }
        
        function copyKey() {
            copyToClipboard(document.getElementById('privateKey').value, 'Private key copied!');
        }
        
        function copyCA() {
            copyToClipboard(document.getElementById('caBundle').value, 'CA Bundle copied!');
        }
        
        function copyToClipboard(text, message) {
            navigator.clipboard.writeText(text).then(() => {
                alert(message);
            });
        }
    </script>
</body>
</html>
