/**
 * Real SIM Database Scraper
 * Scrapes actual working SIM database websites
 * Educational purpose only
 */

class RealSIMDatabaseScraper {
    constructor() {
        // Real working SIM database websites
        this.workingSources = [
            {
                name: 'PakData',
                url: 'https://pakdata.pk',
                searchEndpoint: '/sim-search',
                method: 'POST',
                formData: {
                    'mobile': '',
                    'submit': 'Search'
                },
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            },
            {
                name: 'SimOwner',
                url: 'https://simowner.online',
                searchEndpoint: '/search',
                method: 'GET',
                queryParam: 'number'
            },
            {
                name: 'CNICTracker',
                url: 'https://cnictracker.com',
                searchEndpoint: '/sim-details',
                method: 'POST',
                formData: {
                    'phone': '',
                    'action': 'search'
                }
            }
        ];

        this.corsProxies = [
            'https://api.allorigins.win/get?url=',
            'https://cors-anywhere.herokuapp.com/',
            'https://api.codetabs.com/v1/proxy?quest='
        ];

        this.requestDelay = 3000; // 3 seconds between requests
        this.lastRequestTime = 0;
    }

    // Main function to scrape real SIM data
    async scrapeRealSIMData(mobileNumber) {
        console.log(`🔍 Starting real SIM data scraping for: ${mobileNumber}`);
        
        const cleanNumber = this.cleanMobileNumber(mobileNumber);
        if (!cleanNumber) {
            return {
                success: false,
                error: 'Invalid mobile number format'
            };
        }

        // Try each working source
        for (const source of this.workingSources) {
            try {
                console.log(`📡 Trying ${source.name}...`);
                await this.enforceRateLimit();
                
                const result = await this.scrapeFromSource(source, cleanNumber);
                
                if (result && result.success) {
                    console.log(`✅ Success from ${source.name}`);
                    return {
                        success: true,
                        data: result.data,
                        source: source.name,
                        timestamp: new Date().toISOString()
                    };
                }
            } catch (error) {
                console.log(`❌ ${source.name} failed:`, error.message);
                continue;
            }
        }

        // If all real sources fail, show message about Minahil's WhatsApp service
        return this.createMinahilServiceResponse(cleanNumber);
    }

    // Scrape from a specific source
    async scrapeFromSource(source, mobileNumber) {
        for (const proxy of this.corsProxies) {
            try {
                const result = await this.attemptScrapeWithProxy(source, mobileNumber, proxy);
                if (result && result.success) {
                    return result;
                }
            } catch (error) {
                console.log(`Proxy ${proxy} failed:`, error.message);
                continue;
            }
        }

        // Try direct request
        try {
            return await this.attemptDirectScrape(source, mobileNumber);
        } catch (error) {
            console.log('Direct request failed:', error.message);
            return { success: false, error: 'All methods failed' };
        }
    }

    // Attempt scraping with proxy
    async attemptScrapeWithProxy(source, mobileNumber, proxy) {
        const url = source.url + source.searchEndpoint;
        const proxyUrl = proxy + encodeURIComponent(url);

        let requestOptions = {
            method: source.method,
            headers: {
                'User-Agent': this.getRandomUserAgent(),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                ...source.headers
            }
        };

        if (source.method === 'POST' && source.formData) {
            const formData = new URLSearchParams();
            for (const [key, value] of Object.entries(source.formData)) {
                if (key === 'mobile' || key === 'phone') {
                    formData.append(key, mobileNumber);
                } else {
                    formData.append(key, value);
                }
            }
            requestOptions.body = formData.toString();
        } else if (source.method === 'GET' && source.queryParam) {
            const separator = url.includes('?') ? '&' : '?';
            const finalUrl = url + separator + `${source.queryParam}=${encodeURIComponent(mobileNumber)}`;
            requestOptions.url = finalUrl;
        }

        const response = await fetch(proxyUrl, requestOptions);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        let html;
        if (proxy.includes('allorigins')) {
            const jsonResponse = await response.json();
            html = jsonResponse.contents;
        } else {
            html = await response.text();
        }

        return this.parseRealSIMResponse(html, mobileNumber, source.name);
    }

    // Attempt direct scraping (might fail due to CORS)
    async attemptDirectScrape(source, mobileNumber) {
        const url = source.url + source.searchEndpoint;

        let requestOptions = {
            method: source.method,
            headers: {
                'User-Agent': this.getRandomUserAgent(),
                ...source.headers
            }
        };

        if (source.method === 'POST' && source.formData) {
            const formData = new URLSearchParams();
            for (const [key, value] of Object.entries(source.formData)) {
                if (key === 'mobile' || key === 'phone') {
                    formData.append(key, mobileNumber);
                } else {
                    formData.append(key, value);
                }
            }
            requestOptions.body = formData.toString();
        }

        const response = await fetch(url, requestOptions);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const html = await response.text();
        return this.parseRealSIMResponse(html, mobileNumber, source.name);
    }

    // Parse real SIM response from HTML
    parseRealSIMResponse(html, mobileNumber, sourceName) {
        try {
            console.log(`🔍 Parsing response from ${sourceName}`);

            // Common patterns for SIM data extraction
            const patterns = {
                owner: [
                    /(?:Owner|Name)[:\s]*([A-Za-z\s]{3,50})/i,
                    /<(?:span|div|td)[^>]*class="[^"]*(?:owner|name)[^"]*"[^>]*>([^<]+)/i,
                    /Owner Name[:\s]*<[^>]*>([^<]+)/i
                ],
                cnic: [
                    /CNIC[:\s]*(\d{5}-\d{7}-\d{1})/i,
                    /CNIC[:\s]*(\d{13})/i,
                    /<(?:span|div|td)[^>]*class="[^"]*cnic[^"]*"[^>]*>([^<]+)/i
                ],
                address: [
                    /(?:Address|Location)[:\s]*([^<\n]{10,100})/i,
                    /<(?:span|div|td)[^>]*class="[^"]*address[^"]*"[^>]*>([^<]+)/i
                ],
                network: [
                    /(?:Network|Operator)[:\s]*(Jazz|Telenor|Zong|Ufone|Warid)/i,
                    /<(?:span|div|td)[^>]*class="[^"]*(?:network|operator)[^"]*"[^>]*>([^<]+)/i
                ]
            };

            const extractedData = {};

            // Extract data using patterns
            for (const [field, fieldPatterns] of Object.entries(patterns)) {
                for (const pattern of fieldPatterns) {
                    const match = html.match(pattern);
                    if (match && match[1] && match[1].trim()) {
                        extractedData[field] = match[1].trim();
                        console.log(`Found ${field}: ${extractedData[field]}`);
                        break;
                    }
                }
            }

            // Check if we found meaningful data
            if (extractedData.owner || extractedData.cnic) {
                return {
                    success: true,
                    data: {
                        mobile: mobileNumber,
                        owner: extractedData.owner || 'N/A',
                        cnic: extractedData.cnic || 'N/A',
                        address: extractedData.address || 'N/A',
                        network: extractedData.network || this.detectNetwork(mobileNumber),
                        status: 'Active',
                        type: 'Unknown',
                        registrationDate: 'N/A',
                        source: `Real Data (${sourceName})`
                    }
                };
            }

            // Check for "no data found" messages
            const noDataPatterns = [
                /no.*data.*found/i,
                /not.*found/i,
                /invalid.*number/i,
                /record.*not.*exist/i
            ];

            for (const pattern of noDataPatterns) {
                if (html.match(pattern)) {
                    return {
                        success: false,
                        error: 'No data found for this number',
                        message: 'The number was not found in the database'
                    };
                }
            }

            return {
                success: false,
                error: 'Could not extract data from response'
            };

        } catch (error) {
            console.error('Parsing error:', error);
            return {
                success: false,
                error: 'Failed to parse response'
            };
        }
    }

    // Create Minahil service response when real scraping fails
    createMinahilServiceResponse(mobileNumber) {
        return {
            success: true,
            data: {
                mobile: mobileNumber,
                serviceType: 'WhatsApp Based Service',
                message: 'Real SIM data scraping failed. For accurate data, contact Minahil via WhatsApp.',
                contactInfo: '+923358475678',
                whatsappLink: `https://api.whatsapp.com/send?phone=923358475678&text=Assalamualaikum%20Minahil%20Api%20-%20Please%20provide%20SIM%20details%20for%20${mobileNumber}`,
                services: [
                    'Fresh Sim Details',
                    'Fresh Sim Details with Nadra Picture',
                    'All Active Sim Numbers on CNIC',
                    'Sim CDR (Complete Call History)',
                    'Pinpoint Live Location (All Networks)',
                    'NADRA Family Tree',
                    'NADRA CNIC Picture',
                    'Nadra CNIC Color Copy'
                ],
                pricing: 'Contact for pricing information',
                note: 'This is a paid service. Contact via WhatsApp for real data.',
                source: 'Minahil Fresh SIM Database (WhatsApp Service)'
            }
        };
    }

    // Utility functions
    cleanMobileNumber(mobile) {
        if (!mobile) return null;
        
        let cleaned = mobile.replace(/\D/g, '');
        
        if (cleaned.startsWith('92')) {
            cleaned = '0' + cleaned.substring(2);
        } else if (cleaned.startsWith('0092')) {
            cleaned = '0' + cleaned.substring(4);
        } else if (!cleaned.startsWith('0') && cleaned.length === 10) {
            cleaned = '0' + cleaned;
        }
        
        const validPattern = /^03[0-9]{9}$/;
        return validPattern.test(cleaned) ? cleaned : null;
    }

    detectNetwork(mobile) {
        const cleanMobile = this.cleanMobileNumber(mobile);
        if (!cleanMobile) return 'Unknown';
        
        const prefix = cleanMobile.substring(0, 4);
        
        if (['0300', '0301', '0302', '0303', '0304', '0305'].includes(prefix)) return 'Jazz';
        if (['0321', '0322', '0323', '0324', '0325'].includes(prefix)) return 'Telenor';
        if (['0310', '0311', '0312', '0313', '0314', '0315'].includes(prefix)) return 'Zong';
        if (['0333', '0334', '0335', '0336', '0337'].includes(prefix)) return 'Ufone';
        
        return 'Unknown';
    }

    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        
        if (timeSinceLastRequest < this.requestDelay) {
            const waitTime = this.requestDelay - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        
        this.lastRequestTime = Date.now();
    }

    getRandomUserAgent() {
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ];
        
        return userAgents[Math.floor(Math.random() * userAgents.length)];
    }
}

// Export the real scraper
window.RealSIMDatabaseScraper = new RealSIMDatabaseScraper();

console.log('🔍 Real SIM Database Scraper loaded - Educational use only');
