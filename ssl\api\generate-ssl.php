<?php
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

require_once 'acme-client.php';
require_once 'ssl-validator.php';

// Simple domain validation function
function isValidDomain($domain) {
    // Remove www. prefix if present
    $domain = preg_replace('/^www\./', '', $domain);

    // Basic domain format validation
    $pattern = '/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/';
    return preg_match($pattern, $domain);
}

// Generate serial number
function generateSerialNumber() {
    return 'SSL' . time() . rand(1000, 9999);
}

// Log SSL generation
function logSSLGeneration($domain, $email, $status) {
    $logEntry = date('Y-m-d H:i:s') . " - Domain: $domain, Email: $email, Status: $status\n";
    file_put_contents('ssl_generation.log', $logEntry, FILE_APPEND | LOCK_EX);
}

// Check if domain is accessible
function isDomainAccessible($domain) {
    // Try to make HTTP request to domain
    $url = "http://" . $domain;

    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => 'User-Agent: SSL-Generator/1.0'
        ]
    ]);

    $result = @file_get_contents($url, false, $context);

    // If we get any response (even error pages), domain is accessible
    return $result !== false || !empty($http_response_header);
}

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!$input || !isset($input['domain']) || !isset($input['email'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit();
}

$domain = htmlspecialchars(trim($input['domain']));
$email = filter_var(trim($input['email']), FILTER_VALIDATE_EMAIL);

// Remove protocol if present
$domain = preg_replace('/^https?:\/\//', '', $domain);
$domain = rtrim($domain, '/');

if (!$email) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid email address']);
    exit();
}

// STEP 1: Basic Domain Validation
if (!isValidDomain($domain)) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid domain format. Please enter a valid domain name.'
    ]);
    exit();
}

// STEP 2: Check if domain is accessible
if (!isDomainAccessible($domain)) {
    echo json_encode([
        'success' => false,
        'message' => 'Domain is not accessible. Please ensure your domain is pointing to this server and accessible via HTTP.'
    ]);
    exit();
}

// Check if we should use staging or production
$useStaging = isset($input['staging']) ? $input['staging'] : false;

// SSL certificate generation
try {
    // Use Let's Encrypt to generate real SSL certificate
    $acmeClient = new ACMEClient($useStaging);
    $result = $acmeClient->generateCertificate($domain, $email);

    if ($result['success']) {
        $result['domain'] = $domain;
        $result['demo_mode'] = false;
    }

    if ($result['success']) {
        // Log the generation
        logSSLGeneration($domain, $email, 'SUCCESS');

        // SSL package is valid (demo mode)

        // Return success response with complete SSL package
        $message = $useStaging
            ? 'SSL certificate generated successfully (staging environment)'
            : 'SSL certificate generated successfully from Let\'s Encrypt';

        echo json_encode([
            'success' => true,
            'message' => $message,
            'data' => [
                'domain' => $result['domain'],
                'certificate' => $result['certificate'],        // Domain certificate (CRT)
                'privateKey' => $result['privateKey'],          // Private key (KEY)
                'caBundle' => $result['caBundle'],              // CA Bundle (CABUNDLE)
                'fullChain' => $result['fullChain'],            // Complete certificate chain
                'validFrom' => $result['validFrom'],
                'validTo' => $result['validTo'],
                'issuer' => $result['issuer'],
                'staging' => $useStaging,
                'demo_mode' => false,
                'serialNumber' => generateSerialNumber()
                'files' => [
                    'certificate' => $domain . '.crt',
                    'privateKey' => $domain . '.key',
                    'caBundle' => $domain . '_ca_bundle.crt',
                    'fullChain' => $domain . '_fullchain.crt'
                ]
            ]
        ]);
    } else {
        // Log the error
        logSSLGeneration($domain, $email, 'ERROR: ' . $result['error']);

        echo json_encode([
            'success' => false,
            'message' => 'SSL certificate generation failed: ' . $result['error']
        ]);
    }

} catch (Exception $e) {
    error_log("SSL Generation error: " . $e->getMessage());
    logSSLGeneration($domain, $email, 'EXCEPTION: ' . $e->getMessage());

    echo json_encode([
        'success' => false,
        'message' => 'Failed to generate SSL certificate: ' . $e->getMessage()
    ]);
}

// Domain validation function
function validateDomain($domain) {
    // Remove protocol if present
    $domain = preg_replace('/^https?:\/\//', '', $domain);
    
    // Remove trailing slash
    $domain = rtrim($domain, '/');
    
    // Basic domain validation
    return filter_var($domain, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME);
}

// Mock domain ownership validation
function validateDomainOwnership($domain) {
    // In a real implementation, this would:
    // 1. Create a challenge file or DNS record
    // 2. Verify the challenge is accessible
    // 3. Confirm domain ownership
    
    // For demo purposes, we'll simulate this process
    $challengeToken = bin2hex(random_bytes(16));
    
    // Simulate DNS/HTTP challenge validation
    // In reality, you would check if the domain responds to the challenge
    
    return [
        'success' => true,
        'message' => 'Domain ownership validated',
        'challengeToken' => $challengeToken
    ];
}

// Generate private key (mock)
function generatePrivateKey() {
    // In a real implementation, this would generate an actual RSA private key
    // For demo purposes, we'll return a mock private key
    
    return "-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC4iP8QzBc6gGJd
qXAv3d+q7OMQHbZPMUeJOXYqMCqQ6e+y01PYEQ8vJMnGXtc+RWJ00igtLb9RBeOw
d+lMAMLLnGyeMz3aTUVvHLY+AeVjhegAoVTW+aaCv/DurhGYCyTDFElFrpU4rDlP
f5P4hoZ126TusjqFeZh6iVvdapXDVgjUtrbaKCcOQseXNleDGu4GMT1sUB8eMZ8I
Kz52hZZPgqmT1B7VlfVNTI51EQdBt8dHEzINKLoP0IOVUfFVCoPZNiOgFxONFrtC
mRfMS4b83aIm6m3Y+bBH4xPCu0AUG4CfaUJ4YZakKXFn9nNBGYxc0zKxYgwEfnO/
azXAgMBAAECggEAFhgZzwI6Mi6w4r6ArtAcC2SJBFAXd7a1lQ5bc4xOfz+GlSA7
s+t2kAzXuQZdqZHjlVOADVNVmsKMrGypmBLADxFxeCWpAOVOaw8rKrdB2fwHxvyK
VwQlDkHFMSMEAT4r+fcaKlbADZmTgaRiGVfGQcEuZ4FMpq+VVrANxngjQQKBgQDm
OfLasNWGQYyXtwlBo1BM+dAXa4dDNd3uE4tcPFYFqRdlAgMBAAECggEAFhgZzwI6
Mi6w4r6ArtAcC2SJBFAXd7a1lQ5bc4xOfz+GlSA7s+t2kAzXuQZdqZHjlVOADVNV
msKMrGypmBLADxFxeCWpAOVOaw8rKrdB2fwHxvyKVwQlDkHFMSMEAT4r+fcaKlbA
DZmTgaRiGVfGQcEuZ4FMpq+VVrANxngjQQKBgQDmOfLasNWGQYyXtwlBo1BM+dAX
a4dDNd3uE4tcPFYFqRdlAgMBAAECggEAFhgZzwI6Mi6w4r6ArtAcC2SJBFAXd7a1
lQ5bc4xOfz+GlSA7s+t2kAzXuQZdqZHjlVOADVNVmsKMrGypmBLADxFxeCWpAOVO
aw8rKrdB2fwHxvyKVwQlDkHFMSMEAT4r+fcaKlbADZmTgaRiGVfGQcEuZ4FMpq+V
VrANxngjQQKBgQDmOfLasNWGQYyXtwlBo1BM+dAXa4dDNd3uE4tcPFYFqRdl
-----END PRIVATE KEY-----";
}

// Generate Certificate Signing Request (mock)
function generateCSR($domain, $privateKey) {
    // In a real implementation, this would create an actual CSR
    // For demo purposes, we'll return a mock CSR
    
    return "-----BEGIN CERTIFICATE REQUEST-----
MIICijCCAXICAQAwRTELMAkGA1UEBhMCQVUxEzARBgNVBAgMClNvbWUtU3RhdGUx
ITAfBgNVBAoMGEludGVybmV0IFdpZGdpdHMgUHR5IEx0ZDCCASIwDQYJKoZIhvcN
AQEBBQADggEPADCCAQoCggEBALiI/xDMFzqAYl2pcC/d36rs4xAdtk8xR4k5dio
wKpDp77LTU9gRDy8kycZe1z5FYnTSKC0tv1EF47B36UwAwsucbJ4zPdpNRW8ctj
4B5WOF6AChVNb5poK/8O6uEZgLJMMUSUWulTisOU9/k/iGhnXbpO6yOoV5mHqJW
91qlcNWCNS2tsqIlw5Cx5c2V4Ma7gYxPWxQHx4xnwgrPnaFlk+CqZPUHtWV9U1M
jnURB0G3x0cTMg0oug/Qg5VR8VUKg9k2I6AXE40Wu0KZF8xLhvzdoibqbdj5sEf
jE8K7QBQbgJ9pQnhhlqQpcWf2c0EZjFzTMrFiDAR+c79rNcCAwEAAaAAMA0GCSq
GSIb3DQEBCwUAA4IBAQAAuBiMyGKIiX8LVBrJ9S/k9l0YQIUP3P32x/LuHqVMaQO
cvaTHFr+FjkmskZss0C1TS4udrRVOEcuzHrDiTJcYbfF2dRTuDjWiA/qjQUuaInO
e5Sb/qxXFFBNXB9d1rwDvZLrr+/IFuA6QQO/0wUfMj2m/IBXXfW1JQngjzgHJFHV
qVHccUxHcfzUxDw4X4iv2u1VoE2adQBrTTOBddlWHZO1rDMYHAA13Sj0C6rVwSBj
CCRVYuY24ELiaLNiPVYirzrwv9XyqSakqz6KXZMaABdhyDTLcYBrSFcuIiJ2Dvhg
+f/J4TgmJFcGGnrHzuQfFOoTl/UmzLXCFcEhsqhzVg==
-----END CERTIFICATE REQUEST-----";
}

// Generate SSL certificate (mock)
function generateSSLCertificate($domain, $csr) {
    // In a real implementation, this would submit the CSR to Let's Encrypt
    // and receive back a signed certificate
    
    $currentDate = date('Y-m-d');
    $expiryDate = date('Y-m-d', strtotime('+90 days'));
    
    return "-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjUwNzE5MDAwMDAwWhcNMjUxMDE3MDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuIj/EMwXOoBiXalwL93fquzjEB22TzFHiTl2KjAqkOnvstNT2BEPLyTJ
xl7XPkVidNIoLS2/UQXjsHfpTADCy5xsnjM92k1Fbxy2PgHlY4XoAKFU1vmmgr/w
7q4RmAskwxRJRa6VOKw5T3+T+IaGdduk7rI6hXmYeolb3WqVw1YI1La2yoiXDkLH
lzZXgxruBjE9bFAfHjGfCCs+doWWT4Kpk9Qe1ZX1TUyOdREHQbfHRxMyDSi6D9CD
lVHxVQqD2TYjoBcTjRa7QpkXzEuG/N2iJupt2PmwR+MTwrtAFBuAn2lCeGGWpClx
Z/ZzQRmMXNMysWIMBH5zv2s1wIDAQABo1AwTjAdBgNVHQ4EFgQUhBjMhTTsvAyU
lC4IWZzHshBOCggwHwYDVR0jBBgwFoAUhBjMhTTsvAyUlC4IWZzHshBOCggwDAYD
VR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAAuBiMyGKIiX8LVBrJ9S/k9l0
YQIUP3P32x/LuHqVMaQOcvaTHFr+FjkmskZss0C1TS4udrRVOEcuzHrDiTJcYbfF
2dRTuDjWiA/qjQUuaInOe5Sb/qxXFFBNXB9d1rwDvZLrr+/IFuA6QQO/0wUfMj2m
/IBXXfW1JQngjzgHJFHVqVHccUxHcfzUxDw4X4iv2u1VoE2adQBrTTOBddlWHZO1
rDMYHAA13Sj0C6rVwSBjCCRVYuY24ELiaLNiPVYirzrwv9XyqSakqz6KXZMaABdh
yDTLcYBrSFcuIiJ2Dvhg+f/J4TgmJFcGGnrHzuQfFOoTl/UmzLXCFcEhsqhzVg==
-----END CERTIFICATE-----";
}

// Create certificate chain
function createCertificateChain($certificate) {
    // In a real implementation, this would include intermediate certificates
    return $certificate . "\n" . getIntermediateCertificate();
}

// Get intermediate certificate (mock)
function getIntermediateCertificate() {
    return "-----BEGIN CERTIFICATE-----
MIIEkjCCA3qgAwIBAgIQCgFBQgAAAVOFc2oLheynCDANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDb4xCzAJBgNVBAMT
GERTVCBSb290IENBIFgzMB4XDTE2MDMxNzE2NDA0NloXDTIxMDMxNzE2NDA0Nlow
SjELMAkGA1UEBhMCVVMxFjAUBgNVBAoTDUxldCdzIEVuY3J5cHQxIzAhBgNVBAMT
GkxldCdzIEVuY3J5cHQgQXV0aG9yaXR5IFgzMIIBIjANBgkqhkiG9w0BAQEFAAOC
AQ8AMIIBCgKCAQEAnNMM8FrlLke3cl03g7NoYzDq1zUmGSXhvb418XCSL7e4S0EF
q6meNQhY7LEqxGiHC6PjdeTm86dicbp5gWAf15Gan/PQeGdxyGkOlZHP/uaZ6WA8
SMx+yk13EiSdRxta67nsHjcAHJyse6cF6s5K671B5TaYucv9bTyWaN8jKkKQDIZ0
Z8h/pZq4UmEUEz9l6YKHy9v6Dlb2honzhT+Xhq+w3Brvaw2VFn3EK6BlspkENnWA
a6xK8xuQSXgvopZPKiAlKQTGdMDQMc2PMTiVFrqoM7hD8bEfwzB/onkxEz0tNvjj
/PIzark5McWvxI0NHWQWM6r6hCm21AvA2H3DkwIDAQABo4IBfTCCAXkwEgYDVR0T
AQH/BAgwBgEB/wIBADAOBgNVHQ8BAf8EBAMCAYYwfwYIKwYBBQUHAQEEczBxMDIG
CCsGAQUFBzABhiZodHRwOi8vaXNyZy50cnVzdGlkLm9jc3AuaWRlbnRydXN0LmNv
bTA7BggrBgEFBQcwAoYvaHR0cDovL2FwcHMuaWRlbnRydXN0LmNvbS9yb290cy9k
c3Ryb290Y2F4My5wN2MwHwYDVR0jBBgwFoAUxKexpHsscfrb4UuQdf/EFWCFiRAw
VAYDVR0gBE0wSzAIBgZngQwBAgEwPwYLKwYBBAGC3xMBAQEwMDAuBggrBgEFBQcC
ARYiaHR0cDovL2Nwcy5yb290LXgxLmxldHNlbmNyeXB0Lm9yZzA8BgNVHR8ENTAz
MDGgL6AthitodHRwOi8vY3JsLmlkZW50cnVzdC5jb20vRFNUUk9PVENBWDNDTE
wHhcNMjEwMzE3MTY0MDQ2WjANBgkqhkiG9w0BAQsFAAOCAQEAKmjnZ4kg5jjigwe
/SqoizgCx9E+pIti7kD2luZz6tNpPi5WuJ+q4TlzqgHwy/XqahWYTW3rvdMaNhq
4C5fnHRDS/PgqaGcWyH8QZE4ELdN1ByIVvIAQ3uvxw5LcUt9QuUNpwKAPvHdPd
7WVmKVSPQAEQDewD+y5wlKGZBkmhiMlzxydKT3XJNUG6jBVBaYVu8QjBbLHcv/7
M/cCUJH5+dZfZrlo1qQ2hLohKWFzI19+dQYSdHFzOxMQVklAJysOVdRqLTVmh69
L/a3NL/1207vQXALEXh2VkQFAH2w+/uKv0YmFfxieFMl+DeqVTLKXMTaS4SqVe
fVqhIWaUGb+2hM=
-----END CERTIFICATE-----";
}

// Generate serial number
function generateSerialNumber() {
    return strtoupper(bin2hex(random_bytes(8)));
}

// Generate demo SSL certificate for testing
function generateDemoSSLCertificate($domain, $email, $staging = true) {
    // Create realistic demo certificate
    $certificate = "-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjUwNzE5MDAwMDAwWhcNMjUxMDE3MDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuIj/EMwXOoBiXalwL93fquzjEB22TzFHiTl2KjAqkOnvstNT2BEPLyTJ
xl7XPkVidNIoLS2/UQXjsHfpTADCy5xsnjM92k1Fbxy2PgHlY4XoAKFU1vmmgr/w
7q4RmAskwxRJRa6VOKw5T3+T+IaGdduk7rI6hXmYeolb3WqVw1YI1La2yoiXDkLH
lzZXgxruBjE9bFAfHjGfCCs+doWWT4Kpk9Qe1ZX1TUyOdREHQbfHRxMyDSi6D9CD
lVHxVQqD2TYjoBcTjRa7QpkXzEuG/N2iJupt2PmwR+MTwrtAFBuAn2lCeGGWpClx
Z/ZzQRmMXNMysWIMBH5zv2s1wIDAQABo1AwTjAdBgNVHQ4EFgQUhBjMhTTsvAyU
lC4IWZzHshBOCggwHwYDVR0jBBgwFoAUhBjMhTTsvAyUlC4IWZzHshBOCggwDAYD
VR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAAuBiMyGKIiX8LVBrJ9S/k9l0
YQIUP3P32x/LuHqVMaQOcvaTHFr+FjkmskZss0C1TS4udrRVOEcuzHrDiTJcYbfF
2dRTuDjWiA/qjQUuaInOe5Sb/qxXFFBNXB9d1rwDvZLrr+/IFuA6QQO/0wUfMj2m
/IBXXfW1JQngjzgHJFHVqVHccUxHcfzUxDw4X4iv2u1VoE2adQBrTTOBddlWHZO1
rDMYHAA13Sj0C6rVwSBjCCRVYuY24ELiaLNiPVYirzrwv9XyqSakqz6KXZMaABdh
yDTLcYBrSFcuIiJ2Dvhg+f/J4TgmJFcGGnrHzuQfFOoTl/UmzLXCFcEhsqhzVg==
-----END CERTIFICATE-----";

    $privateKey = "-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC4iP8QzBc6gGJd
qXAv3d+q7OMQHbZPMUeJOXYqMCqQ6e+y01PYEQ8vJMnGXtc+RWJ00igtLb9RBeOw
d+lMAMLLnGyeMz3aTUVvHLY+AeVjhegAoVTW+aaCv/DurhGYCyTDFElFrpU4rDlP
f5P4hoZ126TusjqFeZh6iVvdapXDVgjUtrbaKCcOQseXNleDGu4GMT1sUB8eMZ8I
Kz52hZZPgqmT1B7VlfVNTI51EQdBt8dHEzINKLoP0IOVUfFVCoPZNiOgFxONFrtC
mRfMS4b83aIm6m3Y+bBH4xPCu0AUG4CfaUJ4YZakKXFn9nNBGYxc0zKxYgwEfnO/
azXAgMBAAECggEAFhgZzwI6Mi6w4r6ArtAcC2SJBFAXd7a1lQ5bc4xOfz+GlSA7
s+t2kAzXuQZdqZHjlVOADVNVmsKMrGypmBLADxFxeCWpAOVOaw8rKrdB2fwHxvyK
VwQlDkHFMSMEAT4r+fcaKlbADZmTgaRiGVfGQcEuZ4FMpq+VVrANxngjQQKBgQDm
OfLasNWGQYyXtwlBo1BM+dAXa4dDNd3uE4tcPFYFqRdlAgMBAAECggEAFhgZzwI6
Mi6w4r6ArtAcC2SJBFAXd7a1lQ5bc4xOfz+GlSA7s+t2kAzXuQZdqZHjlVOADVNV
msKMrGypmBLADxFxeCWpAOVOaw8rKrdB2fwHxvyKVwQlDkHFMSMEAT4r+fcaKlbA
DZmTgaRiGVfGQcEuZ4FMpq+VVrANxngjQQKBgQDmOfLasNWGQYyXtwlBo1BM+dAX
a4dDNd3uE4tcPFYFqRdlAgMBAAECggEAFhgZzwI6Mi6w4r6ArtAcC2SJBFAXd7a1
lQ5bc4xOfz+GlSA7s+t2kAzXuQZdqZHjlVOADVNVmsKMrGypmBLADxFxeCWpAOVO
aw8rKrdB2fwHxvyKVwQlDkHFMSMEAT4r+fcaKlbADZmTgaRiGVfGQcEuZ4FMpq+V
VrANxngjQQKBgQDmOfLasNWGQYyXtwlBo1BM+dAXa4dDNd3uE4tcPFYFqRdl
-----END PRIVATE KEY-----";

    $caBundle = "-----BEGIN CERTIFICATE-----
MIIEkjCCA3qgAwIBAgIQCgFBQgAAAVOFc2oLheynCDANBgkqhkiG9w0BAQsFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDb4xCzAJBgNVBAMT
GERTVCBSb290IENBIFgzMB4XDTE2MDMxNzE2NDA0NloXDTIxMDMxNzE2NDA0Nlow
SjELMAkGA1UEBhMCVVMxFjAUBgNVBAoTDUxldCdzIEVuY3J5cHQxIzAhBgNVBAMT
GkxldCdzIEVuY3J5cHQgQXV0aG9yaXR5IFgzMIIBIjANBgkqhkiG9w0BAQEFAAOC
AQ8AMIIBCgKCAQEAnNMM8FrlLke3cl03g7NoYzDq1zUmGSXhvb418XCSL7e4S0EF
q6meNQhY7LEqxGiHC6PjdeTm86dicbp5gWAf15Gan/PQeGdxyGkOlZHP/uaZ6WA8
SMx+yk13EiSdRxta67nsHjcAHJyse6cF6s5K671B5TaYucv9bTyWaN8jKkKQDIZ0
Z8h/pZq4UmEUEz9l6YKHy9v6Dlb2honzhT+Xhq+w3Brvaw2VFn3EK6BlspkENnWA
a6xK8xuQSXgvopZPKiAlKQTGdMDQMc2PMTiVFrqoM7hD8bEfwzB/onkxEz0tNvjj
/PIzark5McWvxI0NHWQWM6r6hCm21AvA2H3DkwIDAQABo4IBfTCCAXkwEgYDVR0T
AQH/BAgwBgEB/wIBADAOBgNVHQ8BAf8EBAMCAYYwfwYIKwYBBQUHAQEEczBxMDIG
CCsGAQUFBzABhiZodHRwOi8vaXNyZy50cnVzdGlkLm9jc3AuaWRlbnRydXN0LmNv
bTA7BggrBgEFBQcwAoYvaHR0cDovL2FwcHMuaWRlbnRydXN0LmNvbS9yb290cy9k
c3Ryb290Y2F4My5wN2MwHwYDVR0jBBgwFoAUxKexpHsscfrb4UuQdf/EFWCFiRAw
VAYDVR0gBE0wSzAIBgZngQwBAgEwPwYLKwYBBAGC3xMBAQEwMDAuBggrBgEFBQcC
ARYiaHR0cDovL2Nwcy5yb290LXgxLmxldHNlbmNyeXB0Lm9yZzA8BgNVHR8ENTAz
MDGgL6AthitodHRwOi8vY3JsLmlkZW50cnVzdC5jb20vRFNUUk9PVENBWDNDTE
wHhcNMjEwMzE3MTY0MDQ2WjANBgkqhkiG9w0BAQsFAAOCAQEAKmjnZ4kg5jjigwe
/SqoizgCx9E+pIti7kD2luZz6tNpPi5WuJ+q4TlzqgHwy/XqahWYTW3rvdMaNhq
4C5fnHRDS/PgqaGcWyH8QZE4ELdN1ByIVvIAQ3uvxw5LcUt9QuUNpwKAPvHdPd
7WVmKVSPQAEQDewD+y5wlKGZBkmhiMlzxydKT3XJNUG6jBVBaYVu8QjBbLHcv/7
M/cCUJH5+dZfZrlo1qQ2hLohKWFzI19+dQYSdHFzOxMQVklAJysOVdRqLTVmh69
L/a3NL/1207vQXALEXh2VkQFAH2w+/uKv0YmFfxieFMl+DeqVTLKXMTaS4SqVe
fVqhIWaUGb+2hM=
-----END CERTIFICATE-----";

    return [
        'success' => true,
        'certificate' => $certificate,
        'privateKey' => $privateKey,
        'caBundle' => $caBundle,
        'fullChain' => $certificate . "\n" . $caBundle,
        'domain' => $domain,
        'issuer' => $staging ? 'Demo SSL Generator (Staging)' : 'Demo SSL Generator',
        'validFrom' => date('Y-m-d H:i:s'),
        'validTo' => date('Y-m-d H:i:s', strtotime('+90 days')),
        'demo_mode' => true
    ];
}

// Log SSL generation
function logSSLGeneration($domain, $email, $status = 'SUCCESS') {
    $logEntry = date('Y-m-d H:i:s') . " - SSL generation for domain: $domain, email: $email, status: $status\n";
    file_put_contents(__DIR__ . '/ssl_generation.log', $logEntry, FILE_APPEND | LOCK_EX);
}
?>
