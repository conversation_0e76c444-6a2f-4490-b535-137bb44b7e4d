<?php
/**
 * Database Configuration
 * OLX Marketplace Backend
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'olx_marketplace';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

// Database configuration constants
define('DB_HOST', 'localhost');
define('DB_NAME', 'olx_marketplace');
define('DB_USER', 'root');
define('DB_PASS', '');

// Site configuration
define('SITE_URL', 'http://localhost/olx-marketplace');
define('UPLOAD_PATH', '../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// Email configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'OLX Marketplace');

// Security settings
define('JWT_SECRET', 'your-secret-key-here-change-this');
define('PASSWORD_SALT', 'your-password-salt-here');
define('SESSION_TIMEOUT', 3600); // 1 hour

// Pagination settings
define('PRODUCTS_PER_PAGE', 12);
define('ADMIN_ITEMS_PER_PAGE', 20);

// File upload settings
define('PRODUCT_IMAGE_PATH', 'uploads/products/');
define('USER_AVATAR_PATH', 'uploads/avatars/');
define('MAX_PRODUCT_IMAGES', 5);

// Categories configuration
$default_categories = [
    'mobiles' => ['name' => 'Mobiles', 'icon' => 'fas fa-mobile-alt'],
    'cars' => ['name' => 'Cars', 'icon' => 'fas fa-car'],
    'bikes' => ['name' => 'Bikes', 'icon' => 'fas fa-motorcycle'],
    'houses' => ['name' => 'Houses', 'icon' => 'fas fa-home'],
    'electronics' => ['name' => 'Electronics', 'icon' => 'fas fa-laptop'],
    'fashion' => ['name' => 'Fashion', 'icon' => 'fas fa-tshirt'],
    'furniture' => ['name' => 'Furniture', 'icon' => 'fas fa-couch'],
    'jobs' => ['name' => 'Jobs', 'icon' => 'fas fa-briefcase']
];

// Pakistani cities
$pakistani_cities = [
    'karachi' => 'Karachi',
    'lahore' => 'Lahore',
    'islamabad' => 'Islamabad',
    'rawalpindi' => 'Rawalpindi',
    'faisalabad' => 'Faisalabad',
    'multan' => 'Multan',
    'peshawar' => 'Peshawar',
    'quetta' => 'Quetta',
    'sialkot' => 'Sialkot',
    'gujranwala' => 'Gujranwala',
    'hyderabad' => 'Hyderabad',
    'bahawalpur' => 'Bahawalpur'
];

// Error messages
$error_messages = [
    'invalid_login' => 'Invalid email or password',
    'email_exists' => 'Email already exists',
    'weak_password' => 'Password must be at least 8 characters',
    'invalid_email' => 'Invalid email format',
    'required_field' => 'This field is required',
    'file_too_large' => 'File size too large',
    'invalid_file_type' => 'Invalid file type',
    'upload_failed' => 'File upload failed',
    'unauthorized' => 'Unauthorized access',
    'product_not_found' => 'Product not found',
    'user_not_found' => 'User not found'
];

// Success messages
$success_messages = [
    'registration_success' => 'Registration successful! Please login.',
    'login_success' => 'Login successful',
    'product_added' => 'Product added successfully',
    'product_updated' => 'Product updated successfully',
    'product_deleted' => 'Product deleted successfully',
    'profile_updated' => 'Profile updated successfully',
    'password_changed' => 'Password changed successfully',
    'email_sent' => 'Email sent successfully'
];

// Helper functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generate_token($length = 32) {
    return bin2hex(random_bytes($length));
}

function format_price($price) {
    return 'Rs ' . number_format($price);
}

function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validate_phone($phone) {
    // Pakistani phone number validation
    $pattern = '/^(\+92|0)?[0-9]{10}$/';
    return preg_match($pattern, $phone);
}

function generate_slug($string) {
    $slug = strtolower($string);
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/[\s-]+/', '-', $slug);
    $slug = trim($slug, '-');
    return $slug;
}

function resize_image($source, $destination, $max_width = 800, $max_height = 600) {
    $info = getimagesize($source);
    $width = $info[0];
    $height = $info[1];
    $mime = $info['mime'];
    
    // Calculate new dimensions
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = $width * $ratio;
    $new_height = $height * $ratio;
    
    // Create new image
    $new_image = imagecreatetruecolor($new_width, $new_height);
    
    switch ($mime) {
        case 'image/jpeg':
            $source_image = imagecreatefromjpeg($source);
            break;
        case 'image/png':
            $source_image = imagecreatefrompng($source);
            imagealphablending($new_image, false);
            imagesavealpha($new_image, true);
            break;
        case 'image/gif':
            $source_image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    imagecopyresampled($new_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
    
    switch ($mime) {
        case 'image/jpeg':
            imagejpeg($new_image, $destination, 85);
            break;
        case 'image/png':
            imagepng($new_image, $destination);
            break;
        case 'image/gif':
            imagegif($new_image, $destination);
            break;
    }
    
    imagedestroy($new_image);
    imagedestroy($source_image);
    
    return true;
}

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set timezone
date_default_timezone_set('Asia/Karachi');
?>
