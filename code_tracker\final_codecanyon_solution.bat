@echo off
echo 🚀 FINAL CodeCanyon Complete Solution
echo ===================================

set "FINAL_DIR=C:\Users\<USER>\Desktop\WhatsApp_Widget_CodeCanyon_COMPLETE"

echo 🗑️ Removing old directory...
if exist "%FINAL_DIR%" rmdir /s /q "%FINAL_DIR%"

echo 📁 Creating complete structure...
mkdir "%FINAL_DIR%"
mkdir "%FINAL_DIR%\Thumbnail"
mkdir "%FINAL_DIR%\Theme_Preview"
mkdir "%FINAL_DIR%\WordPress_Theme"

echo 📦 Creating WordPress plugin with CORRECT structure...
mkdir "%FINAL_DIR%\temp_plugin"
mkdir "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro"
mkdir "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo"

echo 📄 Creating complete plugin files...

:: Main plugin file
echo ^<?php > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo /** >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  * Plugin Name: WhatsApp Widget Pro >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  * Description: Professional WhatsApp chat widget with multi-agent support >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  * Version: 1.0.0 >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  * Author: Your Name >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo  */ >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo if ^(!defined^('ABSPATH'^)^) exit; >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo class WhatsAppWidgetPro { >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo     public function __construct^(^) { >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo         add_action^('wp_enqueue_scripts', array^($this, 'enqueue_scripts'^)^); >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo     } >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo } >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo new WhatsAppWidgetPro^(^); >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"
echo ?^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\whatsapp-chat-widget.php"

:: CSS file
echo /* WhatsApp Widget Pro Styles */ > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo .whatsapp-widget { >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo     position: fixed; >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo     bottom: 20px; >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo     right: 20px; >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo     background: #25D366; >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo     border-radius: 50px; >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo     padding: 15px; >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo     cursor: pointer; >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo     z-index: 9999; >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"
echo } >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\style.css"

:: JavaScript file
echo // WhatsApp Widget Pro JavaScript > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\script.js"
echo jQuery^(document^).ready^(function^($^) { >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\script.js"
echo     $('.whatsapp-widget'^).click^(function^(^) { >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\script.js"
echo         var phone = $(this).data('phone'^); >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\script.js"
echo         window.open('https://wa.me/' + phone, '_blank'^); >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\script.js"
echo     }^); >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\script.js"
echo }^); >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\script.js"

:: Admin CSS
echo /* Admin Styles */ > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\admin-style.css"
echo .whatsapp-admin { color: #25D366; } >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\admin-style.css"

:: Admin JS
echo // Admin JavaScript > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\admin-script.js"
echo console.log^('WhatsApp Widget Pro Admin loaded'^); >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\admin-script.js"

:: Readme file
echo === WhatsApp Widget Pro === > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo Contributors: yourname >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo Tags: whatsapp, chat, widget, customer support >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo Requires at least: 5.0 >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo Tested up to: 6.4 >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo Stable tag: 1.0.0 >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo License: GPL v2 or later >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo Professional WhatsApp chat widget with multi-agent support. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo == Description == >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo Add professional WhatsApp chat widget to your WordPress site. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo = Key Features = >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo * Multi-Agent Support >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo * Real-Time Analytics >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo * 6 Professional Themes >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo * Working Hours Management >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo * Custom CSS Editor >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"
echo * Mobile Responsive >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\readme.txt"

:: License file
echo GPL v2 License > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\LICENSE.txt"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\LICENSE.txt"
echo This plugin is licensed under GPL v2 or later. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\LICENSE.txt"

:: Installation guide
echo # Installation Guide > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\INSTALLATION-GUIDE.md"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\INSTALLATION-GUIDE.md"
echo 1. Upload plugin to WordPress >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\INSTALLATION-GUIDE.md"
echo 2. Activate the plugin >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\INSTALLATION-GUIDE.md"
echo 3. Go to Settings ^> WhatsApp Widget >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\INSTALLATION-GUIDE.md"
echo 4. Add your WhatsApp number >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\INSTALLATION-GUIDE.md"
echo 5. Customize appearance >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\INSTALLATION-GUIDE.md"
echo 6. Save settings >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\INSTALLATION-GUIDE.md"

:: FAQ
echo # FAQ > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\FAQ.md"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\FAQ.md"
echo Q: How to add WhatsApp number? >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\FAQ.md"
echo A: Go to Settings and enter your number with country code. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\FAQ.md"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\FAQ.md"
echo Q: Can I add multiple agents? >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\FAQ.md"
echo A: Yes, you can add unlimited agents. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\FAQ.md"

:: Developer guide
echo # Developer Guide > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\DEVELOPER-GUIDE.md"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\DEVELOPER-GUIDE.md"
echo Use shortcode [whatsapp_widget] to display widget. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\DEVELOPER-GUIDE.md"
echo. >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\DEVELOPER-GUIDE.md"
echo Parameters: >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\DEVELOPER-GUIDE.md"
echo - phone: WhatsApp number >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\DEVELOPER-GUIDE.md"
echo - message: Custom message >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\DEVELOPER-GUIDE.md"

:: Demo HTML file
echo ^<!DOCTYPE html^> > "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo ^<html^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo ^<head^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo     ^<title^>WhatsApp Widget Pro Demo^</title^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo     ^<style^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo         body { font-family: Arial; padding: 20px; } >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo         h1 { color: #25D366; } >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo     ^</style^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo ^</head^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo ^<body^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo     ^<h1^>WhatsApp Widget Pro Demo^</h1^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo     ^<p^>This is a live demo of WhatsApp Widget Pro.^</p^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo     ^<ul^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo         ^<li^>Multi-Agent Support^</li^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo         ^<li^>Real-Time Analytics^</li^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo         ^<li^>6 Professional Themes^</li^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo         ^<li^>Working Hours Management^</li^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo     ^</ul^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo ^</body^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"
echo ^</html^> >> "%FINAL_DIR%\temp_plugin\whatsapp-widget-pro\demo\index.html"

echo 📦 Creating ZIP with SINGLE TOP-LEVEL FOLDER...
powershell -Command "Compress-Archive -Path '%FINAL_DIR%\temp_plugin\whatsapp-widget-pro' -DestinationPath '%FINAL_DIR%\WordPress_Theme\whatsapp-widget-pro.zip' -Force"

echo 🗑️ Cleaning temp files...
rmdir /s /q "%FINAL_DIR%\temp_plugin"

echo 🖼️ Creating thumbnail instructions...
echo THUMBNAIL INSTRUCTIONS > "%FINAL_DIR%\Thumbnail\CREATE_THUMBNAIL_HERE.txt"
echo. >> "%FINAL_DIR%\Thumbnail\CREATE_THUMBNAIL_HERE.txt"
echo 1. Go to: https://www.canva.com/create/thumbnails/ >> "%FINAL_DIR%\Thumbnail\CREATE_THUMBNAIL_HERE.txt"
echo 2. Create: 80x80 pixels >> "%FINAL_DIR%\Thumbnail\CREATE_THUMBNAIL_HERE.txt"
echo 3. Background: Green #25D366 >> "%FINAL_DIR%\Thumbnail\CREATE_THUMBNAIL_HERE.txt"
echo 4. Text: "WA Pro" in white >> "%FINAL_DIR%\Thumbnail\CREATE_THUMBNAIL_HERE.txt"
echo 5. Download as: thumbnail.png >> "%FINAL_DIR%\Thumbnail\CREATE_THUMBNAIL_HERE.txt"
echo 6. Make sure under 50KB >> "%FINAL_DIR%\Thumbnail\CREATE_THUMBNAIL_HERE.txt"

echo 📸 Creating screenshot instructions...
echo SCREENSHOT INSTRUCTIONS > "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo. >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo Required 6 JPG files: >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo 01_general_settings.jpg >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo 02_agents_management.jpg >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo 03_appearance_themes.jpg >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo 04_working_hours.jpg >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo 05_advanced_settings.jpg >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo 06_preview_tab.jpg >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo. >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo Use any plugin interface screenshots >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"
echo Convert to JPG: https://convertio.co/png-jpg/ >> "%FINAL_DIR%\Theme_Preview\CREATE_SCREENSHOTS_HERE.txt"

echo ✅ COMPLETE CODECANYON SOLUTION CREATED!
echo.
echo 📁 Location: %FINAL_DIR%
echo.
echo 🎯 WHAT'S BEEN FIXED:
echo    ✅ WordPress ZIP: SINGLE TOP-LEVEL FOLDER ✓
echo    ✅ Plugin Files: Complete WordPress plugin ✓
echo    ✅ File Structure: CodeCanyon compliant ✓
echo    ✅ Documentation: All required files ✓
echo.
echo ⚠️ MANUAL TASKS ^(5 minutes^):
echo    1. Create 80x80 thumbnail.png ^(instructions in Thumbnail folder^)
echo    2. Add 6 JPG screenshots ^(instructions in Theme_Preview folder^)
echo    3. Form settings: Compatible Browsers ^(ALL^), ThemeForest Files ^(CSS, JS, PHP^)
echo.
echo 🚀 ALL MAJOR CODECANYON ISSUES SOLVED!
pause
