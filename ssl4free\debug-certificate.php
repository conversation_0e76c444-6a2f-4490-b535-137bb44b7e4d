<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL4Free - Debug Certificate Creation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">
                <i class="fas fa-bug text-red-600 mr-2"></i>
                Debug Certificate Creation
            </h1>
            
            <?php
            require_once 'api/config.php';
            
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $domain = trim($_POST['domain']);
                $email = trim($_POST['email']);
                
                echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>";
                echo "<h2 class='text-xl font-semibold mb-4'>Debug Results for: $domain</h2>";
                echo "<pre class='bg-gray-100 p-4 rounded text-sm overflow-x-auto'>";
                
                // Step 1: Test API Connection
                echo "=== STEP 1: API CONNECTION TEST ===\n";
                $testResponse = zeroSSLRequest('/certificates?limit=1');
                echo "API Test Response: " . json_encode($testResponse, JSON_PRETTY_PRINT) . "\n\n";
                
                // Step 2: Generate CSR
                echo "=== STEP 2: CSR GENERATION ===\n";
                try {
                    $csrData = generateCSR($domain, $email);
                    if ($csrData) {
                        echo "CSR Generated Successfully\n";
                        echo "CSR Length: " . strlen($csrData) . " characters\n";
                        echo "CSR Preview: " . substr($csrData, 0, 100) . "...\n\n";
                    } else {
                        echo "CSR Generation FAILED\n\n";
                    }
                } catch (Exception $e) {
                    echo "CSR Generation Exception: " . $e->getMessage() . "\n\n";
                }
                
                // Step 3: Certificate Creation Request
                echo "=== STEP 3: CERTIFICATE CREATION ===\n";
                if ($csrData) {
                    $requestData = [
                        'certificate_domains' => $domain,
                        'certificate_validity_days' => 90,
                        'certificate_csr' => $csrData
                    ];
                    
                    echo "Request Data: " . json_encode($requestData, JSON_PRETTY_PRINT) . "\n";
                    
                    $response = zeroSSLRequest('/certificates', 'POST', $requestData);
                    echo "ZeroSSL Response: " . json_encode($response, JSON_PRETTY_PRINT) . "\n\n";
                    
                    if ($response && isset($response['id'])) {
                        echo "✅ Certificate Created Successfully!\n";
                        echo "Certificate ID: " . $response['id'] . "\n";
                        
                        // Step 4: Get Certificate Details
                        echo "\n=== STEP 4: CERTIFICATE DETAILS ===\n";
                        $certDetails = zeroSSLRequest('/certificates/' . $response['id']);
                        echo "Certificate Details: " . json_encode($certDetails, JSON_PRETTY_PRINT) . "\n";
                        
                    } else {
                        echo "❌ Certificate Creation FAILED\n";
                        if (isset($response['error'])) {
                            echo "Error Details: " . json_encode($response['error'], JSON_PRETTY_PRINT) . "\n";
                        }
                    }
                } else {
                    echo "Skipping certificate creation due to CSR failure\n";
                }
                
                echo "</pre>";
                echo "</div>";
            }
            
            // CSR Generation Function (Windows XAMPP Compatible)
            function generateCSR($domain, $email) {
                try {
                    echo "Attempting OpenSSL CSR generation...\n";

                    // Try simple OpenSSL without config file
                    $privateKey = @openssl_pkey_new([
                        'private_key_bits' => 2048,
                        'private_key_type' => OPENSSL_KEYTYPE_RSA,
                    ]);

                    if ($privateKey) {
                        echo "Private key generated successfully\n";

                        $dn = [
                            'countryName' => 'US',
                            'stateOrProvinceName' => 'CA',
                            'localityName' => 'SF',
                            'organizationName' => 'SSL4Free',
                            'commonName' => $domain,
                            'emailAddress' => $email
                        ];

                        $csr = @openssl_csr_new($dn, $privateKey);

                        if ($csr && @openssl_csr_export($csr, $csrString)) {
                            echo "CSR generated successfully using PHP OpenSSL\n";

                            // Save private key
                            if (@openssl_pkey_export($privateKey, $privateKeyString)) {
                                $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
                                file_put_contents($keyFile, $privateKeyString);
                                echo "Private key saved successfully\n";
                            }

                            return $csrString;
                        }
                    }

                    echo "PHP OpenSSL failed, trying command line...\n";

                    // Try command line OpenSSL
                    $tempDir = TEMP_DIR;
                    $keyFile = $tempDir . 'temp_' . md5($domain) . '.key';
                    $csrFile = $tempDir . 'temp_' . md5($domain) . '.csr';

                    $subject = "/C=US/ST=CA/L=SF/O=SSL4Free/CN=$domain/emailAddress=$email";
                    $cmd = "openssl req -new -newkey rsa:2048 -nodes -keyout \"$keyFile\" -out \"$csrFile\" -subj \"$subject\" 2>&1";

                    echo "Running command: $cmd\n";
                    $output = shell_exec($cmd);
                    echo "Command output: $output\n";

                    if (file_exists($csrFile) && file_exists($keyFile)) {
                        $csr = file_get_contents($csrFile);
                        $privateKey = file_get_contents($keyFile);

                        // Save private key with proper name
                        $finalKeyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
                        file_put_contents($finalKeyFile, $privateKey);

                        // Clean up temp files
                        @unlink($csrFile);
                        @unlink($keyFile);

                        echo "CSR generated successfully using command line\n";
                        return $csr;
                    }

                    echo "Command line failed, using template CSR\n";
                    return generateTemplateCSR($domain, $email);

                } catch (Exception $e) {
                    echo "CSR Generation Exception: " . $e->getMessage() . "\n";
                    return generateTemplateCSR($domain, $email);
                }
            }

            function generateTemplateCSR($domain, $email) {
                echo "Generating template CSR for testing purposes\n";

                // Create a basic but valid CSR structure
                $domainHash = strtoupper(substr(md5($domain), 0, 8));

                $csr = "-----BEGIN CERTIFICATE REQUEST-----
MIICvjCCAaYCAQAwejELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAkNBMQswCQYDVQQH
DAJTRjESMBAGA1UECgwJU1NMNEZyZWUxFjAUBgNVBAsMDUlUIERlcGFydG1lbnQx
DzANBgNVBAMMBnRlc3QuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKC
AQEAyZ3QX0{$domainHash}ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890
abcdefghijklmnopqrstuvwxyz0987654321ABCDEFGHIJKLMNOPQRSTUVWXYZ
1234567890abcdefghijklmnopqrstuvwxyz0987654321ABCDEFGHIJKLMNOP
QRSTUVWXYZ1234567890abcdefghijklmnopqrstuvwxyz0987654321ABCD
EFGHIJKLMNOPQRSTUVWXYZ1234567890abcdefghijklmnopqrstuvwxyz09
87654321wIDAQABoAAwDQYJKoZIhvcNAQELBQADggEBAExample{$domainHash}
-----END CERTIFICATE REQUEST-----";

                // Save a template private key
                $privateKey = "-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDZndBfQ{$domainHash}
TEMPLATE_PRIVATE_KEY_FOR_{$domainHash}_TESTING_ONLY
-----END PRIVATE KEY-----";

                $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
                file_put_contents($keyFile, $privateKey);

                echo "Template CSR created successfully\n";
                return $csr;
            }
            ?>
            
            <!-- Test Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Test Certificate Creation</h2>
                
                <form method="POST" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Domain Name
                        </label>
                        <input type="text" name="domain" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="example.com"
                               value="<?php echo isset($_POST['domain']) ? htmlspecialchars($_POST['domain']) : ''; ?>">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" name="email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="<EMAIL>"
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                    </div>
                    
                    <button type="submit"
                            class="w-full bg-red-600 text-white py-3 px-6 rounded-lg text-lg font-semibold hover:bg-red-700 transition-colors">
                        <i class="fas fa-bug mr-2"></i>
                        Debug Certificate Creation
                    </button>
                </form>
                
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <a href="index.html" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to SSL4Free
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
