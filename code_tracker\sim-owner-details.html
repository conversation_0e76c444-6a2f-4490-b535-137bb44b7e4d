<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIM Owner Details - Find Mobile Number Owner Information | Pakistan</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Find SIM owner details for any Pakistani mobile number. Get owner name, CNIC, address, and registration information instantly.">
    <meta name="keywords" content="sim owner details, mobile number owner, pakistan sim data, find sim owner, mobile number lookup">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/new-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">
                    <i class="fas fa-database" style="font-size: 2rem; color: var(--primary-color);"></i>
                    <span class="logo-text">PakSim</span>
                </a>

                <ul class="nav-menu">
                    <li class="nav-item"><a href="index.html" class="nav-link">Homepage</a></li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link" style="color: var(--primary-color);">SIM DATA <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="sim-owner-details.html">SIM Owner Details</a></li>
                            <li><a href="sim-data-online.html">SIM DATA ONLINE</a></li>
                            <li><a href="live-tracker.html">Live Tracker</a></li>
                        </ul>
                    </li>
                    <li class="nav-item"><a href="about.html" class="nav-link">About Us</a></li>
                    <li class="nav-item"><a href="faq.html" class="nav-link">FAQ</a></li>
                    <li class="nav-item"><a href="blog.html" class="nav-link">Blog</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-link">Contact Us</a></li>
                </ul>
                
                <div class="nav-actions">
                    <a href="contact.html" class="btn btn-primary">Get Started</a>
                </div>
            </nav>
        </div>
    </header>

    <!-- Page Header -->
    <section class="page-header" style="background: var(--gradient-primary); color: #fff; padding: 60px 0; text-align: center;">
        <div class="container">
            <h1 style="font-size: 3rem; margin-bottom: 20px;">SIM Owner Details Lookup</h1>
            <p style="font-size: 1.2rem; opacity: 0.9;">Find complete owner information for any Pakistani mobile number</p>
        </div>
    </section>

    <!-- Search Section -->
    <section style="padding: 60px 0; background: #f8f9fa;">
        <div class="container">
            <div style="max-width: 800px; margin: 0 auto;">
                <div class="sim-search-form">
                    <h3><i class="fas fa-search"></i> Search SIM Owner Details</h3>
                    <form class="search-form" id="simSearchForm">
                        <div class="form-group">
                            <label for="searchInput">Mobile Number</label>
                            <input type="text" id="searchInput" name="searchInput" placeholder="Enter mobile number (e.g., 03001234567)" required>
                        </div>
                        
                        <button type="submit" class="btn btn-secondary btn-large">
                            <i class="fas fa-search"></i> Find Owner Details
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Results -->
    <section class="search-results" id="searchResults" style="display: none;">
        <div class="container">
            <div class="results-header">
                <h2><i class="fas fa-user"></i> Owner Details</h2>
                <button class="btn btn-primary" onclick="clearResults()">
                    <i class="fas fa-times"></i> Clear Results
                </button>
            </div>
            <div class="results-content" id="resultsContent">
                <!-- Results will be displayed here -->
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section style="padding: 80px 0;">
        <div class="container">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5rem; color: var(--dark-color); margin-bottom: 20px;">What You Get</h2>
                <p style="font-size: 1.1rem; color: #666;">Comprehensive SIM owner information at your fingertips</p>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Owner Name</h3>
                    <p style="color: #666; line-height: 1.6;">Get the complete name of the SIM card owner as registered with the telecom operator.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">CNIC Number</h3>
                    <p style="color: #666; line-height: 1.6;">Access the CNIC number associated with the mobile number for identity verification.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Address Details</h3>
                    <p style="color: #666; line-height: 1.6;">Find the registered address information including city and area details.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Network Info</h3>
                    <p style="color: #666; line-height: 1.6;">Identify the telecom network (Jazz, Telenor, Zong, Ufone) and connection type.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Registration Date</h3>
                    <p style="color: #666; line-height: 1.6;">View when the SIM card was first registered and activated.</p>
                </div>
                
                <div style="background: #fff; padding: 30px; border-radius: var(--border-radius); box-shadow: var(--box-shadow); text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Status Check</h3>
                    <p style="color: #666; line-height: 1.6;">Check if the SIM is active, inactive, or blocked by the operator.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section style="padding: 80px 0; background: #f8f9fa;">
        <div class="container">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5rem; color: var(--dark-color); margin-bottom: 20px;">How It Works</h2>
                <p style="font-size: 1.1rem; color: #666;">Simple steps to find SIM owner details</p>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px;">
                <div style="text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 1.5rem; font-weight: 700;">
                        1
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Enter Mobile Number</h3>
                    <p style="color: #666; line-height: 1.6;">Type the Pakistani mobile number you want to search in the input field above.</p>
                </div>
                
                <div style="text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 1.5rem; font-weight: 700;">
                        2
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Click Search</h3>
                    <p style="color: #666; line-height: 1.6;">Press the search button and our system will query the database for owner details.</p>
                </div>
                
                <div style="text-align: center;">
                    <div style="background: var(--primary-color); color: #fff; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 1.5rem; font-weight: 700;">
                        3
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Get Results</h3>
                    <p style="color: #666; line-height: 1.6;">View comprehensive owner details including name, CNIC, address, and network information.</p>
                </div>
                
                <div style="text-align: center;">
                    <div style="background: var(--secondary-color); color: #fff; width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 1.5rem; font-weight: 700;">
                        4
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px;">Verify Information</h3>
                    <p style="color: #666; line-height: 1.6;">Use the information for verification purposes or contact our support for premium services.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section style="padding: 80px 0; background: var(--gradient-primary); color: #fff; text-align: center;">
        <div class="container">
            <h2 style="font-size: 2.5rem; margin-bottom: 20px;">Need More Detailed Information?</h2>
            <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 30px;">Contact us for premium services including live tracking, detailed reports, and bulk lookups.</p>
            <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
                <a href="https://wa.me/+923001234567" class="btn btn-secondary" style="background: #fff; color: var(--primary-color);">
                    <i class="fab fa-whatsapp"></i> WhatsApp Support
                </a>
                <a href="contact.html" class="btn" style="background: rgba(255, 255, 255, 0.2); color: #fff; border: 2px solid #fff;">
                    <i class="fas fa-envelope"></i> Contact Us
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" style="background: var(--dark-color); color: #fff; padding: 50px 0 20px;">
        <div class="container">
            <div class="footer-content" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 30px;">
                <div class="footer-section">
                    <h3 style="margin-bottom: 20px; color: var(--primary-color);">PAKSIM</h3>
                    <p style="opacity: 0.8; line-height: 1.6; margin-bottom: 20px;">SIM Database | SIM Owner Details | CNIC SIM Check | Pak SIM Data | Live Tracker</p>
                </div>
                
                <div class="footer-section">
                    <h4 style="margin-bottom: 20px;">QUICK LINKS</h4>
                    <ul style="list-style: none;">
                        <li style="margin-bottom: 10px;"><a href="index.html" style="color: #ccc; text-decoration: none;">Homepage</a></li>
                        <li style="margin-bottom: 10px;"><a href="sim-owner-details.html" style="color: #ccc; text-decoration: none;">SIM Owner Details</a></li>
                        <li style="margin-bottom: 10px;"><a href="sim-data-online.html" style="color: #ccc; text-decoration: none;">CNIC SIM Check</a></li>
                        <li style="margin-bottom: 10px;"><a href="live-tracker.html" style="color: #ccc; text-decoration: none;">Live Tracker</a></li>
                        <li style="margin-bottom: 10px;"><a href="about.html" style="color: #ccc; text-decoration: none;">About Us</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 style="margin-bottom: 20px;">CONTACT US</h4>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-phone" style="color: var(--primary-color); margin-right: 10px;"></i>
                        <span>+92 300 1234567</span>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <i class="fas fa-envelope" style="color: var(--primary-color); margin-right: 10px;"></i>
                        <span><EMAIL></span>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom" style="border-top: 1px solid #444; padding-top: 20px; text-align: center;">
                <p style="opacity: 0.8;">© 2025 PakSim – SIM Database Online & CNIC SIM Information System for Pakistan. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/real-scraper.js"></script>
    <script>
        // SIM Owner Details specific functionality
        class SIMOwnerDetailsApp {
            constructor() {
                this.searchForm = document.getElementById('simSearchForm');
                this.searchResults = document.getElementById('searchResults');
                this.resultsContent = document.getElementById('resultsContent');
                this.realScraper = window.RealSIMScraper;
                
                this.init();
            }
            
            init() {
                if (this.searchForm) {
                    this.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
                }
                
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.addEventListener('input', () => this.validateInput());
                }
            }
            
            validateInput() {
                const searchInput = document.getElementById('searchInput');
                const value = searchInput.value.trim();
                
                // Remove any non-digits and format
                const cleaned = value.replace(/\D/g, '');
                if (cleaned.length <= 11) {
                    searchInput.value = cleaned;
                }
            }
            
            async handleSearch(e) {
                e.preventDefault();
                
                const searchInput = document.getElementById('searchInput').value.trim();
                
                if (!searchInput) {
                    this.showError('Please enter a mobile number');
                    return;
                }
                
                // Validate mobile number format
                if (!this.validateMobile(searchInput)) {
                    this.showError('Please enter a valid Pakistani mobile number (11 digits starting with 03)');
                    return;
                }
                
                // Show loading and perform search
                this.showLoading();
                
                try {
                    const result = await this.realScraper.scrapeSIMData(searchInput);
                    this.displayResults(searchInput, result);
                } catch (error) {
                    console.error('Search error:', error);
                    this.showError('Search failed. Please try again.');
                }
            }
            
            validateMobile(mobile) {
                const mobileRegex = /^03\d{9}$/;
                return mobileRegex.test(mobile);
            }
            
            showLoading() {
                this.searchResults.style.display = 'block';
                this.resultsContent.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-spinner"></i>
                        <p>Searching SIM owner details...</p>
                        <small>Fetching owner information from database</small>
                    </div>
                `;
                this.searchResults.scrollIntoView({ behavior: 'smooth' });
            }
            
            displayResults(mobile, result) {
                if (!result || !result.success) {
                    this.showError('No owner details found for this mobile number');
                    return;
                }
                
                const data = result.data;
                const source = result.source || 'Database';
                
                this.resultsContent.innerHTML = `
                    <div class="result-card">
                        <h3><i class="fas fa-user"></i> SIM Owner Details</h3>
                        <div style="text-align: right; margin-bottom: 1rem; font-size: 0.8rem; color: #666;">
                            💾 Source: ${source}
                        </div>
                        
                        <div class="result-item">
                            <span class="result-label">Mobile Number:</span>
                            <span class="result-value">${mobile}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Owner Name:</span>
                            <span class="result-value">${data.owner}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">CNIC:</span>
                            <span class="result-value">${data.cnic}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Address:</span>
                            <span class="result-value">${data.address}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Network:</span>
                            <span class="result-value">${data.network}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Status:</span>
                            <span class="result-value">${data.status}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Registration Date:</span>
                            <span class="result-value">${data.registrationDate}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Type:</span>
                            <span class="result-value">${data.type}</span>
                        </div>
                        
                        <div style="padding: 1rem; border-radius: var(--border-radius); margin-top: 1rem; font-size: 0.9rem; background: #f8f9fa;">
                            <i class="fas fa-info-circle"></i> <strong>Note:</strong> This information is for verification purposes only.
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 2rem;">
                        <p><strong>Need more detailed information?</strong></p>
                        <a href="https://wa.me/+923001234567" class="btn btn-secondary" style="margin-top: 10px;">
                            <i class="fab fa-whatsapp"></i> Contact for Premium Services
                        </a>
                    </div>
                `;
            }
            
            showError(message) {
                this.searchResults.style.display = 'block';
                this.resultsContent.innerHTML = `
                    <div class="error-message" style="text-align: center; padding: 3rem; color: #dc3545;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                        <h3 style="font-size: 1.5rem; margin-bottom: 1rem; color: #dc3545;">Search Error</h3>
                        <p style="margin-bottom: 1rem; color: #666;">${message}</p>
                        <button class="btn btn-primary" onclick="clearResults()">Try Again</button>
                    </div>
                `;
                this.searchResults.scrollIntoView({ behavior: 'smooth' });
            }
        }
        
        function clearResults() {
            const searchResults = document.getElementById('searchResults');
            const searchForm = document.getElementById('simSearchForm');
            
            if (searchResults) {
                searchResults.style.display = 'none';
            }
            
            if (searchForm) {
                searchForm.reset();
            }
        }
        
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            new SIMOwnerDetailsApp();

            // Check if there's a search parameter in URL
            const urlParams = new URLSearchParams(window.location.search);
            const number = urlParams.get('number');

            if (number) {
                // Auto-fill search input if it exists
                const searchInput = document.querySelector('input[type="text"]');
                if (searchInput) {
                    searchInput.value = '0' + number;
                }

                // Auto-trigger search
                setTimeout(() => {
                    performSearch(number);
                }, 500);
            }
        });

        function performSearch(number) {
            // Show results section
            const resultsSection = document.getElementById('searchResults');
            const resultsContent = document.getElementById('resultsContent');

            if (resultsSection && resultsContent) {
                resultsSection.style.display = 'block';

                // Simulate search results
                resultsContent.innerHTML = `
                    <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); margin-bottom: 20px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <h4 style="color: #2c5aa0; margin-bottom: 10px;"><i class="fas fa-phone"></i> Mobile Number</h4>
                                <p style="font-size: 1.1rem; font-weight: 600; color: #333;">+92 ${number}</p>
                            </div>

                            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <h4 style="color: #2c5aa0; margin-bottom: 10px;"><i class="fas fa-user"></i> Owner Name</h4>
                                <p style="font-size: 1.1rem; font-weight: 600; color: #333;">Muhammad Ahmad Khan</p>
                            </div>

                            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <h4 style="color: #2c5aa0; margin-bottom: 10px;"><i class="fas fa-id-card"></i> CNIC</h4>
                                <p style="font-size: 1.1rem; font-weight: 600; color: #333;">42101-1234567-1</p>
                            </div>

                            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <h4 style="color: #2c5aa0; margin-bottom: 10px;"><i class="fas fa-network-wired"></i> Network</h4>
                                <p style="font-size: 1.1rem; font-weight: 600; color: #333;">Jazz (Mobilink)</p>
                            </div>

                            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <h4 style="color: #2c5aa0; margin-bottom: 10px;"><i class="fas fa-map-marker-alt"></i> City</h4>
                                <p style="font-size: 1.1rem; font-weight: 600; color: #333;">Lahore, Punjab</p>
                            </div>

                            <div style="padding: 20px; background: #f8f9fa; border-radius: 8px;">
                                <h4 style="color: #2c5aa0; margin-bottom: 10px;"><i class="fas fa-calendar"></i> Registration</h4>
                                <p style="font-size: 1.1rem; font-weight: 600; color: #333;">Active Since 2020</p>
                            </div>
                        </div>

                        <div style="margin-top: 30px; padding: 20px; background: #e8f4fd; border-radius: 8px; border-left: 4px solid #2c5aa0;">
                            <h4 style="color: #2c5aa0; margin-bottom: 10px;"><i class="fas fa-info-circle"></i> Additional Information</h4>
                            <p style="color: #666; line-height: 1.6; margin: 0;">This SIM card is registered and active. For more detailed information including address details and call history, please contact our premium services.</p>
                        </div>
                    </div>
                `;

                // Scroll to results
                resultsSection.scrollIntoView({ behavior: 'smooth' });
            }
        }
    </script>
</body>
</html>
