// API Configuration
const API_BASE_URL = 'backend/api';
let currentUser = null;
let currentPage = 1;
let isLoading = false;

// DOM Elements
const productsGrid = document.getElementById('productsGrid');
const searchInput = document.getElementById('searchInput');
const locationSelect = document.getElementById('locationSelect');
const loadMoreBtn = document.querySelector('.load-more-btn');
const loginModal = document.getElementById('loginModal');
const registerModal = document.getElementById('registerModal');

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    checkUserSession();
    loadProducts();
    setupEventListeners();
});

// Check if user is logged in
async function checkUserSession() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth.php?action=check-session`);
        const data = await response.json();

        if (data.authenticated) {
            currentUser = data.user;
            updateUIForLoggedInUser();
        } else {
            updateUIForGuestUser();
        }
    } catch (error) {
        console.error('Session check failed:', error);
        updateUIForGuestUser();
    }
}

// Update UI for logged in user
function updateUIForLoggedInUser() {
    const loginLink = document.querySelector('#loginLink');
    const sellBtn = document.querySelector('.sell-btn');

    if (loginLink) {
        loginLink.innerHTML = '<i class="fas fa-user"></i> ' + currentUser.full_name;
        loginLink.href = '#';
        loginLink.onclick = () => showUserMenu();
    }

    if (sellBtn) {
        sellBtn.onclick = () => showSellForm();
    }
}

// Update UI for guest user
function updateUIForGuestUser() {
    const loginLink = document.querySelector('#loginLink');
    const sellBtn = document.querySelector('.sell-btn');

    if (loginLink) {
        loginLink.innerHTML = '<i class="fas fa-user"></i> Login';
        loginLink.onclick = (e) => {
            e.preventDefault();
            showModal('login');
        };
    }

    if (sellBtn) {
        sellBtn.onclick = (e) => {
            e.preventDefault();
            showModal('login');
        };
    }
}

// Load products from API
async function loadProducts(page = 1, filters = {}) {
    if (isLoading) return;

    isLoading = true;
    showLoadingSpinner();

    try {
        const queryParams = new URLSearchParams({
            page: page,
            limit: 12,
            ...filters
        });

        const response = await fetch(`${API_BASE_URL}/products.php?action=read&${queryParams}`);
        const data = await response.json();

        if (response.ok) {
            if (page === 1) {
                productsGrid.innerHTML = '';
            }

            data.products.forEach(product => {
                const productCard = createProductCard(product);
                productsGrid.appendChild(productCard);
            });

            updatePagination(data.pagination);
            currentPage = page;
        } else {
            showError(data.message || 'Failed to load products');
        }
    } catch (error) {
        console.error('Error loading products:', error);
        showError('Failed to load products');
    } finally {
        isLoading = false;
        hideLoadingSpinner();
    }
}

// Create product card element
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.onclick = () => openProductDetails(product.slug || product.id);

    const imageUrl = product.primary_image
        ? `backend/uploads/products/${product.primary_image}`
        : 'https://via.placeholder.com/280x200/f0f0f0/666?text=No+Image';

    const price = product.price ? `Rs ${Number(product.price).toLocaleString()}` : 'Price on request';
    const timeAgo = formatTimeAgo(product.created_at);

    card.innerHTML = `
        <div class="product-image">
            <img src="${imageUrl}" alt="${product.title}" onerror="this.src='https://via.placeholder.com/280x200/f0f0f0/666?text=No+Image'">
            ${product.is_featured ? '<div class="featured-badge">Featured</div>' : ''}
            ${product.is_sold ? '<div class="sold-badge">SOLD</div>' : ''}
        </div>
        <div class="product-info">
            <div class="product-price">${price}</div>
            <div class="product-title">${product.title}</div>
            <div class="product-location"><i class="fas fa-map-marker-alt"></i> ${product.location}</div>
            <div class="product-date">${timeAgo}</div>
            <div class="product-meta">
                <span class="category">${product.category_name || ''}</span>
                <span class="views"><i class="fas fa-eye"></i> ${product.views_count || 0}</span>
            </div>
        </div>
    `;

    return card;
}

// Format time ago
function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return `${Math.ceil(diffDays / 30)} months ago`;
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    locationSelect.addEventListener('change', handleSearch);
    
    // Load more button
    loadMoreBtn.addEventListener('click', loadMoreProducts);
    
    // Modal functionality
    setupModalEvents();
    
    // Category cards click events
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const categoryName = this.querySelector('h3').textContent;
            searchByCategory(categoryName);
        });
    });
    
    // Navigation events
    document.querySelector('.nav-link[href="#"]').addEventListener('click', function(e) {
        if (this.textContent.trim().includes('Login')) {
            e.preventDefault();
            showModal('login');
        }
    });
    
    document.querySelector('.sell-btn').addEventListener('click', function(e) {
        e.preventDefault();
        // Check if user is logged in (for now, just show alert)
        alert('Please login to post an ad. This feature will be implemented with WordPress integration.');
    });
}

// Handle search functionality
async function handleSearch() {
    const searchTerm = searchInput.value.trim();
    const selectedLocation = locationSelect.value;

    const filters = {};
    if (searchTerm) filters.search = searchTerm;
    if (selectedLocation) filters.location = selectedLocation;

    await loadProducts(1, filters);
}

// Advanced search with filters
async function searchWithFilters(filters) {
    await loadProducts(1, filters);
}

// Search by category
function searchByCategory(category) {
    searchInput.value = category;
    handleSearch();
    
    // Scroll to products section
    document.querySelector('.featured-products').scrollIntoView({
        behavior: 'smooth'
    });
}

// Load more products (simulate pagination)
function loadMoreProducts() {
    // In a real app, this would fetch more data from the server
    const moreProducts = [
        {
            id: 7,
            title: "Office Chair - Ergonomic",
            price: "Rs 25,000",
            location: "Lahore, Punjab",
            date: "5 days ago",
            image: "https://via.placeholder.com/280x200/f0f0f0/666?text=Office+Chair"
        },
        {
            id: 8,
            title: "Gaming Console PS5",
            price: "Rs 150,000",
            location: "Karachi, Sindh",
            date: "6 days ago",
            image: "https://via.placeholder.com/280x200/f0f0f0/666?text=PS5"
        }
    ];
    
    moreProducts.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
    
    // Hide load more button after loading (simulate end of data)
    loadMoreBtn.style.display = 'none';
}

// Modal functionality
function setupModalEvents() {
    // Close modal events
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });
    
    // Click outside modal to close
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });
    
    // Switch between login and register
    document.getElementById('showRegister').addEventListener('click', function(e) {
        e.preventDefault();
        loginModal.style.display = 'none';
        registerModal.style.display = 'block';
    });
    
    document.getElementById('showLogin').addEventListener('click', function(e) {
        e.preventDefault();
        registerModal.style.display = 'none';
        loginModal.style.display = 'block';
    });
    
    // Form submissions
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    document.getElementById('registerForm').addEventListener('submit', handleRegister);
}

// Show modal
function showModal(type) {
    if (type === 'login') {
        loginModal.style.display = 'block';
    } else if (type === 'register') {
        registerModal.style.display = 'block';
    }
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();
    const formData = new FormData(e.target);

    const loginData = {
        email: formData.get('log'),
        password: formData.get('pwd')
    };

    try {
        showLoadingSpinner();
        const response = await fetch(`${API_BASE_URL}/auth.php?action=login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(loginData)
        });

        const data = await response.json();

        if (response.ok) {
            currentUser = data.user;
            updateUIForLoggedInUser();
            loginModal.style.display = 'none';
            showSuccess('Login successful!');

            // Reload products to show user-specific content
            loadProducts();
        } else {
            showError(data.message || 'Login failed');
        }
    } catch (error) {
        console.error('Login error:', error);
        showError('Login failed. Please try again.');
    } finally {
        hideLoadingSpinner();
    }
}

// Handle registration
async function handleRegister(e) {
    e.preventDefault();
    const formData = new FormData(e.target);

    const password = formData.get('user_pass');
    const confirmPassword = formData.get('user_pass_confirm');

    if (password !== confirmPassword) {
        showError('Passwords do not match');
        return;
    }

    const registerData = {
        username: formData.get('user_login'),
        email: formData.get('user_email'),
        password: password,
        full_name: formData.get('user_login') // You can add a separate full name field
    };

    try {
        showLoadingSpinner();
        const response = await fetch(`${API_BASE_URL}/auth.php?action=register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(registerData)
        });

        const data = await response.json();

        if (response.ok) {
            registerModal.style.display = 'none';
            showSuccess('Registration successful! Please check your email for verification.');
            showModal('login');
        } else {
            showError(data.message || 'Registration failed');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showError('Registration failed. Please try again.');
    } finally {
        hideLoadingSpinner();
    }
}

// Open product details
function openProductDetails(identifier) {
    // Create product details page URL
    const detailsUrl = `product-details.html?${isNaN(identifier) ? 'slug' : 'id'}=${identifier}`;
    window.location.href = detailsUrl;
}

// Show loading spinner
function showLoadingSpinner() {
    const spinner = document.getElementById('loadingSpinner') || createLoadingSpinner();
    spinner.style.display = 'block';
}

// Hide loading spinner
function hideLoadingSpinner() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.display = 'none';
    }
}

// Create loading spinner
function createLoadingSpinner() {
    const spinner = document.createElement('div');
    spinner.id = 'loadingSpinner';
    spinner.innerHTML = `
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;">
            <div style="background: white; padding: 20px; border-radius: 8px; text-align: center;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #002f34;"></i>
                <p style="margin: 10px 0 0 0; color: #002f34;">Loading...</p>
            </div>
        </div>
    `;
    document.body.appendChild(spinner);
    return spinner;
}

// Show success message
function showSuccess(message) {
    showNotification(message, 'success');
}

// Show error message
function showError(message) {
    showNotification(message, 'error');
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 15px 20px;
        border-radius: 4px;
        z-index: 10000;
        max-width: 300px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: 10px;">&times;</button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Update pagination
function updatePagination(pagination) {
    const loadMoreBtn = document.querySelector('.load-more-btn');
    if (loadMoreBtn) {
        if (pagination.current_page < pagination.total_pages) {
            loadMoreBtn.style.display = 'block';
            loadMoreBtn.onclick = () => loadProducts(pagination.current_page + 1);
        } else {
            loadMoreBtn.style.display = 'none';
        }
    }
}

// Show user menu
function showUserMenu() {
    const menu = document.createElement('div');
    menu.className = 'user-menu';
    menu.style.cssText = `
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1000;
        min-width: 200px;
    `;

    menu.innerHTML = `
        <div style="padding: 10px;">
            <div style="font-weight: bold; margin-bottom: 10px;">${currentUser.full_name}</div>
            <a href="#" onclick="showMyProducts()" style="display: block; padding: 8px 0; text-decoration: none; color: #333;">My Products</a>
            <a href="#" onclick="showProfile()" style="display: block; padding: 8px 0; text-decoration: none; color: #333;">Profile Settings</a>
            <a href="#" onclick="showFavorites()" style="display: block; padding: 8px 0; text-decoration: none; color: #333;">Favorites</a>
            <hr style="margin: 10px 0;">
            <a href="#" onclick="logout()" style="display: block; padding: 8px 0; text-decoration: none; color: #f44336;">Logout</a>
        </div>
    `;

    // Remove existing menu
    const existingMenu = document.querySelector('.user-menu');
    if (existingMenu) {
        existingMenu.remove();
    }

    // Position menu relative to login link
    const loginLink = document.querySelector('#loginLink');
    loginLink.style.position = 'relative';
    loginLink.appendChild(menu);

    // Close menu when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function closeMenu(e) {
            if (!menu.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        });
    }, 100);
}

// Logout function
async function logout() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth.php?action=logout`, {
            method: 'POST'
        });

        if (response.ok) {
            currentUser = null;
            updateUIForGuestUser();
            showSuccess('Logged out successfully');
            loadProducts(); // Reload products
        }
    } catch (error) {
        console.error('Logout error:', error);
    }

    // Remove user menu
    const menu = document.querySelector('.user-menu');
    if (menu) {
        menu.remove();
    }
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-PK', {
        style: 'currency',
        currency: 'PKR',
        minimumFractionDigits: 0
    }).format(amount);
}

// Utility function to format date
function formatDate(date) {
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return `${Math.ceil(diffDays / 30)} months ago`;
}
