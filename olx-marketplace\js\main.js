// Sample product data
const sampleProducts = [
    {
        id: 1,
        title: "iPhone 14 Pro Max 256GB",
        price: "Rs 350,000",
        location: "Karachi, Sindh",
        date: "2 hours ago",
        image: "https://via.placeholder.com/280x200/f0f0f0/666?text=iPhone+14+Pro"
    },
    {
        id: 2,
        title: "Honda Civic 2020 Model",
        price: "Rs 4,500,000",
        location: "Lahore, Punjab",
        date: "5 hours ago",
        image: "https://via.placeholder.com/280x200/f0f0f0/666?text=Honda+Civic"
    },
    {
        id: 3,
        title: "2 Bedroom Apartment for Rent",
        price: "Rs 45,000/month",
        location: "Islamabad, ICT",
        date: "1 day ago",
        image: "https://via.placeholder.com/280x200/f0f0f0/666?text=Apartment"
    },
    {
        id: 4,
        title: "MacBook Pro M2 13-inch",
        price: "Rs 280,000",
        location: "Rawalpindi, Punjab",
        date: "2 days ago",
        image: "https://via.placeholder.com/280x200/f0f0f0/666?text=MacBook+Pro"
    },
    {
        id: 5,
        title: "Yamaha YBR 125 2022",
        price: "Rs 285,000",
        location: "Faisalabad, Punjab",
        date: "3 days ago",
        image: "https://via.placeholder.com/280x200/f0f0f0/666?text=Yamaha+YBR"
    },
    {
        id: 6,
        title: "Samsung 55-inch Smart TV",
        price: "Rs 120,000",
        location: "Karachi, Sindh",
        date: "4 days ago",
        image: "https://via.placeholder.com/280x200/f0f0f0/666?text=Samsung+TV"
    }
];

// DOM Elements
const productsGrid = document.getElementById('productsGrid');
const searchInput = document.getElementById('searchInput');
const locationSelect = document.getElementById('locationSelect');
const loadMoreBtn = document.querySelector('.load-more-btn');
const loginModal = document.getElementById('loginModal');
const registerModal = document.getElementById('registerModal');

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
    setupEventListeners();
});

// Load products into the grid
function loadProducts(products = sampleProducts) {
    productsGrid.innerHTML = '';
    
    products.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
}

// Create product card element
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.onclick = () => openProductDetails(product.id);
    
    card.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.title}" onerror="this.src='https://via.placeholder.com/280x200/f0f0f0/666?text=No+Image'">
        </div>
        <div class="product-info">
            <div class="product-price">${product.price}</div>
            <div class="product-title">${product.title}</div>
            <div class="product-location"><i class="fas fa-map-marker-alt"></i> ${product.location}</div>
            <div class="product-date">${product.date}</div>
        </div>
    `;
    
    return card;
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    locationSelect.addEventListener('change', handleSearch);
    
    // Load more button
    loadMoreBtn.addEventListener('click', loadMoreProducts);
    
    // Modal functionality
    setupModalEvents();
    
    // Category cards click events
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const categoryName = this.querySelector('h3').textContent;
            searchByCategory(categoryName);
        });
    });
    
    // Navigation events
    document.querySelector('.nav-link[href="#"]').addEventListener('click', function(e) {
        if (this.textContent.trim().includes('Login')) {
            e.preventDefault();
            showModal('login');
        }
    });
    
    document.querySelector('.sell-btn').addEventListener('click', function(e) {
        e.preventDefault();
        // Check if user is logged in (for now, just show alert)
        alert('Please login to post an ad. This feature will be implemented with WordPress integration.');
    });
}

// Handle search functionality
function handleSearch() {
    const searchTerm = searchInput.value.toLowerCase();
    const selectedLocation = locationSelect.value.toLowerCase();
    
    const filteredProducts = sampleProducts.filter(product => {
        const matchesSearch = product.title.toLowerCase().includes(searchTerm) ||
                            product.location.toLowerCase().includes(searchTerm);
        const matchesLocation = !selectedLocation || 
                              product.location.toLowerCase().includes(selectedLocation);
        
        return matchesSearch && matchesLocation;
    });
    
    loadProducts(filteredProducts);
    
    // Show no results message if needed
    if (filteredProducts.length === 0) {
        productsGrid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 40px; color: #666;"><i class="fas fa-search" style="font-size: 48px; margin-bottom: 20px; display: block;"></i><h3>No results found</h3><p>Try adjusting your search criteria</p></div>';
    }
}

// Search by category
function searchByCategory(category) {
    searchInput.value = category;
    handleSearch();
    
    // Scroll to products section
    document.querySelector('.featured-products').scrollIntoView({
        behavior: 'smooth'
    });
}

// Load more products (simulate pagination)
function loadMoreProducts() {
    // In a real app, this would fetch more data from the server
    const moreProducts = [
        {
            id: 7,
            title: "Office Chair - Ergonomic",
            price: "Rs 25,000",
            location: "Lahore, Punjab",
            date: "5 days ago",
            image: "https://via.placeholder.com/280x200/f0f0f0/666?text=Office+Chair"
        },
        {
            id: 8,
            title: "Gaming Console PS5",
            price: "Rs 150,000",
            location: "Karachi, Sindh",
            date: "6 days ago",
            image: "https://via.placeholder.com/280x200/f0f0f0/666?text=PS5"
        }
    ];
    
    moreProducts.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
    
    // Hide load more button after loading (simulate end of data)
    loadMoreBtn.style.display = 'none';
}

// Modal functionality
function setupModalEvents() {
    // Close modal events
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });
    
    // Click outside modal to close
    window.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });
    
    // Switch between login and register
    document.getElementById('showRegister').addEventListener('click', function(e) {
        e.preventDefault();
        loginModal.style.display = 'none';
        registerModal.style.display = 'block';
    });
    
    document.getElementById('showLogin').addEventListener('click', function(e) {
        e.preventDefault();
        registerModal.style.display = 'none';
        loginModal.style.display = 'block';
    });
    
    // Form submissions
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    document.getElementById('registerForm').addEventListener('submit', handleRegister);
}

// Show modal
function showModal(type) {
    if (type === 'login') {
        loginModal.style.display = 'block';
    } else if (type === 'register') {
        registerModal.style.display = 'block';
    }
}

// Handle login
function handleLogin(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    
    // In a real app, this would send data to WordPress backend
    alert('Login functionality will be implemented with WordPress integration.');
    loginModal.style.display = 'none';
}

// Handle registration
function handleRegister(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    
    // In a real app, this would send data to WordPress backend
    alert('Registration functionality will be implemented with WordPress integration.');
    registerModal.style.display = 'none';
}

// Open product details (placeholder)
function openProductDetails(productId) {
    // In a real app, this would navigate to a product details page
    alert(`Opening product details for ID: ${productId}. This will be implemented with WordPress integration.`);
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-PK', {
        style: 'currency',
        currency: 'PKR',
        minimumFractionDigits: 0
    }).format(amount);
}

// Utility function to format date
function formatDate(date) {
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return `${Math.ceil(diffDays / 30)} months ago`;
}
