<?php
// Test SSL API
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing SSL API</h2>";

// Test data
$testData = [
    'domain' => 'jobzhit.com',
    'email' => '<EMAIL>'
];

echo "<h3>Test Data:</h3>";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";

// Make API call
$url = 'http://localhost/ssl/api/generate-ssl.php';
$data = json_encode($testData);

$options = [
    'http' => [
        'header' => "Content-Type: application/json\r\n",
        'method' => 'POST',
        'content' => $data
    ]
];

$context = stream_context_create($options);

echo "<h3>Making API Call...</h3>";

try {
    $result = file_get_contents($url, false, $context);
    
    if ($result === FALSE) {
        echo "<p style='color: red;'>API call failed!</p>";
    } else {
        echo "<h3>API Response:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
        
        // Try to decode JSON
        $decoded = json_decode($result, true);
        if ($decoded) {
            echo json_encode($decoded, JSON_PRETTY_PRINT);
        } else {
            echo "Raw response:\n" . htmlspecialchars($result);
        }
        
        echo "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Exception: " . $e->getMessage() . "</p>";
}

echo "<h3>Direct File Test:</h3>";

// Test the file directly
try {
    $_POST = [];
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Simulate the input
    $input = json_encode($testData);
    
    echo "<p>Simulating API call with data: " . htmlspecialchars($input) . "</p>";
    
    // Include the API file
    ob_start();
    
    // Mock the input
    $GLOBALS['HTTP_RAW_POST_DATA'] = $input;
    
    include 'api/generate-ssl.php';
    
    $output = ob_get_clean();
    
    echo "<h4>Direct Output:</h4>";
    echo "<pre style='background: #f0f8ff; padding: 10px; border: 1px solid #ccc;'>";
    echo htmlspecialchars($output);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Direct test exception: " . $e->getMessage() . "</p>";
}
?>
