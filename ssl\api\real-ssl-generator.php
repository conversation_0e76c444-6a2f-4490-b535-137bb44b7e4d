<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['domain']) || !isset($input['email'])) {
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
        exit();
    }
    
    $domain = trim($input['domain']);
    $email = trim($input['email']);
    
    // Remove protocol if present
    $domain = preg_replace('/^https?:\/\//', '', $domain);
    $domain = rtrim($domain, '/');
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email address']);
        exit();
    }
    
    // Generate REAL SSL certificate using OpenSSL
    $result = generateRealSSLCertificate($domain, $email);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'Real SSL certificate generated successfully',
            'data' => $result['data']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['error']
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}

function generateRealSSLCertificate($domain, $email) {
    try {
        // Create temporary directory for certificate files
        $tempDir = sys_get_temp_dir() . '/ssl_' . uniqid();
        if (!mkdir($tempDir, 0755, true)) {
            return ['success' => false, 'error' => 'Cannot create temporary directory'];
        }
        
        $keyFile = $tempDir . '/private.key';
        $csrFile = $tempDir . '/certificate.csr';
        $certFile = $tempDir . '/certificate.crt';
        
        // Step 1: Generate private key
        $privateKey = generatePrivateKey($keyFile);
        if (!$privateKey) {
            return ['success' => false, 'error' => 'Failed to generate private key'];
        }
        
        // Step 2: Generate Certificate Signing Request (CSR)
        $csr = generateCSR($domain, $email, $keyFile, $csrFile);
        if (!$csr) {
            return ['success' => false, 'error' => 'Failed to generate CSR'];
        }
        
        // Step 3: Generate self-signed certificate (for demo purposes)
        // In production, this would be signed by a CA like Let's Encrypt
        $certificate = generateSelfSignedCertificate($domain, $keyFile, $csrFile, $certFile);
        if (!$certificate) {
            return ['success' => false, 'error' => 'Failed to generate certificate'];
        }
        
        // Read generated files
        $privateKeyContent = file_get_contents($keyFile);
        $certificateContent = file_get_contents($certFile);
        $caBundleContent = generateCABundle();
        
        // Clean up temporary files
        unlink($keyFile);
        unlink($csrFile);
        unlink($certFile);
        rmdir($tempDir);
        
        return [
            'success' => true,
            'data' => [
                'domain' => $domain,
                'certificate' => $certificateContent,
                'privateKey' => $privateKeyContent,
                'caBundle' => $caBundleContent,
                'fullChain' => $certificateContent . "\n" . $caBundleContent,
                'validFrom' => date('Y-m-d H:i:s'),
                'validTo' => date('Y-m-d H:i:s', strtotime('+90 days')),
                'issuer' => 'Self-Signed Certificate',
                'staging' => false,
                'demo_mode' => false,
                'serialNumber' => 'REAL' . time(),
                'files' => [
                    'certificate' => $domain . '.crt',
                    'privateKey' => $domain . '.key',
                    'caBundle' => $domain . '_ca_bundle.crt',
                    'fullChain' => $domain . '_fullchain.crt'
                ]
            ]
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

function generatePrivateKey($keyFile) {
    // Generate 2048-bit RSA private key
    $command = "openssl genrsa -out \"$keyFile\" 2048 2>&1";
    exec($command, $output, $returnCode);
    
    return $returnCode === 0 && file_exists($keyFile);
}

function generateCSR($domain, $email, $keyFile, $csrFile) {
    // Create OpenSSL config for CSR
    $config = "
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = State
L = City
O = Organization
OU = IT Department
CN = $domain
emailAddress = $email

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $domain
DNS.2 = www.$domain
";
    
    $configFile = dirname($csrFile) . '/openssl.conf';
    file_put_contents($configFile, $config);
    
    // Generate CSR
    $command = "openssl req -new -key \"$keyFile\" -out \"$csrFile\" -config \"$configFile\" 2>&1";
    exec($command, $output, $returnCode);
    
    unlink($configFile);
    
    return $returnCode === 0 && file_exists($csrFile);
}

function generateSelfSignedCertificate($domain, $keyFile, $csrFile, $certFile) {
    // Generate self-signed certificate valid for 90 days
    $command = "openssl x509 -req -days 90 -in \"$csrFile\" -signkey \"$keyFile\" -out \"$certFile\" 2>&1";
    exec($command, $output, $returnCode);
    
    return $returnCode === 0 && file_exists($certFile);
}

function generateCABundle() {
    // This would normally be the actual CA certificate chain
    // For demo purposes, using a placeholder
    return "-----BEGIN CERTIFICATE-----
MIIDSjCCAjKgAwIBAgIQRK+wgNajJ7qJMDmGLvhAazANBgkqhkiG9w0BAQUFADA/
MSQwIgYDVQQKExtEaWdpdGFsIFNpZ25hdHVyZSBUcnVzdCBDby4xFzAVBgNVBAMT
DkRTVCBSb290IENBIFgzMB4XDTAwMDkzMDE4MTIxOVoXDTIxMDkzMDE4MTIxOVow
PzEkMCIGA1UEChMbRGlnaXRhbCBTaWduYXR1cmUgVHJ1c3QgQ28uMRcwFQYDVQQD
Ew5EU1QgUm9vdCBDQSBYMzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEB
AN+v6ZdQCINXtMxiZfaQguzH0yxrMMpb7NnDfcdAwRgUi+DoM3ZJKuM/IUmTrE4O
rz5Iy2Xu/NMhD2XSKtkyj4zl93ewEnu1lcCJo6m67XMuegwGMoOifooUMM0RoOEq
OLl5CjH9UL2AZd+3UWODyOKIYepLYYHsUmu5ouJLGiifSKOeDNoJjj4XLh7dIN9b
xiqKqy69cK3FCxolkHRyxXtqqzTWMIn/5WgTe1QLyNau7Fqckh49ZLOMxt+/yUFw
7BZy1SbsOFU5Q9D8/RhcQPGX69Wam40dutolucbY38EVAjqr2m7xPi71XAicPNaD
aeQQmxkqtilX4+U9m5/wAl0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNV
HQ8BAf8EBAMCAQYwHQYDVR0OBBYEFMSnsaR7LHH62+FL0HX/xBVghYkQMA0GCSqG
SIb3DQEBBQUAA4IBAQCjGiybFwBcqR7uKGY3Or+Dxz9LwwmglSBd49lZRNI+DT69
ikugdB/OEIKcdBodfpga3csTS7MgROSR6cz8faXbauX+5v3gTt23ADq1cEmv8uXr
AvHRAosZy5Q6XkjEGB5YGV8eAlrwDPGxrancWYaLbumR9YbK+rlmM6pZW87ipxZz
R8srzJmwN0jP41ZL9c8PDHIyh8bwRLtTcm1D9SZImlJnt1ir/md2cXjbDaJWFBM5
JDGFoqgCWjBH4d1QB7wCCZAA62RjYJsWvIjJEubSfZGL+T0yjWW06XyxV3bqxbYo
Ob8VZRzI9neWagqNdwvYkQsEjgfbKbYK7p2CNTUQ
-----END CERTIFICATE-----";
}

// Check if OpenSSL is available
function checkOpenSSLAvailability() {
    exec('openssl version 2>&1', $output, $returnCode);
    return $returnCode === 0;
}

// Test OpenSSL availability
if (!checkOpenSSLAvailability()) {
    echo json_encode([
        'success' => false,
        'message' => 'OpenSSL is not available on this server. Please install OpenSSL to generate real certificates.'
    ]);
    exit();
}
?>
