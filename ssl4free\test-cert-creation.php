<?php
require_once 'api/config.php';

echo "<h1>Direct Certificate Creation Test</h1>";

$testDomain = 'test.jobzhit.com';
$testEmail = '<EMAIL>';

echo "<p><strong>Testing Domain:</strong> $testDomain</p>";
echo "<p><strong>Testing Email:</strong> $testEmail</p>";

// Test direct API call to create-certificate.php
echo "<h2>Testing create-certificate.php directly</h2>";

$postData = json_encode([
    'domain' => $testDomain,
    'email' => $testEmail
]);

echo "<p><strong>Request Data:</strong></p>";
echo "<pre>" . htmlspecialchars($postData) . "</pre>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/ssl4free/api/create-certificate.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h3>Response:</h3>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";

if ($error) {
    echo "<p style='color: red;'><strong>cURL Error:</strong> $error</p>";
} else {
    echo "<p style='color: green;'><strong>cURL Success</strong></p>";
}

echo "<p><strong>Response Body:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

$responseData = json_decode($response, true);
if ($responseData) {
    echo "<h3>Parsed Response:</h3>";
    echo "<pre>" . print_r($responseData, true) . "</pre>";
    
    if (isset($responseData['success']) && $responseData['success']) {
        echo "<p style='color: green; font-weight: bold;'>🎉 SUCCESS! Certificate creation worked!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ FAILED: " . ($responseData['message'] ?? 'Unknown error') . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Invalid JSON response</p>";
}

// Test direct ZeroSSL API call
echo "<h2>Testing ZeroSSL API directly</h2>";

// Generate a simple CSR first
$config = [
    'private_key_bits' => 2048,
    'private_key_type' => OPENSSL_KEYTYPE_RSA,
];

$privateKey = @openssl_pkey_new($config);
if ($privateKey) {
    $dn = [
        'C' => 'US',
        'ST' => 'CA',
        'L' => 'SF',
        'O' => 'SSL4Free',
        'CN' => $testDomain
    ];
    
    $csr = @openssl_csr_new($dn, $privateKey, $config);
    if ($csr && @openssl_csr_export($csr, $csrString)) {
        echo "<p style='color: green;'>✅ CSR generated successfully</p>";
        
        // Test direct ZeroSSL API call
        $requestData = [
            'certificate_domains' => $testDomain,
            'certificate_validity_days' => 90,
            'certificate_csr' => $csrString,
            'certificate_validation_method' => 'HTTP_CSR_HASH'
        ];
        
        echo "<p><strong>ZeroSSL Request Data:</strong></p>";
        echo "<pre>" . htmlspecialchars(json_encode($requestData, JSON_PRETTY_PRINT)) . "</pre>";
        
        $response = zeroSSLRequest('/certificates', 'POST', $requestData);
        
        echo "<p><strong>ZeroSSL Response:</strong></p>";
        echo "<pre>" . htmlspecialchars(json_encode($response, JSON_PRETTY_PRINT)) . "</pre>";
        
        if ($response && isset($response['id'])) {
            echo "<p style='color: green; font-weight: bold;'>🎉 ZeroSSL API SUCCESS! Certificate ID: " . $response['id'] . "</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ ZeroSSL API FAILED</p>";
            if (isset($response['error'])) {
                echo "<p style='color: red;'>Error: " . json_encode($response['error']) . "</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ CSR generation failed</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Private key generation failed</p>";
}

echo "<p><a href='debug-full.php'>← Back to Debug</a></p>";
?>
