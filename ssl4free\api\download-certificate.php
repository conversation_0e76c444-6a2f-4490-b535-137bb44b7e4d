<?php
require_once 'config.php';

setApiHeaders();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo apiResponse(false, 'Method not allowed');
    exit();
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo apiResponse(false, 'Invalid JSON input');
        exit();
    }
    
    // Validate required fields
    if (!isset($input['certificate_id'])) {
        echo apiResponse(false, 'Missing required field: certificate_id');
        exit();
    }
    
    $certificateId = trim($input['certificate_id']);
    $includeCrossSigned = isset($input['include_cross_signed']) ? $input['include_cross_signed'] : false;
    
    logMessage("Downloading certificate for ID: $certificateId", 'INFO');
    
    // First check if certificate is issued
    $statusResponse = zeroSSLRequest("/certificates/$certificateId");
    
    if (!$statusResponse) {
        echo apiResponse(false, 'Failed to retrieve certificate information');
        exit();
    }
    
    if ($statusResponse['status'] !== 'issued') {
        echo apiResponse(false, 'Certificate is not yet issued. Current status: ' . $statusResponse['status']);
        exit();
    }
    
    // Download certificate files using inline method
    $downloadEndpoint = "/certificates/$certificateId/download/return";
    if ($includeCrossSigned) {
        $downloadEndpoint .= "?include_cross_signed=1";
    }
    
    $certificateFiles = zeroSSLRequest($downloadEndpoint);
    
    if (!$certificateFiles) {
        echo apiResponse(false, 'Failed to download certificate files from ZeroSSL API');
        exit();
    }
    
    // Validate that we have the required files
    if (!isset($certificateFiles['certificate.crt']) || !isset($certificateFiles['ca_bundle.crt'])) {
        echo apiResponse(false, 'Incomplete certificate files received from ZeroSSL');
        exit();
    }
    
    // Get private key from temporary storage
    $domain = $statusResponse['common_name'];
    $privateKey = getStoredPrivateKey($domain);
    
    if (!$privateKey) {
        logMessage("Private key not found for domain: $domain", 'WARNING');
        $privateKey = "# Private key not available\n# Please use the private key you generated during CSR creation";
    }
    
    // Prepare certificate data
    $certificates = [
        'certificate' => $certificateFiles['certificate.crt'],
        'private_key' => $privateKey,
        'ca_bundle' => $certificateFiles['ca_bundle.crt'],
        'full_chain' => $certificateFiles['certificate.crt'] . "\n" . $certificateFiles['ca_bundle.crt']
    ];
    
    // Save certificates to temporary files for download
    $tempDir = TEMP_DIR . 'download_' . md5($certificateId) . '/';
    if (!file_exists($tempDir)) {
        mkdir($tempDir, 0755, true);
    }
    
    $files = [
        'certificate.crt' => $certificates['certificate'],
        'private.key' => $certificates['private_key'],
        'ca_bundle.crt' => $certificates['ca_bundle'],
        'fullchain.crt' => $certificates['full_chain']
    ];
    
    $downloadUrls = [];
    foreach ($files as $filename => $content) {
        $filepath = $tempDir . $filename;
        file_put_contents($filepath, $content);
        $downloadUrls[$filename] = 'api/download-file.php?file=' . urlencode($filepath) . '&name=' . urlencode($filename);
    }
    
    // Update certificate data in storage
    $tempData = loadCertificateData($domain);
    if ($tempData) {
        $tempData['certificates'] = $certificates;
        $tempData['download_urls'] = $downloadUrls;
        $tempData['download_time'] = time();
        $tempData['validation_step'] = 'completed';
        saveCertificateData($domain, $tempData);
    }
    
    logMessage("Certificate downloaded successfully for ID: $certificateId", 'INFO');
    
    echo apiResponse(true, 'Certificate downloaded successfully', [
        'certificate_id' => $certificateId,
        'domain' => $domain,
        'certificates' => $certificates,
        'download_urls' => $downloadUrls,
        'certificate_info' => [
            'common_name' => $statusResponse['common_name'],
            'additional_domains' => $statusResponse['additional_domains'],
            'created' => $statusResponse['created'],
            'expires' => $statusResponse['expires'],
            'status' => $statusResponse['status']
        ]
    ]);
    
} catch (Exception $e) {
    logMessage("Error downloading certificate: " . $e->getMessage(), 'ERROR');
    echo apiResponse(false, 'Internal server error: ' . $e->getMessage());
}

function getStoredPrivateKey($domain) {
    $keyFile = TEMP_DIR . 'key_' . md5($domain) . '.pem';
    
    if (file_exists($keyFile)) {
        $privateKey = file_get_contents($keyFile);
        // Don't delete the key file yet, user might need to download again
        return $privateKey;
    }
    
    return null;
}
?>
